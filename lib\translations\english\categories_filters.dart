const Map<String, String> categoriesFiltersEnglish = {
  // <PERSON> Categories
  'wedding': "Wedding",
  'funeral/burial': "Funeral/Burial",
  'birthday': "Birthday",
  'charity': "Charity",
  'church/Religious': "Church/Religious",
  'community_project': "Community Project",
  'graduation': "Graduation",
  'holiday/festival': "Holiday/Festivals",
  'group_investment/Chama': "Group Investment/Chama",
  'debt_repayment': "Debt Repayment",
  'emergency_fund': "Emergency Fund",
  'savings': "Savings",
  'investment': "Investment",

  // Stepper indicator translations
  'event': 'Event',
  'time_location': 'Time & Location',
  'tickets': 'Tickets',
  'socials': 'Socials',

  // Categories and filters
  'categories': 'Categories:',
  'all_categories': 'All Categories',
  'unknown_category': 'Unknown Category',
  'all_statuses': 'All Statuses',
  'start_date': 'Start Date',
  'include_end_date': 'Include End Date',
  'click_select_end_date': 'click to select end Date',
  'select_end_date': 'Select End Date',
  'expected_contribution_end_date': 'Expected contribution end date',
  'expected_contribution_end_date_label': 'Expected contribution end date',
  'expected_contribution_end_date_info': 'Expected contribution end date',
  'pick_end_date': 'Pick an end date',
  'set_deadline': 'Set a Deadline For Your',
  'contribution': 'Contribution',

  // Search and filter functionality
  'search_for_kitty': 'Search for a Kitty',
  'filter_kitties': 'Filter Kitties',
  'refine_search_results': 'Refine your search results',
  'search_by_name_or_id': 'Search by Name or ID',
  'enter_kitty_name_or_id': 'Enter kitty name or ID',
  'search_by_name': 'Search by name',
  'search_contacts': 'Search contacts...',
  'search_beneficiaries': 'Search beneficiaries',
  'filter_members': 'Filter Members',
  'search_by_first_name': 'Search by first name',
  'search_by_last_name': 'Search by last name',
  'search_by_phone': 'Search by phone',
  'no_members_found_matching_filter': 'No members found matching your filter',

  // No data states
  'no_categories_found': 'No categories found',
  'no_kitties_found_for': 'No kitties found for',
  'no_beneficiaries_found': 'No beneficiaries found',
  'no_settings_found': 'No Settings Found',
  'settings_created_when_submit': 'Settings will be created when you submit',
  'no_meetings_added': 'No meetings added yet',
  'no_meetings_added_yet': 'No Meetings added Yet',
  'no_penalties_added': 'No Penalties added yet',
  'no_penalties_added_yet': 'No Penalties added Yet',
  'no_penalties_here_yet': 'No penalties here yet',
  'you_dont_have_penalties': 'You don\'t have any penalties yet',
  'no_general_penalties': 'No general penalties set, kindly add one.',
  'no_signatories_added': 'No Signatories added to this chama',
  'no_documents_added': 'No Documents Added Yet',
  'no_members_added_yet': 'No members added yet',
  'no_whatsapp_groups_connected_event': 'No WhatsApp groups connected to this event',
  'no_whatsapp_groups_connected': 'No WhatsApp Groups Connected',
  'no_whatsapp_groups': "You don't have any whatsapp groups",
  'no_whatsapp_groups_message': 'You don\'t have any whatsapp groups yet, click the \'Add Group\' button to add.',
  'no_groups_connected': 'No groups connected',
  'groups_connected': '{count} group{plural} connected',
  'no_referrals_found': 'No Referrals found',
  'kitty_not_found': 'Kitty not found',
  'chama_not_found': 'Chama not found',
  'no_referral_code_message': 'It seems you do not have a referral code.\n\nIf you\'re interested in becoming an affiliate, register and join our program to start earning rewards.',
  'you_have_no_earnings_yet': 'You have no earnings yet here',
  'you_dont_have_any_kitties': 'You dont have any kitties',
  'no_transactions_yet': 'You have no transactions yet',
  'no_messages_yet': 'You Do not Have any Messages Yet, ',
  'try_it_now': 'Try it Now',

  // Frequency and time-related
  'how_often_contribute': 'How Often Do Members Contribute',
  'select_frequency': 'Select Frequency',
  'meeting_frequency_tooltip': 'This is how often the meeting shall be held.',
  'often_tooltip': 'Number of times members contribute within a period',
  'amount_tooltip': 'Amount each member should contribute',
  'deadline_tooltip': 'End date of contribution',

  // Payment channels and methods
  'mobile_money': 'Mobile Money',
  'paybill': 'Paybill',
  'till_buy_goods': 'Till(Buy Goods)',
  'bank': 'Bank',
  'mpesa_paybill': 'Mpesa Paybill',
  'mpesa_till_number': 'Mpesa Till number',
  'bank_account': 'Bank Account',
  'select_bank': 'Select Bank',
  'm_pesa': 'M-PESA',
  'mpesa_till': 'M-PESA Till',
  'sasapay': 'SasaPay',
  'airtel_money': 'AirtelMoney',
  'visa': 'Card',
  'bank_transfer': 'Bank Transfer',

  // Notification types
  'sms': 'SMS',
  'whatsapp': 'WHATSAPP',
  'notification_sms': 'SMS',
  'notification_whatsapp': 'WHATSAPP',
  'notification_email': 'EMAIL',
  'notification_all': 'ALL',
  'select_notification_type': 'Select a notification type',
  'notification_type': 'Notification Type',

  // Transaction and contribution types
  'contribution_type': 'CONTRIBUTION',
  'penalty_type': 'PENALTY',
  'volumes': 'Volumes',
  'financial': 'Financial',

  // Roles and permissions
  'admin': 'ADMIN',
  'treasurer': 'TREASURER',
  'secretary': 'SECRETARY',
  'member': 'MEMBER',
  'chairperson': 'CHAIRPERSON',
  'signatory': 'Signatory',
  'select_role': 'Select Role',
  'role': 'Role',
  'is_signatory': 'Is Signatory',
  'update_role': 'Update Role',

  // Split configuration
  'split_config': 'Split config:',
  'percentage': 'Percentage',
  'split_config_tooltip': 'Split config determines how the funds will be divided. You can choose to split by Percentage or by a fixed Amount.',

  // Advanced settings categories
  'minimum_amount': 'Minimum Amount',
  'minimum_amount_tooltip': 'Minimum amount a person can contribute',
  'maximum_amount': 'Maximum Amount',
  'maximum_amount_tooltip': 'Maximum amount a person can contribute',
  'hide_amount': 'Hide Amount',
  'hide_amount_tooltip': 'Hide individual contribution amounts in the Contribution List',
  'hide_names': 'Hide Names',
  'hide_individual_names_contribution_list': 'Hide Individual Names in the contribution List',
  'show_names': 'Show Names',
  'display_contributor_names': 'Display contributor names in transaction lists',
  'group_by_account': 'Group by Account? ',
  'group_by_account_tooltip': 'Group the transactions in the contribution list by the phone numbers',
  'group_transactions_contribution_list_phone_numbers': 'Group the transactions in the contribution list by the phone numbers',
  'history_start_date': 'History Start Date',
  'history_start_date_tooltip': 'Show the contribution list from the specified Date',
  'history_limit': 'History Limit',
  'history_limit_tooltip': 'Limit the length of the contribution List, eg Limit of 20 will show the latest 20 transactions',
  'has_membership': 'Has membership',
  'has_membership_tooltip': 'Enable this if contributors need unique identifier for contributions, such as ID number, member number, or employee ID. This helps track and identify individual contributions.',
  'membership_id_label': 'Membership ID Label',
  'membership_id_example': 'e.g. Member Number, Employee ID, House No.',
  'membership_id_tooltip': 'Label that will be shown when requesting membership ID from contributors',

  // Custom messages and tooltips
  'custom_thank_you_message': 'Custom Thank You Message',
  'custom_message_tooltip': 'A personalized message displayed to users as a token of appreciation whenever they make a contribution. This feature ensures every contributor feels valued and recognized.',
  'hide_names_tooltip': 'By checking this option, your full names\nwill not be displayed in the\nkitty notification message',
  'hide_number_tooltip': 'By checking this option, your Number\nwill not be displayed in the kitty\nnotification message',
  'end_date_info_tooltip': 'When this Date and Time has reached,\nthe kitty will be marked as CLOSED\nand the funds will be sent to the Beneficiary account',
  'limit_text_tooltip': 'This is the maximum amount this kitty should hold',
  'benf_channel_tooltip': 'This is the channel where the beneficiary will receive the money.\nThe account should be registered on the selected Platform',
  'benf_acc_tooltip': 'After all the collection has been done Or when the Kitty closes on the end date, all the funds will be sent to this Number',
  'whatsapp_link_tooltip': 'One-kitty bot will join the Group inorder to notify the members timely on the contribution status of the kitty.\nRemember to provide a valid link.',
  'whatsapp_name_join_tooltip': 'You can also add this number to your Whatsapp Group.\nThen paste the group name below',
  'whatsapp_join_message_tooltip': 'Send a message containing the link and kitty link to our online assistant. Onekitty will automatically join the group.',
  'receive_channel_pay_req_tooltip': 'This is the channel where you will receive the payments.\nThe account should be registered on the selected Platform',
  'kitty_description_tooltip': 'This is the general description of your Kitty',
  'phone_tooltip': 'This is the phone Number of the Kitty owner',
  'kitty_name_tooltip': 'This is the name of your kitty.',
  'benef_per_cycle_tooltip': 'These are the number of people receiving funds in a single merry go round',
  'sig_threshold_tooltip': 'These are the number of signatories that are required to sign a transaction for it to be verified. This number can\'t be more than the number of set signatories.',
  'benef_percentage_tooltip': 'This is the percentage of what the beneficiaries will receive after a contribution. In the sense 50% might go to the beneficiaries set and 50% will remain in the chama wallet',
  'location_tip_tooltip': 'Give a tip of a nearby location to help members navigate easily.',
  'general_penalty_tooltip': 'These are penalties pre-set by the system to make your work easier. You can edit their details to your liking. These are penalties that are commonly found in most chamas',
  'payer_phone_tooltip': 'This is the number you can use to do transactions. Does not have to be part of the chama',
  'chama_member_phone_tooltip': 'This is the phone number associated with the chama member',
  'payment_reference_optional_tooltip': 'Optional field for employee ID or payment reference number',
};