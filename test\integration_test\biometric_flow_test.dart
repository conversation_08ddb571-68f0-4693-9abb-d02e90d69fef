import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:onekitty/main.dart' as app;
import 'package:onekitty/screens/onboarding/login_screen.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/dashboard/pages/profile/profile_page.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Biometric Authentication Integration Tests', () {
    testWidgets('Complete biometric enrollment and authentication flow', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Mock biometric availability
      const MethodChannel channel = MethodChannel('plugins.flutter.io/local_auth');
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'canCheckBiometrics':
            return true;
          case 'getAvailableBiometrics':
            return ['fingerprint', 'face'];
          case 'authenticate':
            return true; // Simulate successful authentication
          default:
            return null;
        }
      });

      // Test 1: Navigate to login screen and check biometric capability detection
      await tester.pumpAndSettle(const Duration(seconds: 2));
      
      // Look for login screen elements
      expect(find.byType(LoginScreen), findsOneWidget);
      
      // Test 2: Enter credentials and enable biometric login
      final phoneField = find.byType(TextFormField).first;
      await tester.enterText(phoneField, '+254700000000');
      await tester.pumpAndSettle();

      final passwordField = find.byType(TextFormField).last;
      await tester.enterText(passwordField, 'testpassword123');
      await tester.pumpAndSettle();

      // Tap login button
      final loginButton = find.text('Log In');
      await tester.tap(loginButton);
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test 3: Navigate to profile and enable biometric login
      // This would require successful login first, so we'll simulate navigation
      await tester.pumpWidget(MaterialApp(home: ProfilePage()));
      await tester.pumpAndSettle();

      // Find and toggle biometric switch
      final biometricSwitch = find.byType(SwitchListTile);
      expect(biometricSwitch, findsOneWidget);
      
      await tester.tap(biometricSwitch);
      await tester.pumpAndSettle();

      // Test 4: Test biometric authentication on password screen
      await tester.pumpWidget(MaterialApp(home: PasswdReqScreen()));
      await tester.pumpAndSettle();

      // Look for fingerprint icon
      final fingerprintIcon = find.byIcon(Icons.fingerprint);
      expect(fingerprintIcon, findsOneWidget);

      // Tap fingerprint icon to trigger biometric authentication
      await tester.tap(fingerprintIcon);
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify authentication was attempted
      // In a real test, this would check for navigation or success indicators
    });

    testWidgets('Handle biometric authentication failures gracefully', (WidgetTester tester) async {
      // Mock biometric failure scenarios
      const MethodChannel channel = MethodChannel('plugins.flutter.io/local_auth');
      
      // Test scenario 1: No biometrics available
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'canCheckBiometrics':
            return false;
          case 'getAvailableBiometrics':
            return [];
          default:
            return null;
        }
      });

      await tester.pumpWidget(MaterialApp(home: PasswdReqScreen()));
      await tester.pumpAndSettle();

      // Fingerprint icon should not be visible when biometrics unavailable
      final fingerprintIcon = find.byIcon(Icons.fingerprint);
      expect(fingerprintIcon, findsNothing);

      // Test scenario 2: Authentication failure
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'canCheckBiometrics':
            return true;
          case 'getAvailableBiometrics':
            return ['fingerprint'];
          case 'authenticate':
            throw PlatformException(
              code: 'NotAvailable',
              message: 'Biometric authentication not available',
            );
          default:
            return null;
        }
      });

      await tester.pumpWidget(MaterialApp(home: PasswdReqScreen()));
      await tester.pumpAndSettle();

      // Fingerprint icon should be visible
      final fingerprintIcon2 = find.byIcon(Icons.fingerprint);
      expect(fingerprintIcon2, findsOneWidget);

      // Tap to trigger authentication failure
      await tester.tap(fingerprintIcon2);
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Should show error message (would need to check for specific error UI)
    });

    testWidgets('Verify encryption/decryption of stored credentials', (WidgetTester tester) async {
      // This test would verify that the encryption/decryption process works correctly
      // when biometric authentication is successful
      
      const MethodChannel channel = MethodChannel('plugins.flutter.io/local_auth');
      tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'canCheckBiometrics':
            return true;
          case 'getAvailableBiometrics':
            return ['fingerprint'];
          case 'authenticate':
            return true;
          default:
            return null;
        }
      });

      // Test the login screen with biometric enrollment
      await tester.pumpWidget(MaterialApp(home: LoginScreen()));
      await tester.pumpAndSettle();

      // Enter test credentials
      final phoneField = find.byType(TextFormField).first;
      await tester.enterText(phoneField, '+254700000000');
      
      final passwordField = find.byType(TextFormField).last;
      await tester.enterText(passwordField, 'testpassword123');
      await tester.pumpAndSettle();

      // This would test the enrollBiometrics function
      // In a real scenario, we'd verify the encrypted data is stored correctly
    });
  });
}
