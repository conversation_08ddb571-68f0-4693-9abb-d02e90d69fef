import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/kitty/media_model.dart';
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:onekitty/models/transaction_edit_models.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/services/user_ktty_services.dart';

/// Controller for managing User Kitty UI state and orchestrating service calls
class UserKittyController extends GetxController {
  final UserKittyService _userKittyService = Get.isRegistered() ? Get.find() : Get.put(UserKittyService());
  final logger = Get.find<Logger>();

  // User data
  final RxBool kittiesLoading = false.obs;
  final Rx<UserModelLatest> user = UserModelLatest().obs;
  final RxString apiMessage = ''.obs;
  final RxBool status = false.obs;
  final Rx<UserModelLatest> usermodel = UserModelLatest().obs;
  final Rx<MerchantModel> usermerchant = MerchantModel().obs;

  // Kitties data
  final RxList<UserKitty> kitties = <UserKitty>[].obs;
  final RxList<Kitty> kittyList = <Kitty>[].obs;
  final RxList<MediaModel> media = <MediaModel>[].obs;

  // Top up
  final RxBool topUploading = false.obs;
  final RxMap topUpData = {}.obs;
  final RxString apiMessageTopup = ''.obs;

  // Transactions
  final loadingTransactions = false.obs;
  final alltransactions = <TransactionModel>[].obs;
  final results = UserTransactionModel().obs;
  final loadingfiltrTransactions = false.obs;
  final filtrtransactions = <TransactionModel>[].obs;
  final isloadingUser = false.obs;

  // Refer kitties
  RxList<Kitty> referkitties = <Kitty>[].obs;
  RxInt totalRefers = 0.obs;
  RxString kittyType = ''.obs;
  Rx<transData> resultsDts = transData().obs;
  RxList<TransactionModel> merchtransactions = <TransactionModel>[].obs;

  RxBool loading = false.obs;

  // Pagination
  final scrollController = ScrollController();
  RxBool loadingMore = false.obs;
  RxInt currentPage = 0.obs;
  RxInt pageSize = 10.obs;
  RxInt totalKitties = 0.obs;
  RxBool hasMoreData = true.obs;

  // Search and filters
  RxBool searchLoading = false.obs;
  RxString searchQuery = ''.obs;
  final RxMap<String, String> currentFilters = <String, String>{}.obs;
  final RxString currentSort = '-id'.obs;

  @override
  void onInit() {
    super.onInit();
    getLocalUser();
    _setupScrollListener();
  }

  /// Setup scroll listener for pagination
  void _setupScrollListener() {
    scrollController.addListener(() {
      if (!scrollController.hasClients) return;

      // Additional safety check for position
      if (!scrollController.position.hasContentDimensions) return;

      // Load more when 80% scrolled
      final maxScrollExtent = scrollController.position.maxScrollExtent;

      // Ensure maxScrollExtent is valid (greater than 0)
      if (maxScrollExtent <= 0) return;

      final threshold = maxScrollExtent * 0.8;
      if (scrollController.position.pixels >= threshold &&
          !loadingMore.value &&
          hasMoreData.value &&
          !kittiesLoading.value) {
        loadMoreKitties();
      }
    });
  }

  /// Reset pagination state
  void resetPagination() {
    currentPage.value = 0;
    hasMoreData.value = true;
    totalKitties.value = 0;
    kitties.clear();
  }

  /// Get local user data
  UserModelLatest? getLocalUser() {
    final localUser = _userKittyService.getLocalUser();
    if (localUser != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        user(localUser);
      });
      return localUser;
    }
    return null;
  }

  /// Find kitty by ID
  UserKitty? findKittyById(int id) {
    return kitties.firstWhere(
      (kitty) => kitty.kitty?.iD == id,
    );
  }

  /// Get user details from API
  Future<void> getUser() async {
    isloadingUser(true);
    
    try {
      final resp = await _userKittyService.getUser(user.value.phoneNumber ?? '');

      if (resp["status"]) {
        final u = resp["data"]["user"];
        usermodel(UserModelLatest.fromJson(u));
        logger.log(Level.debug, resp);
        
        if (resp["data"]["merchant"] != null) {
          final m = resp["data"]["merchant"];
          usermerchant(MerchantModel.fromJson(m));
        }
        
        _userKittyService.saveLocalUser(u);
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      rethrow;
    } finally {
      isloadingUser(false);
      update();
    }
  }

  /// Main method to fetch kitties with pagination
  Future<void> fetchKitties({
    int? page,
    Map<String, String>? filters,
    String? search,
    bool isRefresh = false,
  }) async {
    // Determine if this is first page load
    final isFirstPage = (page ?? currentPage.value) == 0;
    
    if (isFirstPage) {
      kittiesLoading(true);
      if (isRefresh) resetPagination();
    } else {
      loadingMore(true);
    }

    try {
      final effectivePage = page ?? currentPage.value;
      final effectiveFilters = filters ?? currentFilters;
      final effectiveSearch = search ?? searchQuery.value;

      // Build request filters
      final requestFilters = Map<String, String>.from(effectiveFilters);
      requestFilters['sort'] = currentSort.value;

      logger.i("Fetching kitties: page=$effectivePage, filters=$requestFilters, search=$effectiveSearch");

      final resp = await _userKittyService.getUserKitties(
        phoneNumber: user.value.phoneNumber ?? '',
        page: effectivePage,
        size: pageSize.value,
        filters: requestFilters.isNotEmpty ? requestFilters : null,
        search: effectiveSearch.isNotEmpty ? effectiveSearch : null,
      );

      if (resp["status"]) {
        _handleSuccessResponse(resp["data"], isFirstPage, effectivePage);
      } else {
        logger.w("API returned status false: ${resp["message"]}");
        apiMessage(resp["message"]);
      }
    } catch (e) {
      logger.e("Error fetching kitties: $e");
      apiMessage('An error occurred while fetching kitties');
    } finally {
      kittiesLoading(false);
      loadingMore(false);
    }
  }

  /// Handle successful API response
  void _handleSuccessResponse(Map<String, dynamic> data, bool isFirstPage, int requestedPage) {
    // Clear list on first page
    if (isFirstPage) {
      kitties.clear();
      media.clear();
    }

    // Parse pagination info
    final pageInfo = data["page"] ?? {};
    totalKitties.value = pageInfo["total"] ?? 0;
    currentPage.value = pageInfo["page"] ?? requestedPage;
    final totalPages = pageInfo["total_pages"] ?? 1;
    final isLastPage = pageInfo["last"] ?? false;

    // Update hasMoreData
    hasMoreData.value = !isLastPage && currentPage.value < totalPages - 1;

    // Parse kitties
    final kittiesData = (data["user_kitties"] ?? []) as List<dynamic>;
    if (kittiesData.isNotEmpty) {
      final newKitties = kittiesData
          .map((element) => UserKitty.fromJson(element))
          .toList();
      kitties.addAll(newKitties);
      logger.i("Added ${newKitties.length} kitties. Total: ${kitties.length}/${totalKitties.value}");
    } else {
      hasMoreData.value = false;
      logger.i("No kitties returned for page $requestedPage");
    }

    // Parse media
    if (data["media"] != null) {
      for (var element in data["media"]) {
        media.add(MediaModel.fromJson(element));
      }
    }
  }

  /// Load initial kitties
  Future<void> loadKitties() async {
    await fetchKitties(page: 0, isRefresh: true);
  }

  /// Load more kitties (for infinite scroll)
  Future<void> loadMoreKitties() async {
    if (loadingMore.value || !hasMoreData.value) {
      logger.i("Skipping loadMore: loading=${loadingMore.value}, hasMore=${hasMoreData.value}");
      return;
    }

    final nextPage = currentPage.value + 1;
    logger.i("Loading more: page $nextPage");
    await fetchKitties(page: nextPage);
  }

  /// Refresh kitties
  Future<void> refreshKitties() async {
    resetPagination();
    await fetchKitties(page: 0, isRefresh: true);
  }

  /// Apply filters
  Future<void> applyFilters(Map<String, String> filters) async {
    currentFilters.value = filters;
    resetPagination();
    await fetchKitties(page: 0, filters: filters, isRefresh: true);
  }

  /// Apply sort
  Future<void> applySort(String sort) async {
    currentSort.value = sort;
    resetPagination();
    await fetchKitties(page: 0, isRefresh: true);
  }

  /// Search kitties
  Future<void> searchKitties(String query) async {
    searchQuery.value = query;
    
    if (query.isEmpty) {
      // Reset to normal list
      resetPagination();
      await fetchKitties(page: 0, isRefresh: true);
      return;
    }

    searchLoading(true);
    resetPagination();
    
    try {
      await fetchKitties(page: 0, search: query, isRefresh: true);
    } finally {
      searchLoading(false);
    }
  }

  /// Get user kitties (legacy - now uses fetchKitties)
  Future<void> getUserkitties({int? page, int? size}) async {
    await fetchKitties(page: page);
  }

  /// Get kitties with filters (legacy - now uses fetchKitties)
  Future<bool> getUserkittiesWithFilters({
    required Map<String, String> filters,
    int? page,
    int? size,
  }) async {
    try {
      await fetchKitties(page: page, filters: filters);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get refer kitties
  Future<void> getReferkitties({int? page = 0, int? size = 10}) async {
    kittiesLoading(true);
    update();

    try {
      final resp = await _userKittyService.getReferKitties(
        merchantCode: usermerchant.value.merchantCode,
        page: page ?? 0,
        size: size ?? 10,
      );

      status(resp["status"]);
      apiMessage(resp["message"]);
      
      if (resp["status"]) {
        referkitties([]);
        for (var element in resp["data"]["items"] ?? []) {
          referkitties.add(Kitty.fromJson(element));
        }
        if (referkitties.isNotEmpty) {
          totalRefers.value = resp["data"]["total"];
        }
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      rethrow;
    } finally {
      kittiesLoading(false);
      update();
    }
  }
 
  /// Get merchant transactions
  Future<void> getMerchantTransactions({
    required int code,
    int? page = 0,
    int size = 100,
    int? kittId,
  }) async {
    loadingTransactions(true);
    update();

    try {
      final resp = await _userKittyService.getMerchantTransactions(
        code: code,
        page: page ?? 0,
        size: size,
        kittyId: kittId,
      );

      Transac Results = Transac.fromJson(resp);

      merchtransactions([]);
      for (var items in resp["data"]["items"] ?? []) {
        merchtransactions.add(TransactionModel.fromJson(items));
      }
      Results.data = resultsDts.value;
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
    } finally {
      loadingTransactions(false);
      update();
    }
  }

  /// Set referer code
  Future<bool> setRefererCode({required SetMrchtDto request}) async {
    loading(true);
    update();

    try {
      final res = await _userKittyService.setRefererCode(
        requestData: request.toJson(),
      );

      apiMessage(res["message"]);
      
      if (res["status"]) {
        apiMessage(res["message"]);
      } else {
        Get.snackbar(
          "error",
          res["message"],
          backgroundColor: Colors.red,
        );
      }
      
      return res["status"];
    } catch (e) {
      logger.e(e);
      Get.snackbar(
        "error",
        "$e",
        backgroundColor: Colors.amber,
      );
      apiMessage("An error occured");
      return false;
    } finally {
      loading(false);
    }
  }

  /// Mask string for security
  String maskString(String input) {
    return _userKittyService.maskString(input);
  }

  /// Top up user account
  Future<bool> topup({
    required String phoneNumber,
    required int amount,
    required int channel,
    required int userId,
    String? email,
  }) async {
    topUploading(true);

    try {
      final res = await _userKittyService.topup(
        phoneNumber: phoneNumber,
        amount: amount,
        channel: channel,
        userId: userId,
        email: email,
      );

      if (res["status"]) {
        topUpData(res["data"]);
        apiMessageTopup(res["message"]);
        return true;
      } else {
        apiMessageTopup(res["message"]);
        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessageTopup('An error occured');
      return false;
    } finally {
      topUploading(false);
    }
  }

  /// Force refresh data from API
  Future<void> forceRefresh() async {
    await refreshKitties();
  }

  /// Edit transaction
  Future<TransactionEditResponse> editTransaction(
      TransactionEditRequest request) async {
    try {
      final response = await _userKittyService.editTransaction(
        request.toJson(),
      );

      if (response['status'] ?? false) {
        final editResponse = TransactionEditResponse.fromJson(response);

        if (editResponse.success) {
          if (editResponse.updatedTransaction != null) {
            _updateLocalTransaction(editResponse.updatedTransaction!);
          }

          Get.snackbar(
            'Success',
            editResponse.message,
            snackPosition: SnackPosition.top,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        }

        return editResponse;
      } else {
        return TransactionEditResponse(
          success: false,
          message: 'Failed to update transaction',
          statusCode: response['status_code'],
        );
      }
    } catch (e) {
      logger.e('Error editing transaction: $e');
      return TransactionEditResponse(
        success: false,
        message: 'Network error occurred',
        statusCode: 500,
      );
    }
  }

  /// Helper method to update transaction in local lists
  void _updateLocalTransaction(dynamic updatedTransaction) {
    try {
      // Update logic for local transaction lists if needed
    } catch (e) {
      logger.e('Error updating local transaction: $e');
    }
  }

  @override
  void onClose() {
    // Remove all listeners before disposing
    scrollController.dispose();
    super.onClose();
  }
}

/// Controller for managing single kitty data
class DataController extends GetxController {
  Rx<UserKitty> kitty = UserKitty().obs;
}