import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';

/// Service class responsible for all User Kitty-related API operations
class UserKittyService extends GetxService {
  final HttpService apiProvider = Get.find();
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();

  /// Get user data from local storage
  UserModelLatest? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      return UserModelLatest.fromJson(usr);
    }
    return null;
  }

  /// Save user data to local storage
  void saveLocalUser(Map<String, dynamic> userData) {
    box.write(CacheKeys.user, userData);
  }

  /// Get user details from API
  Future<Map<String, dynamic>> getUser(String phoneNumber) async {
    try {
      var resp = await apiProvider.request(
        url: "${ApiUrls.getUser}?phone_number=$phoneNumber",
        method: Method.GET,
      );
      return resp.data;
    } catch (e) {
      logger.e('Error fetching user: $e');
      rethrow;
    }
  }

  /// Get user kitties with optional filters and search
  Future<Map<String, dynamic>> getUserKitties({
    required String phoneNumber,
    required int page,
    required int size,
    String? search,
    Map<String, String>? filters,
  }) async {
    try {
      final queryParams = <String, String>{
        'phone_number': phoneNumber,
        'page': page.toString(),
        'size': size.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      if (filters != null) {
        queryParams.addAll(filters);
      }

      final uri = Uri.parse(ApiUrls.getUserKitties)
          .replace(queryParameters: queryParams);

      var resp = await apiProvider.request(
        url: uri.toString(),
        method: Method.GET,
      );
      return resp.data;
    } catch (e) {
      logger.e('Error fetching user kitties: $e');
      rethrow;
    }
  }

  /// Get refer kitties
  Future<Map<String, dynamic>> getReferKitties({
    required int? merchantCode,
    required int page,
    required int size,
  }) async {
    try {
      var resp = await apiProvider.request(
        url: "${ApiUrls.getReferKitties}?code=${merchantCode??''}&page=$page&size=$size",
        method: Method.GET,
      );
      return resp.data;
    } catch (e) {
      logger.e('Error fetching refer kitties: $e');
      rethrow;
    }
  }

  /// Get user transactions
  Future<Map<String, dynamic>> getUserTransactions({
    required String phoneNumber,
    required int page,
    required int size,
  }) async {
    try {
      var resp = await apiProvider.request(
        url: "${ApiUrls.getUserAllTransactions}?phone_number=$phoneNumber&page=$page&size=$size",
        method: Method.GET,
      );
      return resp.data;
    } catch (e) {
      logger.e('Error fetching user transactions: $e');
      rethrow;
    }
  }

  /// Get filtered user contributions
  Future<Map<String, dynamic>> getUserFilteredContributions({
    required String phoneNumber,
    String? startDate,
    String? endDate,
    String? code,
    int? kittyId,
  }) async {
    try {
      String url = "${ApiUrls.getUserAllTransactions}?phone_number=$phoneNumber";

      if (startDate != null && startDate.isNotEmpty && 
          endDate != null && endDate.isNotEmpty) {
        url += "&start-date=$startDate&end-date=$endDate";
      } else if (kittyId != null) {
        url += "&kitty_id=$kittyId";
      } else if (code != null && code.isNotEmpty) {
        url += "&transaction_code=$code";
      }

      var resp = await apiProvider.request(
        url: url,
        method: Method.GET,
      );
      return resp.data;
    } catch (e) {
      logger.e('Error fetching filtered contributions: $e');
      rethrow;
    }
  }

  /// Get merchant transactions
  Future<Map<String, dynamic>> getMerchantTransactions({
    required int code,
    required int page,
    required int size,
    int? kittyId,
  }) async {
    try {
      String url = kittyId != null
          ? "${ApiUrls.getMerTransac}?code=$code&page=$page&size=$size&kitty_id=$kittyId"
          : "${ApiUrls.getMerTransac}?code=$code&page=$page&size=$size";

      var resp = await apiProvider.request(
        url: url,
        method: Method.GET,
      );
      return resp.data;
    } catch (e) {
      logger.e('Error fetching merchant transactions: $e');
      rethrow;
    }
  }

  /// Set referer code
  Future<Map<String, dynamic>> setRefererCode({
    required Map<String, dynamic> requestData,
  }) async {
    try {
      var res = await apiProvider.request(
        url: ApiUrls.getCode,
        method: Method.POST,
        params: requestData,
      );
      return res.data;
    } catch (e) {
      logger.e('Error setting referer code: $e');
      rethrow;
    }
  }

  /// Top up user account
  Future<Map<String, dynamic>> topup({
    required String phoneNumber,
    required int amount,
    required int channel,
    required int userId,
    String? email,
  }) async {
    try {
      var res = await apiProvider.request(
        url: ApiUrls.topUp,
        method: Method.POST,
        params: {
          "amount": amount,
          "phone_number": phoneNumber,
          "channel_code": channel,
          "user_id": userId,
          "payer_email": email,
        },
      );
      return res.data;
    } catch (e) {
      logger.e('Error processing topup: $e');
      rethrow;
    }
  }

  /// Edit transaction
  Future<Map<String, dynamic>> editTransaction(
    Map<String, dynamic> requestData,
  ) async {
    try {
      final response = await apiProvider.request(
        url: ApiUrls.editTransaction,
        method: Method.PUT,
        params: requestData,
      );
      return response.data;
    } catch (e) {
      logger.e('Error editing transaction: $e');
      rethrow;
    }
  }



  /// Utility: Mask string for security
  String maskString(String input) {
    if (input.length > 9) {
      final int startLength = (input.length - 5) ~/ 2;
      final int endLength = input.length - startLength - 5;
      final String start = input.substring(0, startLength);
      final String end = input.substring(input.length - endLength);
      final String masked = '*' * 5;
      return '$start$masked$end';
    } else {
      return input;
    }
  }
}