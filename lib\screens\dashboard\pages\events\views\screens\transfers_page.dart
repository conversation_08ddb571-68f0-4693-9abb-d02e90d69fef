// DEPRECATED: This file is deprecated. Use the unified transfer system instead.
// Import the new unified transfer system
import 'package:onekitty/screens/dashboard/pages/transfers/transfers_exports.dart';
import 'package:flutter/material.dart'; 
class TransferScreen extends StatelessWidget {
  final int kittyId;
  
  const TransferScreen({
    super.key,
    required this.kittyId,
  });

  @override
  Widget build(BuildContext context) {
    // Redirect to the new unified transfer system
    return TransferPage(
      config: TransferPageConfig(
        transferType: TransferType.event,
        entityId: kittyId,
        title: 'Event Transfer',
      ),
    );
  }
}

/// DEPRECATED: This class is no longer used.
/// Transfer confirmation is now handled within the unified TransferPage.
class TransferConfirmPage extends StatelessWidget {
  final dynamic res, selectedProvider;
  final int kittyId;
  final int amount;
  final String receiverName;
  final String receiverAccRef;
  final String reason, transferMode;
  
  const TransferConfirmPage({
    super.key,
    this.res,
    this.selectedProvider,
    required this.kittyId,
    required this.amount,
    required this.receiverName,
    required this.receiverAccRef,
    required this.reason,
    required this.transferMode,
  });

  @override
  Widget build(BuildContext context) {
    // This should not be used anymore - redirect to main transfer page
    return Scaffold(
      appBar: AppBar(title: const Text('Deprecated')),
      body: const Center(
        child: Text(
          'This page is deprecated. Please use the unified transfer system.',
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}