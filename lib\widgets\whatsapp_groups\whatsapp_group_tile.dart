import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/contr_kitty_model.dart'; 
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart'; 
import 'package:onekitty/widgets/whatsapp_groups/whatsapp_group_type.dart';

/// Unified WhatsApp Group Tile that works with different group types
class WhatsAppGroupTile extends StatefulWidget {
  final dynamic group; // Can be Notifications or NotificationCls
  final WhatsAppGroupType type;
  final Function(bool)? onToggle;
  final VoidCallback? onRemove;

  const WhatsAppGroupTile({
    super.key,
    required this.group,
    required this.type,
    this.onToggle,
    this.onRemove,
  });

  @override
  State<WhatsAppGroupTile> createState() => _WhatsAppGroupTileState();
}

class _WhatsAppGroupTileState extends State<WhatsAppGroupTile> {
  late bool isActive;
  bool isToggling = false;

  @override
  void initState() {
    super.initState();
    _initializeActiveState();
  }

  void _initializeActiveState() {
    if (widget.group is Notifications) {
      final group = widget.group as Notifications;
      isActive = group.whatsappStatus == "ACTIVE";
    } else if (widget.group is NotificationCls) {
      final group = widget.group as NotificationCls;
      isActive = group.whatsappStatus == "ACTIVE";
    } else {
      isActive = false;
    }
  }

  String _getGroupName() {
    if (widget.group is Notifications) {
      final group = widget.group as Notifications;
      return group.whatsappGroupName ?? 'whatsapp_group'.tr;
    } else if (widget.group is NotificationCls) {
      final group = widget.group as NotificationCls;
      return group.whatsappGroupName ?? 'whatsapp_group'.tr;
    }
    return 'whatsapp_group'.tr;
  }

  String? _getProfileImage() {
    if (widget.group is Notifications) {
      final group = widget.group as Notifications;
      return group.whatsAppProfile;
    } else if (widget.group is NotificationCls) {
      final group = widget.group as NotificationCls;
      return group.whatsappProfile;
    }
    return null;
  }

  Widget _buildProfileImage() {
    final profileImage = _getProfileImage();
    
    if (profileImage != null && profileImage.isNotEmpty) {
      return CircleAvatar(
        radius: 20,
        backgroundImage: CachedNetworkImageProvider(profileImage),
        onBackgroundImageError: (_, __) {},
        child: Container(), // Fallback will be handled by onBackgroundImageError
      );
    }
    
    return CustomImageView(
      imagePath: AssetUrl.whatsapp,
      height: 40.h,
      width: 40.w,
    );
  }

  Future<void> _handleToggle(bool value) async {
    if (isToggling || widget.onToggle == null) return;

    setState(() {
      isToggling = true;
    });

    try {
      await widget.onToggle!(value);
      setState(() {
        isActive = value;
      });
    } catch (e) {
      // Revert the switch if the operation failed
      print('Error toggling WhatsApp group: $e');
    } finally {
      setState(() {
        isToggling = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          // Profile Image
          _buildProfileImage(),
          
          const SizedBox(width: 12),
          
          // Group Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getGroupName(),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isActive ? Colors.green.shade700 : Colors.grey,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      isActive ? 'active'.tr : 'inactive'.tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: isActive ? Colors.green.shade700 : Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Toggle Switch
          if (widget.onToggle != null)
            Switch(
              value: isActive,
              onChanged: isToggling ? null : _handleToggle,
              activeColor: Colors.green,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          
          // Remove Button
          if (widget.onRemove != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: widget.onRemove,
              icon: const Icon(
                Icons.delete_outline,
                color: Colors.red,
              ),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              padding: EdgeInsets.zero,
            ),
          ],
        ],
      ),
    );
  }
}