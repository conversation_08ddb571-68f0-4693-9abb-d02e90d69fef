import 'package:get/get.dart';

class TimeDurationTranslator {
  // Base translation map for English (you can translate these values later)
  static const Map<String, String> _translatables = {
    // Singular forms
    'second': 'second',
    'minute': 'minute',
    'hour': 'hour',
    'day': 'day',
    'week': 'week',
    'month': 'month',
    'year': 'year',
    
    // Plural forms
    'seconds': 'seconds',
    'minutes': 'minutes',
    'hours': 'hours',
    'days': 'days',
    'weeks': 'weeks',
    'months': 'months',
    'years': 'years',
  };

  static String translateDuration(String durationString) {
    if (durationString.trim().isEmpty) {
      return '';
    }

    // Parse the duration string and extract time units
    List<String> parts = durationString.trim().split(' ');
    List<String> translatedParts = [];
    
    for (int i = 0; i < parts.length; i += 2) {
      if (i + 1 < parts.length) {
        String number = parts[i];
        String unit = parts[i + 1].toLowerCase();
        
        // Check if the unit exists in our translatables
        if (_translatables.containsKey(unit)) {
          // Apply .tr only to the translatable unit
          String translatedUnit = _translatables[unit]!.toLowerCase().tr;
          
          // For Swahili, put the unit before the number
          if (Get.locale?.languageCode == 'sw') {
            translatedParts.add('$translatedUnit $number');
          } else {
            translatedParts.add('$number $translatedUnit');
          }
        } else {
          // If unit not found, keep original without .tr
          translatedParts.add('$number $unit');
        }
      }
    }
    
    String result = translatedParts.join(' ');
    
    return result;
  }
}