import 'package:onekitty/translations/english/events.dart';
import 'package:onekitty/translations/english/extra_twos.dart';
import 'package:onekitty/translations/english/extras_four.dart';
import 'package:onekitty/translations/english/extras_one.dart';
import 'package:onekitty/translations/english/extras_three.dart';
import 'common.dart';
import 'categories_filters.dart';
import 'chama_management.dart';
import 'kitty_management.dart';
import 'user_profile.dart';
import 'messaging_communication.dart';

const Map<String, String> english = {
  ...chamaManagementEnglish,
  ...kittyManagementEnglish,
  ...userProfileEnglish,
  ...messagingCommunicationEnglish,
  ...commonEnglish,
  ...categoriesFiltersEnglish,
  ...englishEvents, 
  ...extraTwos,
  ...extraOnes, 
  ...extraThrees, 
  ...extrasFour

  };
