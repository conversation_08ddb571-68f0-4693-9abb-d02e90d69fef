import 'package:onekitty/configs/country_specifics.dart';

const englishEvents = {
  'step_of': 'STEP {current} OF {total}',
  'scan_fingerprint_authenticate': 'Scan your fingerprint to authenticate',
  'biometric_authentication_not_available':
      'Biometric authentication not available on this device',
  'biometric_credentials_not_setup':
      'Biometric credentials not set up on this device',
  'no_biometric_credentials_enrolled':
      'No biometric credentials enrolled on this device',
  'biometric_authentication_failed':
      'Biometric authentication failed: {message}',
  'website_url_not_valid':
      '{field} URL is not valid. Please enter a valid URL (e.g., https://example.com)',
  'url_not_valid_platform':
      '{field} URL does not appear to be a valid {platform} link',
  'media_upload_incomplete':
      'Media {index} upload is incomplete. Please wait for upload to finish or remove it.',
  'media_invalid_url':
      'Media {index} has an invalid URL. Please re-upload the media.',
  'event_minimum_30_minutes': 'Event must be at least 30 minutes long',
  'please_select_location_map': 'Please select a location on the map',
  'invalid_event_details': 'Invalid event details',
  'failed_to_share_content': 'Failed to share content',
  'failed_to_launch_url': 'Failed to launch URL',
  'check_out_this_event': 'Check out this event: {url}',
  'event_default': 'Event',
  'kenya_awards_night_example': 'e.g ${CountryConfig.name} awards night',
  'info_awards_example': 'e.g <EMAIL>',
  'website_hint': 'https://yourwebsite.com',
  'facebook_hint': 'https://facebook.com/yourpage',
  'x_hint': 'https://x.com/youraccount',
  'instagram_hint': 'https://instagram.com/youraccount',
  'tiktok_hint': 'https://tiktok.com/@youraccount',
  'failed_to_add_ticket': 'Failed to add ticket: {error}',
  'club_member_example': 'e.g. For Club member',
  'size_label': 'Size',
  'price_500_example': '500',
  'date_time_example': '13/2/2024 14:00',
  'ticket_type_label': 'Ticket Type',
  'group_size_label': 'Group Size',
  'ticket_description_label': 'Ticket description',
  'ticket_description_required_msg': 'Ticket description is required',
  'slots_available_label': 'slots available',
  'limited_slots_label': 'Limited slots',
  'slots_required_msg': 'Slots is required',
  'price_required_msg': 'Price is required',
  'price_label': 'Price',
  'purchase_start_date_required_msg': 'Purchase Start Date is required',
  'purchase_start_date_label': 'Purchase Start Date',
  'date_example': '13/2/2024',
  'purchase_end_date_required_msg': 'Purchase End Date is required',
  'purchase_end_date_label': 'Purchase End Date',
  'edit_ticket_label': 'Edit Ticket',
  'must_pick_ticket_type_proceed': 'Must Pick a Ticket type to proceed',
  'event_date_time_example': '13/2/2024 2:00 PM',
  'kicc_example': 'e.g. KICC',
  'enter_location': 'Enter location',
  'enter_referral_code': 'Enter referral code',
  'wait_for_media_uploads_complete':
      'Please wait for all media uploads to complete',
  'please_fill_all_required_fields': 'Please fill all required fields',
  'end_date_must_be_after_start_date': 'End date must be after start date',
  'events_media_limit': 'Events Media: {message}',
  'file_size_exceeds_5mb': 'File size exceeds 5MB limit',
  'event_start_date_time_required': 'Event Start Date and Time is required',
  'event_start_date_time_label': 'Event Start Date and Time',
  'event_end_date_time_required': 'Event End Date and Time is required',
  'event_end_date_time_label': 'Event End Date and Time',
  'event_venue_is_required': 'Event Venue is required',
  'venue_label': 'Venue',
  'location_is_required': 'Location is required',
  'location_tip_label': 'Location Tip',
  'search_location': 'Search location',
  'date_time_pm_example': '13/2/2024 02:00 PM',
  'collected': 'Collected',
  'please_wait_fetching_data': 'Please wait a little while fetching data',
  'please_try_again_later': 'Please try again later',
  'transaction_id': 'Transaction ID',
  'contribution_details': 'Contribution details',
  'error_generating_poster': 'Error generating poster. Please try again.',
  'user_details_for_phone': 'User details {phone}',
  'first_name_is_required': 'First Name is required',
  'email_is_required': 'Email is required',
  'please_enter_valid_email': 'Please enter a valid email address',
  'invite_member_phone': 'Invite Member - {phone}',
  'first_name_label': 'First name: ',
  'please_enter_first_name': 'Please enter a first name',
  'please_enter_last_name': 'Please enter a last name',

  'admin_lock_warning':
      'Once you lock an admin, they cannot be modified or removed without contacting customer support. This is a security feature to prevent unauthorized changes by admin accounts.',

  'view_location': 'View Location',
  'pick_location': 'Pick Location',
  'please_select_location': 'Please select a location',
  'select_payment_method': 'Select Payment Method',
  'full_names_optional': 'Full Names(Optional)',
  'email_address_optional': 'Email Address(Optional)',
  'full_names': 'Full Names',
  'success_stk_push_sent': 'Success. STK PUSH SENT',
  'didnt_receive_stk_push': 'Didn\'t receive this STK push?',
  'kindly_wait_stk_push': 'Kindly Wait for the STK PUSH',
  'stk_sent_enter_pin':
      '{method} STK sent. Enter your PIN to complete transaction',
  'grant_permission': 'Grant Permission',
  'cancel_scanning': 'Cancel Scanning',
  'loading_transactions': 'Loading transactions...',
  'approved': 'Approved',
  'no_pending_transactions': 'No pending transactions',
  'no_approved_transactions': 'No approved transactions',
  'enter_your_comment': 'Enter your comment',
  'verify_ticket': 'Verify Ticket',
  'enter_ticket_code': 'Enter Ticket Code',
  'enter_code_here': 'Enter code here',
  'verify': 'Verify',
  'swipe_to_scan': 'Swipe to scan ',
  'verify_ticket_confirm': 'Verify Ticket Confirm',
  'qty': 'Qty: {quantity}',
  'verify_ticket_button': 'Verify Ticket',
  'error_generating_qr_code': 'Error generating QR code: {error}',
  'share_url': 'Share URL',
  'organizer': 'Organizer',
  'buy_ticket': 'Buy Ticket',
  'available_tickets': 'Available Tickets:',
  'no_tickets_available_moment': 'No Tickets Available at the moment',
  'reserve': 'Reserve',
  'checkout': 'Checkout {amount}',
  'successfully_payed': 'Successfully Payed!',
  'error_occurred_while_paying': 'Error occurred while paying',
  'contributions': 'Contributions',
  // Time-related translations for highPrecisiontimeSince function
  'left': 'left',
  // Singular forms
  'second': 'second',
  'minute': 'minute',
  'hour': 'hour',
  'day': 'day',
  'week': 'week',
  'month': 'month',
  'year': 'year',

  // Plural forms
  'seconds': 'seconds',
  'minutes': 'minutes',
  'hours': 'hours',
  'days': 'days',
  'weeks': 'weeks',
  'months': 'months',
  'years': 'years',

  // Compact Event Statistics
  'view_all_statistics': 'View All Statistics',

};