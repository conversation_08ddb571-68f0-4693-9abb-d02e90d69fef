import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:pinput/pinput.dart';

class ProcessPaymentOtp extends StatefulWidget {
  final bool isChamaContribute;
  const ProcessPaymentOtp({
    required this.isChamaContribute,
    super.key,
  }); 

  @override
  State<ProcessPaymentOtp> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<ProcessPaymentOtp> {
  late OTPTextEditController pinController;

  final logger = Get.find<Logger>();

  final box = Get.find<GetStorage>();

  ContributeController contributeController = Get.find();
  final ChamaController chamaController = Get.put(ChamaController());
  
  late OTPInteractor _otpInteractor;

  @override
  void initState() {
    super.initState();
    _otpInteractor = OTPInteractor();
    _otpInteractor
        .getAppSignature()
        .then((value) => print("signature = $value"));
    pinController = OTPTextEditController(
      codeLength: 6,
      onCodeReceive: (code) {
        if (kDebugMode) {
          print("Your Application receive code = $code");
        }
      },
      otpInteractor: _otpInteractor,
    )..startListenUserConsent((p0) {
        final exp = RegExp(r'(\d{6})');
        return exp.stringMatch(p0 ?? '') ?? '';
      });
  }

  @override
  void dispose() {
    pinController.stopListen();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
          fontSize: 20,
          color: Theme.of(context).scaffoldBackgroundColor,
          fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        border: Border.all(color: const Color.fromARGB(255, 1, 30, 54)),
        borderRadius: BorderRadius.circular(16),
      ),
    );
    SizeConfig().init(context);
    return Scaffold(
      appBar: AppBar(
        title: Text("otp_verification".tr),
        centerTitle: true,
        elevation: 0.3,
        leading: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
            ),
            onPressed: () => Navigator.of(context).pop()),
      ),
      body: Stack(
        children: [
          Container(
            alignment: Alignment.center,
            child: ListView(
              children: [
                const SizedBox(
                  height: 50,
                ),
                Image.asset(
                  "assets/images/otp.png",
                  semanticLabel: "one kitty otp",
                  height: 50,
                  width: 40,
                ),
                const SizedBox(
                  height: 30,
                ),
                Center(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text("enter_otp_code_sent".tr),
                  ],
                )),
                const SizedBox(
                  height: 20,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.spMin),
                  child: Obx(
                    () => Column(
                      children: [
                        Pinput(
                          length: 6,
                          controller: pinController,
                          defaultPinTheme: defaultPinTheme,
                          // androidSmsAutofillMethod: AndroidSmsAutofillMethod.smsUserConsent,
                          // listenForMultipleSmsOnAndroid: true,
                          showCursor: true,
                          onCompleted: (pin) async {
                            bool res =
                                await contributeController.contributeProcess(
                                    otp: pinController.text.trim(),
                                    transId: widget.isChamaContribute
                                        ? chamaController.contributeData[
                                            "checkout_request_id"]
                                        : contributeController.contributeData[
                                            "checkout_request_id"]);
                            if (!mounted) {
                              return;
                            }
                            if (res) {
                              ToastUtils.showSuccessToast(
                                  context,
                                  contributeController.apiMessageProcess.string,
                                  "Success");

                              try {
                                contributeController.confirmpayLoading.value = true;
                                
                                // Show processing dialog with progress indicator
                                _showProcessingDialog(context);
                                
                                // Use asynchronous polling instead of blocking wait
                                _startAsyncPaymentPolling(
                                  widget.isChamaContribute
                                      ? chamaController.contributeData["checkout_request_id"]
                                      : contributeController.contributeData["checkout_request_id"],
                                  onSuccess: () {
                                    // Dismiss the processing dialog
                                    if (Navigator.canPop(context)) {
                                      Navigator.pop(context);
                                    }
                                    contributeController.confirmpayLoading.value = false;
                                    Get.toNamed(NavRoutes.contrSuccessScreen);
                                  },
                                  onError: () {
                                    // Dismiss the processing dialog
                                    if (Navigator.canPop(context)) {
                                      Navigator.pop(context);
                                    }
                                    contributeController.confirmpayLoading.value = false;
                                    Get.toNamed(NavRoutes.contrErrScreen);
                                  }
                                );
                              } catch (e) {
                                // Dismiss the processing dialog
                                if (Navigator.canPop(context)) {
                                  Navigator.pop(context);
                                }
                                contributeController.confirmpayLoading.value = false;
                                print('${'some_error_occurred'.tr}${e.toString()}');
                                Get.toNamed(NavRoutes.contrErrScreen);
                              }
                            } else {
                              ToastUtils.showErrorToast(
                                context,
                                contributeController.apiMessageProcess.string,
                                "Error",
                              );
                            }
                          },
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Visibility(
                          visible: contributeController.isProcessloading.isTrue,
                          child: SpinKitDualRing(
                            color: Theme.of(context).primaryColor,
                            size: 70.0,
                          ),
                        ),
                        Visibility(
                          visible: contributeController.confirmpayLoading.value,
                          child: Container(
                            color: Colors.black.withOpacity(
                                0.5), // Semi-transparent background
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SpinKitDualRing(
                                    color: Theme.of(context).primaryColor,
                                    size: 70.0,
                                  ),
                                  const SizedBox(
                                      height:
                                          20), // Adjust the spacing between the loader and text
                                  Text(
                                    'processing_payment_please_wait'.tr, // Add your desired text here
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 35.spMin,
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "manual_method".tr,
                            style: const TextStyle(
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        widget.isChamaContribute
                            ? chamaController.contributeData["customer_message"]
                            : contributeController
                                .contributeData["customer_message"]
                                .toString(),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Add new methods for asynchronous polling
  void _showProcessingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async => false,
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SpinKitDualRing(
                    color: Theme.of(context).primaryColor,
                    size: 50.0,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'processing_payment'.tr,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'please_wait_confirm_payment'.tr,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 10),
                  Obx(() => Text(
                    '${'attempt_of_5'.tr} ${_currentAttempt.value} ${'of_5'.tr}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  )),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Observable for tracking attempts
  final RxInt _currentAttempt = 1.obs;

  // Asynchronous polling method using a more efficient approach
  void _startAsyncPaymentPolling(
    String checkoutId, {
    required Function onSuccess,
    required Function onError,
  }) async {
    const maxAttempts = 5;
    const pollInterval = Duration(seconds: 5);
    bool pollComplete = false;
    
    // Reset attempt counter
    _currentAttempt.value = 1;
    
    for (int i = 0; i < maxAttempts; i++) {
      _currentAttempt.value = i + 1;
      
      try {
        bool result = await contributeController.confirmContribution(
          checkoutId: checkoutId,
        );
        
        if (result) {
          var status = contributeController.status.value;
          if (status == "SUCCESS") {
            pollComplete = true;
            onSuccess();
            break;
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error during poll attempt ${i+1}: ${e.toString()}');
        }
      }
      
      // Only wait if we're going to poll again
      if (i < maxAttempts - 1 && !pollComplete) {
        await Future.delayed(pollInterval);
      }
    }
    
    // If we've completed all attempts without success
    if (!pollComplete) {
      onError();
    }
  }
}
