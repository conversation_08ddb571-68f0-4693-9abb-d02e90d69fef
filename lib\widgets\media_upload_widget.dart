
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/services/media_upload_service.dart';
import 'package:onekitty/utils/custom_text_style.dart';

/// Widget for handling media uploads with validation and progress tracking
class MediaUploadWidget extends StatefulWidget {
  final Function(List<Map<String, dynamic>>) onUploadsComplete;
  final List<MediaType> allowedTypes;
  final int maxFiles;
  final bool allowUrls;
  final String? title;

  const MediaUploadWidget({
    super.key,
    required this.onUploadsComplete,
    this.allowedTypes = const [MediaType.image, MediaType.video],
    this.maxFiles = 5,
    this.allowUrls = true,
    this.title,
  });

  @override
  State<MediaUploadWidget> createState() => _MediaUploadWidgetState();
}

class _MediaUploadWidgetState extends State<MediaUploadWidget> {
  late MediaUploadService uploadService;
  final TextEditingController urlController = TextEditingController();

  @override
  void initState() {
    super.initState();
    uploadService = Get.put(MediaUploadService());
  }

  @override
  void dispose() {
    urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.cloud_upload, color: Theme.of(context).primaryColor),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    widget.title ?? 'media_upload'.tr,
                    style: CustomTextStyles.titleSmallGray900.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // Upload buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _pickFiles,
                    icon: const Icon(Icons.file_upload),
                    label: Text('upload_files'.tr),
                  ),
                ),
                if (widget.allowUrls) ...[
                  SizedBox(width: 12.w),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _showUrlDialog,
                      icon: const Icon(Icons.link),
                      label: Text('add_url'.tr),
                    ),
                  ),
                ],
              ],
            ),
            SizedBox(height: 16.h),

            // Upload queue
            Obx(() => _buildUploadQueue()),

            // Progress indicator
            Obx(() => _buildProgressIndicator()),

            // Upload button
            SizedBox(height: 16.h),
            Obx(() => _buildUploadButton()),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadQueue() {
    if (uploadService.uploadQueue.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300, style: BorderStyle.solid),
        ),
        child: Row(
          children: [
            Icon(Icons.cloud_upload_outlined, color: Colors.grey.shade500),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                'no_media_files_selected'.tr,
                style: CustomTextStyles.bodyMediumGray600,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${'upload_queue'.tr} (${uploadService.uploadQueue.length}/${widget.maxFiles})',
          style: CustomTextStyles.bodyMediumGray600.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        ...uploadService.uploadQueue.map((item) => _buildUploadItem(item)),
      ],
    );
  }

  Widget _buildUploadItem(MediaUploadItem item) {
    return Obx(() => Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Status icon
          Icon(
            item.status.value.icon,
            color: item.status.value.color,
            size: 20,
          ),
          SizedBox(width: 12.w),
          
          // File info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.displayName,
                  style: CustomTextStyles.bodyMediumGray600.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '${item.mediaType.name.toUpperCase()} • ${item.status.value.displayName}',
                  style: CustomTextStyles.bodySmallGray600,
                ),
                if (item.hasFailed && item.errorMessage.value.isNotEmpty)
                  Text(
                    item.errorMessage.value,
                    style: CustomTextStyles.bodySmallGray600.copyWith(
                      color: Colors.red.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          
          // Progress or remove button
          if (item.isUploading)
            SizedBox(
              width: 20.w,
              height: 20.h,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                value: item.progress.value > 0 ? item.progress.value : null,
              ),
            )
          else
            IconButton(
              onPressed: () => uploadService.removeFromUploadQueue(item.id),
              icon: const Icon(Icons.close, size: 20),
              constraints: const BoxConstraints(),
              padding: EdgeInsets.zero,
            ),
        ],
      ),
    ));
  }

  Widget _buildProgressIndicator() {
    if (!uploadService.isUploading.value || uploadService.uploadQueue.isEmpty) {
      return const SizedBox();
    }

    return Column(
      children: [
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: uploadService.overallProgress.value,
                backgroundColor: Colors.grey.shade300,
              ),
            ),
            SizedBox(width: 12.w),
            Text(
              '${(uploadService.overallProgress.value * 100).toInt()}%',
              style: CustomTextStyles.bodySmallGray900.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Text(
          '${'uploading'.tr} ${uploadService.completedUploads.value + 1} of ${uploadService.uploadQueue.length}',
          style: CustomTextStyles.bodySmallGray600,
        ),
      ],
    );
  }

  Widget _buildUploadButton() {
    final canUpload = uploadService.uploadQueue.isNotEmpty && 
                     !uploadService.isUploading.value;
    
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: canUpload ? _startUpload : null,
        icon: uploadService.isUploading.value
            ? SizedBox(
                width: 16.w,
                height: 16.h,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : const Icon(Icons.cloud_upload),
        label: Text(
          uploadService.isUploading.value
              ? 'uploading'.tr
              : '${'upload_media'.tr} (${uploadService.uploadQueue.length})',
        ),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 12.h),
        ),
      ),
    );
  }

  void _pickFiles() async {
    // Placeholder for file picker implementation
    // In real implementation, you would use file_picker package
    // For now, just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('file_picker_not_implemented'.tr),
      ),
    );
  }

  void _showUrlDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('add_media_url'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: urlController,
              decoration: InputDecoration(
                labelText: 'media_url'.tr,
                hintText: 'media_url_example'.tr,
                border: const OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16.h),
            DropdownButtonFormField<MediaType>(
              decoration: InputDecoration(
                labelText: 'media_type'.tr,
                border: const OutlineInputBorder(),
              ),
              items: widget.allowedTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.name.toUpperCase()),
                );
              }).toList(),
              onChanged: (type) {
                if (type != null && urlController.text.isNotEmpty) {
                  _addUrlToQueue(urlController.text, type);
                  urlController.clear();
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('cancel'.tr),
          ),
        ],
      ),
    );
  }

  void _addUrlToQueue(String url, MediaType type) {
    if (uploadService.uploadQueue.length >= widget.maxFiles) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('maximum_files_allowed'.tr.replaceAll('{count}', widget.maxFiles.toString()))),
      );
      return;
    }

    final item = MediaUploadItem.fromUrl(url, type);
    uploadService.addToUploadQueue(item);
  }

  void _startUpload() async {
    final uploadedMedia = await uploadService.uploadAllMedia(
      context: context,
      onProgressUpdate: (progress) {
        // Progress is already handled by the service observables
      },
      onItemComplete: (item, url) {
        // Individual item completion handled by service
      },
      onItemError: (item, error) {
        // Individual item errors handled by service
      },
    );

    // Notify parent widget of completed uploads
    widget.onUploadsComplete(uploadedMedia);
  }
}
