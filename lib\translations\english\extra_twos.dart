const extraTwos = {
  
  'failed_to_edit_member': 'Failed to edit member',
  'failed_to_delete_member': 'Failed to delete member',
  'an_error_occurred_msg': 'An error occurred',
  'upload_csv_xlsx': 'Upload CSV/XLSX',
  'search_members': 'Search Members',
  'select_all_deselect_all': 'Select All / Deselect All',
  'delete_selected': 'Delete Selected',
  'exit_selection': 'Exit Selection',
  'multi_select': 'Multi-Select',
  'help': 'help',
  'continue': 'Continue',
  'no_members_in_group': 'No members in group',
  'sms_groups': 'SMS Groups',
  'create_group': 'Create Group',
  'create_group_now': 'Create Group Now',
  'edit_group': 'Edit Group',
  'edit_members': 'Edit Members',
  'delete_group': 'Delete Group',
  'are_you_sure_delete_group': 'Are you sure you want to delete this group?',
  'created_at': 'created at:',
  'events': 'Events',
  'create_event': 'Create Event',
  'my_events': 'My Events',
  'other_events': 'Other Events',
  'exit_event_creation': 'Exit Event Creation?',
  'progress_will_be_lost': 'Your progress will be lost.',
  'exit': 'Exit',
  'create_an_event': 'Create an Event',
  'was_successfully_created': 'was\nsuccessfully created',
  'share_event_link_to_friends':
      'Share the event link to friends and families to register',
  'invite_event_operators': 'Invite event operators →',
  'event_title_required': 'Event title is required',
  'event_banner_required': 'Event banner is required',
  'event_title': 'Event Title',
  'event_description': 'Event Description',
  'describe_your_event': 'Describe your event',
  'email_address_required': 'Email address is required',

  'category_required': 'Category is required',
  'event_images': 'Event Images',
  'wait_for_uploads_complete': 'Please wait for current uploads to complete',
  'no_images_selected': 'No images selected',
  'no_valid_images_found': 'No valid images found. Images must be under 10MB.',
  'upload_event_images': 'Upload Event Images',
  'enter_valid_facebook_url': 'Please enter a valid Facebook URL',
  'enter_valid_x_url': 'Please enter a valid X (Twitter) URL',
  'enter_valid_instagram_url': 'Please enter a valid Instagram URL',
  'enter_valid_tiktok_url': 'Please enter a valid TikTok URL',
  'website_optional': 'Website (optional)',
  'facebook_link_optional': 'Facebook link (optional)',
  'x_link_optional': 'X link (optional)',
  'instagram_link_optional': 'Instagram link (optional)',
  'tiktok_link_optional': 'TikTok link (optional)',
  'end_date_after_start_date': 'End date must be after start date',
  'invalid_date_format': 'Invalid date format',
  'must_pick_ticket_type': 'Must pick a ticket type to proceed',
  'slots_required': 'Slots is required',
  'price_required': 'Price is required',
  'purchase_start_date_required': 'Purchase Start Date and Time is required',
  'purchase_start_date': 'Purchase Start Date',
  'purchase_end_date': 'Purchase End Date',
  'confirm_delete_ticket': 'Are you sure you want to delete this ticket?',
  'event_start_date_required': 'Event Start Date is required',
  'event_start_date_time': 'Event Start Date and Time',
  'event_end_date_required': 'Event End Date is required',
  'event_end_date_time': 'Event End Date and Time',
  'physical_event': 'Physical event',
  'online_event': 'Online event',
  'event_venue_required': 'Event Venue is required',
  'choose_on_map': 'Choose on Map',
  'location_required': 'Location is required',
  'edit_event': 'Edit Event',
  'events_media': 'Events Media',
  'originally_had_media_items':
      'Originally had {count} media items. Showing first 3.',
  'maximum_media_items_allowed': 'Maximum {limit} media items allowed',
  'please_select_image_file': 'Please select an image file',
  'failed_to_upload_image': 'Failed to upload image',
  'upload_completed_invalid_url': 'Upload completed but file URL is invalid',
  'backend_invalid_url_format': 'Backend returned invalid URL format',
  'media_already_exists': 'Media already exists',
  'failed_update_event_media': 'Failed to update event media',
  'media_uploaded_successfully': 'Media uploaded successfully',
  'upload_failed': 'Upload failed',
  'error_uploading_image': 'Error uploading image',
  'confirm_delete_photo': 'Are you sure you want to delete this Photo?',
  'deleting_media': 'Deleting media...',
  'media_deleted_successfully': 'Media deleted successfully',
  'failed_delete_media': 'Failed to delete media',
  'event_description_placeholder': 'e.g Time to award our best artist',
  'loading_event': 'Loading Event...',
  'event_statistics': 'Event Statistics',
  'refresh_statistics': 'Refresh Statistics',
  'loading_statistics': 'Loading statistics...',
  'unable_load_statistics': 'Unable to Load Statistics',
  'no_statistics_available': 'No Statistics Available',
  'no_statistics_message':
      'There are no statistics to display at this time. Try creating some events or check back later.',
  'overview': 'Overview',
  'total_events': 'Total Events',
  'total_revenue': 'Total Revenue',
  'tickets_sold': 'Tickets Sold',
  'total_attendees': 'Total Attendees',
  'revenue_breakdown': 'Revenue Breakdown',
  'daily_average': 'Daily Average',
  'total_transactions': 'Total Transactions',
  'user_demographics': 'User Demographics',
  'unique_customers': 'Unique Customers',
  'repeat_customers': 'Repeat Customers',
  'repeat_rate': 'Repeat Rate',
  'avg_tickets_per_user': 'Avg Tickets/User',
  'event_performance': 'Event Performance',
  'payment_methods': 'Payment Methods',
  'popular_tickets': 'Popular Tickets',
  'sales_timeline': 'Sales Timeline',
  'coming_soon': 'Coming Soon',
  'feature_available_future': '{feature} will be available in a future update.',
  'got_it': 'Got it',
  'invite_user': 'Invite User',
  'new_event_invitees': 'new Event invitees',
  'send_invites': 'Send Invites',
  'event_invitees': 'Event invitees',
  'no_invitees_found': 'No invitees found',
  'invite_code_copied': 'Invite code copied to clipboard',
  'user_details': 'User details',
  'click_member_set_role':
      'Click on a member to set their role in {type} management',
  'admin_locked': 'Admin Locked',
  'loading_whatsapp_groups': 'Loading WhatsApp Groups...',
  'connected_groups': 'Connected Groups',
  'connect_whatsapp_group_message':
      'Connect a WhatsApp group to receive\ntransaction notifications',
  'confirm_remove_whatsapp_group':
      'Are you sure you want to remove this WhatsApp group?\nYou will no longer receive transaction updates for this event.',
  'please_enter_whatsapp_link': 'Please enter a WhatsApp link',
  'cannot_connect_whatsapp_group': 'Cannot connect WhatsApp group at this time',
  'connect': 'Connect',
  'event_qr_code': 'Event QR Code',
  'scan_to_open_event_page': 'Scan to open event page',
  'url': 'URL',
  'share_qr_code': 'Share QR Code',
  'scan_to_join_participate': 'Scan to Join & Participate',
  'about_event': 'About Event',
  'manage_delegates': 'Manage Delegates',
  'purchase_ticket': 'Purchase Ticket',
  'verify_tickets': 'Verify Tickets',
  'transfer_funds': 'Transfer funds',
  'event_transactions': 'Event Transactions',
  'no_transactions_found': 'No Transactions Found',
  'no_tickets_available': 'No Tickets Available at the moment',
  'enter_user_details': 'enter user details',
  'pick_a_ticket': 'Pick a Ticket:',
  
  'payment_ref': 'Payment Ref',
  'transactions_will_appear_here':
      'Transactions will appear here once available',
  'export_transaction': 'Export Transaction',
  'export_to_text': 'Export to Text',
  'share_via_any_app': 'Share via any app',
  'whatsapp_message': 'WhatsApp Message',
  'share_directly_via_whatsapp': 'Share directly via WhatsApp',
  'transaction_receipt': 'Transaction Receipt',
  'transaction_tickets': 'Transaction Tickets',
  'save_pdf': 'Save PDF',
  'share_tickets': 'Share Tickets',
  'view_receipt': 'View Receipt',
  
  'enter_account_name': 'Enter account name',
  'export_options': 'Export Options',
  'generate_statement': 'Generate Statement',
  'generate_detailed_statement':
      'Generate detailed statement to your Email/WhatsApp',
  'export_to_pdf': 'Export to PDF',
  'download_as_pdf_document': 'Download as PDF document',
  'export_to_excel': 'Export to Excel',
  'download_as_spreadsheet_file': 'Download as spreadsheet file',
  'share_as_plain_text_message': 'Share as plain text message',
  'pdf_statement': 'PDF Statement',
  'no_transactions_to_export': 'No transactions to export',
  'no_transactions_to_share': 'No transactions to share',
  'file_options': 'File Options',
  'open_file': 'Open File',
  'open_with_default_app': 'Open with default app',
  'share_file': 'Share File',
  'share_with_other_apps': 'Share with other apps',
  'generating_excel_file': 'Generating Excel file...',
  'unable_to_generate_statement':
      'Unable to generate statement: Missing chama information',
  'failed_to_share_transaction': 'Failed to share transaction',
  'failed_to_share_via_whatsapp': 'Failed to share via WhatsApp',
  'event_tickets': 'Event Tickets',
  'failed_to_share_tickets': 'Failed to share tickets',
  'failed_to_share_receipt': 'Failed to share receipt',
  'receipt_saved_as_pdf_successfully': 'Receipt saved as PDF successfully!',
  'failed_to_save_pdf': 'Failed to save PDF',
  'transaction_report': 'Transaction Report',
  'failed_to_generate_excel_file': 'Failed to generate Excel file',
  'your_account_has_been_blocked': 'Your account has been blocked',
  'account_blocked_description':
      'Purchases with cash back remain available. There are restrictions on the withdrawal of funds, as well as on changing the details and personal data. To clarify the situation, contact the Customer Support Service.',
  'write_to_support': 'Write to support',
  'logout': 'Logout',
  'karibu': 'Karibu',
  'sign_in': 'Sign In',
  'password': 'Password',
  'password_required': 'Password required',
  'log_in': 'Log in',
  'forgot_your_password': 'Forgot your password',
  'dont_have_account_yet': "Don't have an account yet? ",
  'sign_up': 'Sign up',
  'maintenance_mode': 'Maintenance Mode',
  'maintenance_description':
      'We are currently undergoing maintenance. Please check back later.',
  'authentication': 'Authentication',
  'enter_password_to_continue': 'Enter your password to continue',
  'click_here_to_log_out': 'Click here to log out',
  'enter_otp': 'Enter OTP',
  'please_enter_otp_sent_to': 'Please enter OTP sent to ',
  'did_not_receive_otp': 'Did not receive an OTP?',
  'resend_otp': 'Resend OTP',
  'have_an_account': 'Have an account?',
  'sign_in_lower': 'Sign in',
  'password_confirmation': 'Password Confirmation',
  'enter_password': 'Enter password',
  'password_cannot_have_less_than_8_characters':
      'Password cannot have less than 8 characters',
  'confirm_password': 'Confirm Password',
  'confirm_password_label': 'Confirm password',
  'pin_does_not_match': 'Pin does not match',
  'reset_password': 'Reset Password',
  'create_an_account': 'Create an Account',
  'name_is_required': 'Name is required',
  'by_signing_up_agree': 'By signing up you agree to our ',
  'terms_conditions': 'Terms & Conditions',
  'accept_terms_to_continue': 'Accept our terms and conditions to continue',
  'verify_number': 'Verify number',
  'already_have_account': 'Already have an account?',
  'accept': 'Accept',
  'skip': 'Skip',
  'welcome_to_onekitty': 'Welcome to OneKitty',
  'onekitty_description':
      'An all in one platform that helps you create and manage social contributions from friends and family',
  'create_kitty_description':
      'One app, endless possibilities. Easily contribute with friends and family to anything',
  'join_hands_with_friends': 'Join hands with friends',
  'join_hands_description':
      'Join your friends in lending a hand to a common goal and be part of something special',
  'kyc_verification': 'KYC Verification',
  'id_number': 'ID Number',
  'start_verification': 'Start Verification',
  'capture_front_id': 'Capture Front ID',
  'capture_back_id': 'Capture Back ID',
  'capture_selfie': 'Capture Selfie',
  'review_submit': 'Review & Submit',
  'camera_permission_required': 'Camera Permission Required',
  'camera_access_needed':
      'Camera access is needed to capture your ID. You can also select from gallery instead.',
  'use_gallery': 'Use Gallery',
  'camera_permission_denied': 'Camera Permission Denied',
  'camera_permission_permanently_denied':
      'Camera permission is permanently denied. Please enable it in settings or use gallery instead.',
  'open_settings': 'Open Settings',
  'no_camera_found': 'No Camera Found',
  'no_cameras_available':
      'No cameras are available on this device. Please select from gallery.',
  'camera_error': 'Camera Error',
  'failed_to_initialize_camera': 'Failed to initialize camera: ',
  'front_id_selected_successfully': 'Front ID selected successfully!',
  'camera_not_initialized':
      'Camera not initialized. Please restart the camera.',
  'front_id_captured_successfully': 'Front ID captured successfully!',
  'failed_to_capture_image': 'Failed to capture image: ',
  'permission_denied': 'Permission Denied',
  'camera_permission_required_capture_id':
      'Camera permission is required to capture ID',
  'back_id_captured_successfully': 'Back ID captured successfully!',
  'permission_denied_selfie': 'Camera permission is required to capture selfie',
  'selfie_captured_successfully': 'Selfie captured successfully!',
  'no_face_detected': 'No Face Detected',
  'ensure_face_clearly_visible': 'Please ensure your face is clearly visible',
  'face_angle_issue': 'Face Angle Issue',
  'face_directly_forward': 'Please face directly forward',
  'validation_error': 'Validation Error',
  'failed_to_process_selfie': 'Failed to process selfie: ',
  'front_id': 'Front ID',
  'back_id': 'Back ID',
  'selfie': 'Selfie',
  'no_front_id_captured': 'No Front ID captured',
  'no_back_id_captured': 'No Back ID captured',
  'no_selfie_captured': 'No Selfie captured',
  'no_document_captured': 'No document captured',
  'id_number_label': 'ID Number',
  'no_id_number_entered': 'No ID number entered',
  'id_number_must_be_7_8_digits': 'ID number must be 7-8 digits',
  'uploading_current_upload': 'Uploading ',
  'submit_kyc': 'Submit KYC',
};