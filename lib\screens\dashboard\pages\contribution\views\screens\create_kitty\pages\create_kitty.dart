import 'package:auto_size_text/auto_size_text.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/configs/country_specifics.dart';
import 'package:onekitty/configs/payment_channels.dart';

import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';

import 'package:onekitty/models/kitty_payload.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/create_kitty/pages/mobile.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/create_kitty/pages/paybill.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/create_kitty/pages/till_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/edit_kitty/tabs/bank.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/success.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/datetime/combined_datetime.dart';
import 'package:onekitty/utils/iswysiwyg.dart' show quilltoHtml;
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/whatsapp_error_dialog.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';

import '../../../../../../../../utils/themes_colors.dart';
import '../../../../widgets/date_picker.dart';

class StepOne extends StatefulWidget {
  const StepOne({super.key});

  @override
  State<StepOne> createState() => _StepOneState();
}

class _StepOneState extends State<StepOne> with TickerProviderStateMixin {
  final formKey = GlobalKey<FormState>();
  //q.QuillController descrpcontroller = q.QuillController.basic();
  final beneficiaryController = TextEditingController();

  final linkController = TextEditingController();
  final kittyNameController = TextEditingController();
  // final desController = TextEditingController();
  final targetController = TextEditingController();

  final phoneController = TextEditingController();
  final accountController = TextEditingController();
  final accountrefController = TextEditingController();
  final dateController = TextEditingController();
  final timeController = TextEditingController();
  final refererController =
      TextEditingController(text: Get.find<GlobalControllers>().getCode());
  PhoneNumber num = CountryConfig.phoneNumber;
  String myPhone =
      Get.find<Eventcontroller>().getLocalUser()?.phoneNumber ?? '';
  String benPhone =
      Get.find<Eventcontroller>().getLocalUser()?.phoneNumber ?? '';
  late TabController tabController;
  final userkitty = Get.put(UserKittyController());
  q.QuillController quillController = q.QuillController.basic();
  int currentStep = 0;
  bool isCompleted = false;
  bool showEditor = false;
  bool showAddLink = false;
  final countryPicker = const FlCountryCodePicker();
  final paymentChannel = Get.isRegistered<PaymentChannel>()
      ? Get.find<PaymentChannel>()
      : Get.put(PaymentChannel());
  CountryCode? countryCode;
  List<String> socials = [
    "assets/images/insta.png",
    "assets/images/whatsapp.jpg",
    "assets/images/twitter.png",
    "assets/images/solar_link-bold.png"
  ];
  bool showMoreSettings = false;
  final q.QuillController desController = q.QuillController(
      document: q.Document(),
      selection: const TextSelection.collapsed(offset: 0));

  String? selectedChannel = "M-Pesa";
  String? endDate;
  int? kittyId;
  var arg = Get.arguments;
  var params = Get.parameters;
  List tillPaybil = ["Mpesa Paybill", "Buy Goods(Till)"];
  String selectedtillPaybil = "Mpesa Paybill";
  KittyController kittyController = Get.put(KittyController());
  final box = GetStorage();

  final _beneficiaryNumberController = TextEditingController(
      text: Get.find<Eventcontroller>()
              .getLocalUser()
              ?.phoneNumber
              ?.substring(3) ??
          '');
  final _mobilenumberController = TextEditingController(
      text: Get.find<Eventcontroller>()
              .getLocalUser()
              ?.phoneNumber
              ?.substring(3) ??
          '');

  setValues() async {
    if (kDebugMode) {}
    if (arg != null) {
      if (arg["isEdit"] == true) {
        String? phone = userkitty.getLocalUser()?.phoneNumber?.substring(3);
        final kittyCreated = kittyController.kittCreated.value;
        String? title = kittyCreated.title;
        kittyId = kittyCreated.iD;
        String? desc = kittyCreated.description;
        DateTime? endDateGot = kittyCreated.endDate;
        String? channelBenef = kittyCreated.beneficiaryChannel;
        String? benef = kittyCreated.beneficiaryAccount;
        kittyNameController.text = title ?? "";
        beneficiaryController.text = benef ?? "";
        selectedChannel = channelBenef?.toLowerCase();
        phoneController.text = phone ?? "";
        endDate = endDateGot!.toIso8601String();
        // desController. = desc ?? "";
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    tabController = TabController(initialIndex: 0, length: paymentChannel.paymentGateways().length, vsync: this);
    kittyController.selectedKittyCategoryModels.clear();
    kittyController.selectedKittyCategories.clear();

    if (kDebugMode) {}
    setValues();
    phoneController.text =
        userkitty.getLocalUser()?.phoneNumber?.substring(3) ?? "";
    "";

    super.initState();
  }

  @override
  void dispose() {
    kittyController.selectedKittyCategoryModels.clear();
    kittyController.selectedKittyCategories.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        minimum: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          children: [
            SizedBox(
              height: 10.h,
            ),
            Text("create_a_kitty_title".tr,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22)),
            Expanded(
              child: Theme(
                data: Theme.of(context).copyWith(
                    colorScheme:
                        const ColorScheme.light(primary: AppColors.primary)),
                child: Form(
                  key: formKey,
                  child: Stepper(
                    physics: const BouncingScrollPhysics(),
                    elevation: 0,
                    currentStep: currentStep,
                    type: StepperType.horizontal,
                    steps: getSteps(),
                    onStepContinue: () {
                      final isLastStep = currentStep == getSteps().length - 1;
                      if (isLastStep) {
                      } else {
                        setState(() {
                          currentStep += 1;
                        });
                      }
                    },
                    // onStepTapped: (step) => setState(() {
                    //   currentStep = step;
                    // }),
                    onStepCancel: currentStep == 0
                        ? null
                        : () {
                            setState(() {
                              currentStep -= 1;
                            });
                          },
                    controlsBuilder: (context, details) {
                      final isLastStep = currentStep == getSteps().length - 2;
                      final isLastStep2 = currentStep == getSteps().length - 1;
                      return isLastStep2
                          ? const SizedBox()
                          : Container(
                              height: 50,
                              margin: const EdgeInsets.only(top: 7),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  if (currentStep != 0)
                                    SizedBox(
                                      height: 40,
                                      child: OutlinedButton(
                                          onPressed: details.onStepCancel,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8.0),
                                            child: Text("back_button".tr),
                                          )),
                                    ),
                                  Obx(
                                    () => CustomKtButton(
                                        width: 100,
                                        height: 40,
                                        isLoading:
                                            kittyController.isloading.isTrue,
                                        onPress: isLastStep
                                            ? () async {
                                                AnalyticsEngine
                                                    .userCreatesKitty();
                                                AnalyticsEngine.analytics
                                                    .setAnalyticsCollectionEnabled(
                                                        true);
                                                await createKitty();
                                              }
                                            : () {
                                                if (currentStep == 0) {
                                                  if (_titleKey.currentState!
                                                      .validate()) {
                                                    details.onStepContinue!();
                                                  } else {
                                                    return;
                                                  }
                                                }
                                              },
                                        btnText: isLastStep
                                            ? "submit_button".tr
                                            : "next_button".tr),
                                  ),
                                ],
                              ),
                            );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  final _titleKey = GlobalKey<FormState>();
  List<Step> getSteps() => [
        Step(
            state: currentStep > 0 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: Text("kitty".tr),
            content: buildTextField(context),
            isActive: currentStep >= 0),
        Step(
            state: currentStep > 1 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: Text("financial".tr),
            content: buildStepTwo(),
            isActive: currentStep >= 1),
        Step(
            state: currentStep == 2 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: Text("complete".tr),
            content: const SucessPage(
              text: 'created',
            ),
            isActive: currentStep >= 2),
      ];

  Widget buildTextField(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "whatsapp_group_link_optional".tr,
          style: Theme.of(context)
              .textTheme
              .titleMedium
              ?.copyWith(fontWeight: FontWeight.bold),
        ),
        CustomTextField(
          labelText: "",
          controller: linkController,
          // validator: (p0) {
          //   if (p0!.isEmpty) {
          //     return "Filed cannot be empty";
          //   }
          //   return p0;
          // },
        ),
        Text("kitty_name".tr,
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        Form(
          key: _titleKey,
          child: CustomTextField(
            controller: kittyNameController,
            hintText: "wedding_contribution_example".tr,
            labelText: "title".tr,
            validator: (p0) {
              if (p0!.isEmpty) {
                return "filed_cannot_be_empty".tr;
              } else if (p0.length < 5) {
                return "kitty_name_length_validation".tr;
              }
              return null;
            },
          ),
        ),
        Text("kitty_description".tr,
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        // CustomTextField(
        //   controller: desController,
        //   hintText: "e.g purpose of contribution",
        //   labelText: "Description",
        //   validator: (p0) {
        //     // if (p0!.isEmpty) {
        //     //   return "This field cannot be empty";
        //     // }
        //     return null;
        //   },
        // ),
        Padding(
          padding: const EdgeInsets.all(4.0),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: AppColors.blueButtonColor),
            ),
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                q.QuillSimpleToolbar(
                  config: const q.QuillSimpleToolbarConfig(
                    multiRowsDisplay: false,
                  ),
                  controller: desController,
                ),
                const SizedBox(height: 15),
                q.QuillEditor.basic(
                  controller: desController,
                  config: q.QuillEditorConfig(
                    placeholder: "describe_purpose_kitty".tr,

                    // readOnly: false,
                    autoFocus: false,
                    enableInteractiveSelection:
                        true, // Enable interactive selection to allow text editing
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),

        Text("target_amount_optional".tr,
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        CustomTextField(
          controller: targetController,
          hintText: "amount_example".tr,
          labelText: "amount".tr,
          showNoKeyboard: true,
          validator: (p0) {
            // if (p0!.isEmpty) {
            //   return "This field cannot be empty";
            // }
            return null;
          },
        ),
        Text("kitty_category".tr,
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        SizedBox(height: 8.h),
        GetX<KittyController>(
            init: KittyController(),
            initState: (state) async {
              await state.controller?.getKittyCategories();
            },
            builder: (controller) {
              if (controller.isLoadingKittyCategories.isTrue) {
                return Container(
                  height: 55.h,
                  padding: const EdgeInsets.all(8),
                  width: double.infinity,
                  decoration: ThemeHelper.inputBoxDecoration(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('loading_categories_text'.tr),
                      const CircularProgressIndicator()
                    ],
                  ),
                );
              } else if (controller.kittyCategories.isEmpty) {
                return Container(
                  height: 55.h,
                  padding: const EdgeInsets.all(8),
                  width: double.infinity,
                  decoration: ThemeHelper.inputBoxDecoration(),
                  child: Center(child: Text('no_categories_found'.tr)),
                );
              } else {
                return GestureDetector(
                  onTap: () => _showCategoryBottomSheet(context, controller),
                  child: Container(
                    height: 55.h,
                    padding: const EdgeInsets.all(12),
                    width: double.infinity,
                    decoration: ThemeHelper.inputBoxDecoration(),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Obx(() =>
                              controller.selectedKittyCategoryModels.isEmpty
                                  ? Text('select_categories_placeholder'.tr)
                                  : Text(
                                      controller.selectedKittyCategoryModels
                                          .map((c) => c.name)
                                          .join(', '),
                                      overflow: TextOverflow.ellipsis,
                                    )),
                        ),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                );
              }
            }),
        SizedBox(height: 16.h),
        Text("referral_code_optional".tr,
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        CustomTextField(
          controller: refererController,
          hintText: "e_g_55".tr,
          labelText: "",
        ),
        /*
        Text("Kitty Banner (optional)",
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),

        //Add banner
        GestureDetector(
          onTap: () async {
            if (!_eventsController.isUploading.value) {
              final result = await FilePicker.platform.pickFiles(
                allowMultiple: false,
                type: FileType.image,
              );

              if (result != null && result.files.isNotEmpty) {
                final file = result.xFiles.first.path;
                kittyController.bannerList.add({"name": file});
                final url = await _eventsController.uploadFile(
                    path: file,
                    fileName:
                        "${DateTime.now().millisecondsSinceEpoch}_${file.split(RegExp(r'[/\\]')).last}");
                kittyController.kittyMedia.add({'url': url, 'type': "image"});
              } else {
                ToastUtils.showToast('Nothing picked');
              }
            } else {
              ToastUtils.showToast(
                  'Please wait for the file to upload before uploading another!');
            }
          },
          child: Container(
            height: 55.h,
            padding: const EdgeInsets.all(8),
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white70,
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Upload Kitty Banner',
                  style: TextStyle(fontSize: 14.spMin),
                ),
                SizedBox(width: 8.w),
                const Icon(Icons.upload_file),
              ],
            ),
          ),
        ),
        SizedBox(
            height: 100,
            child: Obx(
              () => ListView.builder(
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: kittyController.bannerList.length,
                  itemBuilder: (context, index) {
                    return Obx(
                      () => Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Image.file(
                              File(
                                  "${kittyController.bannerList[index]['name']}"),
                              height: 80,
                              width: 80,
                              fit: BoxFit.cover,
                            ),
                            index == kittyController.bannerList.length - 1
                                ? _eventsController.isUploading.value
                                    ? const CircularProgressIndicator.adaptive()
                                    : Positioned(
                                        child: IconButton(
                                        icon: const Icon(Icons.delete_outline),
                                        onPressed: () {
                                          kittyController.bannerList
                                              .removeAt(index);
                                        },
                                      ))
                                : Positioned(
                                    child: IconButton(
                                    icon: const Icon(Icons.delete_outline),
                                    onPressed: () {
                                      kittyController.bannerList
                                          .removeAt(index);
                                    },
                                  ))
                          ],
                        ),
                      ),
                    );
                  }),
            )),
    */
      ],
    );
  }

  Widget buildStepTwo() {
    final screenSize = MediaQuery.of(context).size;
    return Column(
      children: [
        SingleLineRow(
          text: "phone_number".tr,
          popup: KtStrings.phone,
        ),
        CustomInternationalPhoneInput(
           onInputChanged: (PhoneNumber number) {
            myPhone = number.phoneNumber.toString().replaceAll("+", '');
            _beneficiaryNumberController.text = _mobilenumberController.text;
          },
          controller: _mobilenumberController, 
        ),
        SingleLineRow(
          text: "beneficiary_payment_channel".tr,
          popup: KtStrings.benfChannel,
        ),
        DefaultTabController(
            length: paymentChannel.paymentGateways().length,
            child: Column(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 2, vertical: 5),
                  decoration: BoxDecoration(
                    color: AppColors.slate,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: TabBar(
                      controller: tabController,
                      physics: const ClampingScrollPhysics(),
                      unselectedLabelColor: Colors.black,
                      labelColor: Theme.of(context).primaryColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      indicator: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.white),
                      tabs: List.generate(
                          paymentChannel.paymentGateways().length, (index) {
                        return Tab(
                          child: AutoSizeText(paymentChannel.paymentGateways()[index]),
                        );
                      })),
                ),
                SizedBox(
                  height: screenSize.height * 0.32.h,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: TabBarView(
                          controller: tabController,
                          children: [
                            if(paymentChannel.supportsMobile.value)
                            MobileMobile(
                              phoneController: _beneficiaryNumberController,
                              benPhone: benPhone,
                              date: dateController,
                              time: timeController,
                              paymentChannelsBuilder: PaymentChannelsBuilder(
                                selectedChannel: selectedChannel ?? "",
                                onChange: (String? value) {
                                  setState(() {
                                    selectedChannel = value;
                                  });
                                },
                              ),
                              onInputChanged: (num) {
                                // Use a post-frame callback to avoid calling setState during build
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  setState(() {
                                    benPhone = num.phoneNumber!;
                                  });
                                });
                              },
                            ),
                            if(paymentChannel.supportsPaybill.value)
                            PayBill(
                              paybillController: accountController,
                              accountController: accountrefController,
                              dateController: dateController,
                              timeController: timeController,
                            ),
                            if(paymentChannel.supportsTill.value)
                            TillPage(
                              tillController: accountController,
                              date: dateController,
                              time: timeController,
                            ),
                            if(paymentChannel.supportBanks.value)
                            SingleChildScrollView(
                              child: Column(
                                children: [
                                  BankTab(
                                    accountNumber: accountController,
                                  ),
                                  SingleLineRow(
                                    text: "expected_contribution_end_date".tr,
                                    popup: KtStrings.endDateInfo,
                                  ),
                                  DatePicker(
                                    date: dateController,
                                    time: timeController,
                                    isAllow: true,
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ))
      ],
    );
  }

  String cleanPhoneNumber(String phoneNumber) {
    return phoneNumber
        .trim()
        .replaceAll(" ", "")
        .replaceAll("-", "")
        .replaceAll("+", "");
  }

  createKitty() async {
    print("Tab index ${tabController.index}");
    if (dateController.text.trim().isEmpty &&
        timeController.text.trim().isEmpty) {
      ToastUtils.showErrorToast(context, "pick_end_date".tr, "error_label".tr);
      return;
    }

    if (formKey.currentState!.validate()) {
      int settlementType = 0;
      String account = '';
      String accountref = '';
      String beneficiaryChannel = '63902';
      String beneficiaryPhoneNumber = '';
      String? userId = kittyController.user.value.iD?.toString();
      switch (tabController.index) {
        case 1:
          settlementType = 2;
          if (accountController.text.trim().isEmpty ||
              accountrefController.text.trim().isEmpty) {
            ToastUtils.showErrorToast(
                context, "enter_required_fields_paybill".tr, "error_label".tr);
            return;
          } else {
            account = accountController.text.trim();
            accountref = accountrefController.text.trim();
            beneficiaryPhoneNumber = cleanPhoneNumber(myPhone);
            beneficiaryChannel = beneficiaryChannel;
          }
          break;
        case 2:
          settlementType = 1;
          if (accountController.text.trim().isEmpty) {
            ToastUtils.showErrorToast(
                context, "enter_till_number".tr, "error_label".tr);
            return;
          } else {
            account = accountController.text.trim();
            beneficiaryPhoneNumber = cleanPhoneNumber(myPhone);
            beneficiaryChannel = beneficiaryChannel;
          }
        case 3:
          settlementType = 3;
          if (accountController.text.trim().isEmpty) {
            ToastUtils.showErrorToast(
                context, "enter_bank_account_number".tr, "error_label".tr);
            return;
          } else {
            beneficiaryChannel =
                Get.put(ChamaDataController()).channel.toString();
            account = accountController.text.trim();
            beneficiaryPhoneNumber = cleanPhoneNumber(myPhone);
          }

        default:
          settlementType = 0;
          account = cleanPhoneNumber(benPhone);
          beneficiaryPhoneNumber = cleanPhoneNumber(benPhone);
      }

      // Get the date and time from the text fields
      final date = dateController.text;
      final time = timeController.text;

      // Combine the date and time into a single DateTime object
      final combinedDateTime = combineDateTime(date, time);

      // Format the combined DateTime object according to the backend's expected format
      final formattedDateTime = formatDateTime(combinedDateTime);
      final dateTime = DateTime.tryParse(formattedDateTime);
      // Now you can send the formattedDateTime to the backend
      // Example:
      // backendService.sendData(formattedDateTime);
      CreateKitPayload request = CreateKitPayload(
          id: kittyId,
          whatsappLink: linkController.text.trim(),
          target: double.tryParse(targetController.text),
          limit: int.tryParse(targetController.text),
          title: kittyNameController.text.trim(),
          description: quilltoHtml(desController),
          beneficiaryChannel: beneficiaryChannel,
          beneficiaryAccount: account,
          beneficiaryAccountRef: accountref,
          beneficiaryPhoneNumber: beneficiaryPhoneNumber,
          phoneNumber: myPhone
              .trim()
              .replaceAll(" ", "")
              .replaceAll("+", "")
              .replaceAll("-", ""),
          endDate: dateTime,
          refererMerchantCode: int.tryParse(refererController.text.trim()),
          userId: userId,
          settlementType: settlementType,
          categories: kittyController.selectedKittyCategories.isNotEmpty
              ? kittyController.selectedKittyCategories.toList()
              : null,
          media: Get.find<ContributeController>()
              .kittyMedia
              .map((e) => Media(url: e.url, type: e.type))
              .toList());

      if (request.description?.isEmpty ?? true) {
        request.description = request.title;
      }
      bool res = await kittyController.createKitty(payload: request);

      if (res) {
        kittyController.selectedKittyCategoryModels.clear();
        kittyController.selectedKittyCategories.clear();
        Get.find<GlobalControllers>().clearCode();
        if (!mounted) return;
        Snack.show(res, kittyController.apiMessage.string);
        if (kittyController.whtsappStatus.isFalse) {
          Snack.show(res, kittyController.whatsappApiMessage.string);
          Get.to(
            () => ErrorPage(
              link: linkController.text,
              reasons: kittyController.reasons.string,
              messages: kittyController.whatsappApiMessage.string,
              whatsAppNo: kittyController.whatsappnumber.string,
            ),
          );
        } else if (kittyController.whtsappStatus.isTrue) {
          Snack.show(res, kittyController.whatsappApiMessage.string);
          Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                  builder: (context) => SucessPage(
                        text: "created",
                        kittyName: kittyNameController.text.trim(),
                      )),
              (route) => route.isFirst);
        }
      } else {
        if (!mounted) return;
        Snack.show(res, kittyController.apiMessage.string);
      }
    } else {
      ToastUtils.showErrorToast(
          context, "fill_in_all_values".tr, "error_label".tr);
    }
    box.write("whatsapplink", linkController.text);
    box.write("phoneNumber", phoneController.text);
  }

  void _showCategoryBottomSheet(
      BuildContext context, KittyController controller) {
    final searchController = TextEditingController();
    controller.filterCategories('');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              'select_categories_placeholder'.tr,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: 'search_categories'.tr,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              onChanged: (value) => controller.filterCategories(value),
            ),
            SizedBox(height: 16.h),
            Expanded(
              child: GetBuilder<KittyController>(
                builder: (controller) => ListView.builder(
                  itemCount: controller.filteredKittyCategories.length,
                  itemBuilder: (context, index) {
                    final category = controller.filteredKittyCategories[index];
                    final isSelected = controller.selectedKittyCategoryModels
                        .any((c) => c.id == category.id);
                    final canSelect =
                        controller.selectedKittyCategoryModels.length < 3;

                    return CheckboxListTile(
                      title: Text(category.name ?? ''),
                      value: isSelected,
                      onChanged: (canSelect || isSelected)
                          ? (value) {
                              controller.toggleCategorySelection(category);
                            }
                          : null,
                      controlAffinity: ListTileControlAffinity.leading,
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 16.h),
            SizedBox(
              width: double.infinity,
              child: MyButton(
                onClick: () => Navigator.pop(context),
                label: 'done_button'.tr,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
