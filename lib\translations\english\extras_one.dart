const extraOnes = {
  
  'member_penalties_title': 'Member Penalties',
  'penalties_title': 'Penalties',
  'penalties_description':
      'Here are penalties which have been set by chama admin',
  'add_penalty': 'Add Penalty',
  'update_member_details_title': 'Update Member Details',
  'amount_label': 'Amount',
  'submit_report': 'Submit Report',

  'select_group_contacts': 'Select Group Contacts',
  'you_cannot_add_yourself': 'You cannot add yourself',
  'duplicate_member_found': 'Duplicate member found',
  'add_member': 'Add Member',
  'edit_beneficiary_payment_details': 'Edit Beneficiary Payment Details',
  'amount_to_receive': 'Amount To receive',
  'beneficiary_member_details': 'Beneficiary member details',
  'at_receiving_order': 'At receiving order:',
  'till_number': 'Till Number',
  'paybill_number': 'PayBill number',
  'till_number_cannot_contain_alphabets':
      'Till number can not contain Alphabets',
  'enter_paybill_number': 'Enter Paybill Number',
  'paybill_number_can_only_contain_letters_and_numbers':
      'Paybill number can only contain letters and numbers',
  'enter_account_number': 'Enter Account Number',
  'chama_members': 'Chama Members',
  'hold_and_drag_member':
      'Hold and Drag the member to set their contribution receiving order',
  'shuffle_members': 'Shuffle Members',
  'save_order': 'Save Order',
  'penalise_multiple_members': 'Penalise multiple members',
  'kitty_not_found_message':
      'The kitty you are looking for could not be found.',
  'penalise_multiple_members_description':
      'Penalise multiple members by just one click',
  'select_all_members': 'Select All Members',
  'no_members_found': 'No members found',

  // Manage delegates translations
  'invite_member': 'Invite Member',
  'last_name_required': 'Please enter a last name',
  'signatory_tooltip':
      'A signatory is a person authorized to approve financial transactions on behalf of the members.',
  'invite': 'Invite',
  'admin_lock': 'Admin Lock',
  'admin_lock_info':
      'Once you lock an admin, they cannot be modified or removed without contacting customer support. This is a security feature to prevent unauthorized changes by admin accounts.',
  'admin_lock_confirm':
      'Once you lock an admin, they cannot be modified or removed without contacting customer support. This is a security feature to prevent unauthorized changes by admin accounts. Are you sure you want to proceed?',
  'lock_admin': 'Lock Admin',
  'operators': 'operators',
  'operator': 'operator',
  'operators_management_click':
      'Click on any {type} operator to edit their details',
  'no_operators_found': 'No operators found',
  'confirm_delete_operator': 'Are you sure you want to delete this operator?',
  'penalise': 'Penalise',
  'choose_penalty_type':
      'Choose the type of penalty from the options below, you can also edit the amount to penalise',
  'feel_free_to_edit_penalty':
      'Feel free to edit the penalty details, you can also press cancel if you don\'t want to edit',
  'enter_reason_for_penalty': 'Enter reason for penalty',

  // Ticket management translations

  'no_tickets_found': 'No tickets found',
  'click_here': 'Click here',
  'to_add_one': 'to add one',
  'discard_changes': 'Discard Changes',
  'confirm_discard_changes': 'Are you sure you want to discard your changes?',

  'discard': 'Discard',

  'expires_today': 'expires today',
  'expires_in': 'expires in',

  'ticket_description': 'Ticket Description',
  'ticket_type': 'Ticket Type',
  'group_size': 'Group Size',
  'size': 'Size',
  'slots_available': 'slots available',
  'limited_slots': 'Limited slots',
  'unlimited_slots': 'Unlimited slots',
  'slots_is_required': 'Slots is required',
  'slots_available_title': 'Slots Available',
  'eg_100': 'eg. 100',
  'price': 'Price',
  'price_is_required': 'Price is required',
  'purchase_start_date_time': 'Purchase Start Date and Time',
  'purchase_start_date_time_required':
      'Purchase Start Date and Time is required',
  'purchase_end_date_time': 'Purchase End Date and Time',
  'purchase_end_date_required': 'Purchase End Date is required',
  'delete_ticket': 'Delete Ticket',
  'delete_ticket_confirmation': 'Are you sure you want to delete this ticket?',

  'edit_ticket': 'Edit Ticket',
  'add_ticket': 'Add Ticket',
  'ticket_description_required': 'Ticket Description is required',
  'slots_must_be_positive': 'Slots must be a positive number',
  'price_must_be_positive': 'Price must be a positive number',
  'kindly_pick_at_least_one_member': 'Kindly pick at least one member',
  'pick_penalty_to_assign': 'Pick a penalty to assign from the ones below',
  'kindly_give_a_reason': 'Kindly give a reason',
  'update_documents': 'Update Documents',
  'document_title': 'Document title',
  'enter_document_description': 'Enter Document description',
  'document_title_must_be_between_5_and_300':
      'Document title must be between 5 and 300',
  'upload_documents': 'Upload Documents',
  'enter_document_title': 'Enter document title',
  'chama_guidelines_example': 'e.g Chama Guidelines',
  'our_rules_and_guidelines_example': 'e.g Our rules and Guidelines',
  'document': 'Document',
  'choose_file': 'Choose File',
  'file_size_limit_20mb': 'File size limit is 20MB',
  'file_size_exceeds_limit': 'File size exceeds the limit of 20 MB.',
  'could_not_determine_file_type': 'Could not determine the file type',
  'unsupported_file_type': 'Unsupported file type',
  'file_upload_failed': 'File upload failed: {error}',
  'edit_chama_settings': 'Edit Chama Settings',
  'chama_settings_description':
      'Chama settings are set upon creating a chama but you can edit for smooth running of your chama',
  'enter_number_of_beneficiaries_per_cycle':
      'Enter number of beneficiaries per cycle',
  'beneficiaries_percentage_value':
      '{value}% of the Chama Balance will be sent to the Beneficiary(ies)',
  'signature_threshhold': 'Signature threshhold',
  'enter_number_of_sign_signatories': 'Enter number of sign signatories',
  'select_signatories': 'Select Signatories',
  'add_signatories_to_chama_group': 'Add signatories to the chama group',
  'select_a_notification_type': 'Select a notification type',
  'member_not_found': 'Member not found',
  'signatory_approval_transactions': 'Signatory Approval Transactions',
  'no_transactions_added_to_chama': 'No transactions added to this chama',
  'processed': 'Processed',
  'approved_transactions_shown_here':
      'Approved transactions will be shown here',
  'transfer_mode': 'TRANSFER MODE:',
  'initiated_by': 'INITIATED BY:',
  'response': 'RESPONSE: ',
  'read_more': 'Read more',
  'decline': 'DECLINE',
  'approve': 'APPROVE',
  'kindly_fill_out_field':
      'Kindly fill out the field to complete this transaction',
  'confirm_to_decline_transaction': 'Confirm to decline this transaction',
  'comment': 'Comment',
  'ok': 'OK',
  'you_need_to_authenticate': 'You need to authenticate to make this operation',
  'oops': 'Oops',
  'enter_all_required_fields_for_paybill':
      'Enter all required fields for Paybill',
  'insufficient_kitty_balance': 'Insufficient kitty balance',
  'channel': 'Channel',
  'processing_fees': 'Processing Fees',
  'you_need_to_be_authenticated':
      'You need to be authenticated to perform this operation.',
  'error_loading_data': 'Error loading data',
  'loading_ellipsis': 'loading...',
  'next_cycle_in': 'Next cycle in',
  'deadline': 'Deadline',
  'select_the_kitty_type': 'Select the Kitty Type',
  'general_contribution_kitty': 'General Contribution Kitty',
  'create_chama': 'Create Chama',
  'create_event_ticketing': 'Create Event-ticketing',
  'next_in': 'Next in ',
  'update_meeting': 'Update Meeting',
  'add_meeting_description': 'Here you can add meetings for your chama members',
  'meeting_information': 'Meeting Information',
  'meeting_title': 'Meeting Title',
  'meeting_title_hint': 'e.g Monthly Meeting',
  'meeting_description': 'Meeting Description',
  'meeting_description_hint': 'e.g Discuss monthly contributions',
  'start_time': 'Start Time',
  'end_time': 'End Time',
  'event_type': 'Event Type',
  'select_event_type_required': 'Select Event Type (Required)',

  'meeting_link': 'Meeting Link',
  'enter_meeting_link': 'Enter Meeting Link',
  'meeting_link_hint': 'e.g https://zoom.us/j/123456789',
  'meeting_venue': 'Meeting Venue',
  'venue': 'Venue',
  'venue_hint': 'e.g Community Hall',
  'location_tip': 'Location Tip',
  'location_tip_hint': 'e.g Near the market',
  'meeting_frequency_question': 'How often will this meeting be held?',
  'meeting_frequency_hint': 'e.g Weekly',
  'please_select_frequency': 'Please select frequency',
  'please_select_event_type': 'Please select event type',
  'please_select_meeting_frequency': 'Please select meeting frequency',
  'please_enter_valid_dates_times': 'Please enter valid dates and times',
  'start_date_before_end_date': 'Start date must be before end date',
  'invalid_kitty_id': 'Invalid kitty ID',
  'edit_chama': 'Edit Chama',
  'settings': 'Settings',
  'signatory_approvals': 'Signatory\nApprovals',
  'update_chama_details': 'Update Chama Details',
  'update_chama_name': 'Update Chama Name',
  'chama_name_length_validation': 'Chama Name must be between 5 and 300',
  'update_frequency': 'Update Frequency',
  'frequency': 'Frequency',
  'contribution_amount': 'Contribution Amount',
  'update_chama_deadline': 'Update Chama Deadline',
  'enter_valid_number': 'Enter a valid number',
  'duplicates_found': 'Duplicates found',
  'failed_to_add_member': 'Failed to add member',

};