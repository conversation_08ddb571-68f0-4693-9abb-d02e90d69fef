import 'package:onekitty/configs/country_specifics.dart';

const extrasFour = {
  
  'kindly_pick_end_date': 'Kindly pick an end date',
  'select_end_date_15_minutes':
      'Please select an end date at least 15 minutes away',
  'edit_kitty_details': 'Edit Kitty Details',
  'details_tab': 'Details',
  'settings_tab': 'Settings',
  'enter_kitty_name_label': 'Enter Kitty name',
  'update_kitty_name_hint': 'Update kitty name',
  'enter_kitty_title_validation': 'Enter kitty title',
  'kitty_name_length_validation_edit': 'Kitty Name must be between 5 and 300',
  'target_amount_tooltip': 'The amount you want to raise for the kitty',
  'amount_hint_example': 'e.g 10000',
  'select_category_tooltip': 'Select a category for your kitty',
  'select_categories_label': 'Select Categories',
  'media_tooltip': 'The media will be displayed on the kitty profile',
  'confirm_deletion_title': 'Confirm Deletion',
  'delete_photo_confirmation': 'Are you sure you want to delete this Photo?',
  'cancel_button': 'Cancel',
  'delete_button': 'Delete',
  'update_details_button': 'Update Details',
  'search_categories_placeholder': 'Search categories...',
  'enter_whatsapp_link_hint': 'Enter Whatsapp link',
  'provide_link': 'Provide a link',
  'unnamed_kitty': 'Unnamed Kitty',

  'unnamed_account': 'Unnamed Account',
  'view_all': 'View All',
  'edit_end_date': 'Edit End Date',
  'edit_details': 'Edit Details',
  'delegates': 'Delegates',
  'export_transactions': 'Export Transactions',
  'fetching_transactions': 'Fetching Transactions...',
  'beneficiary_details': 'Beneficiary Details',
  'beneficiary_name': 'Beneficiary Name',
  'account_number_reference': 'Account Number Reference',
  'withdrawal_successful': 'Withdrawal successful',
  'withdrawal_request_submitted_successfully':
      'Your withdrawal request has been submitted successfully. The transaction requires verification by signatories before the transfer can be completed.',
  'amount_withdrawn': 'Amount withdrawn',
  'view_signatories': 'View Signatories',
  'kitty_transactions': 'Kitty Transactions',
  'view_transaction_details': 'View Transaction Details',
  'select_beneficiary': 'Select Beneficiary',
  'add_new_beneficiary': 'Add New Beneficiary',
  'withdraw_request': 'Withdraw Request',
  'receiver_details': 'Receiver Details',
  'transaction_description': 'Transaction Description',
  'enter_remarks_optional': 'Enter remarks (optional)',
  'remarks': 'Remarks',
  'user_not_authenticated': 'User is not authenticated',
  'home': 'Home',
  'widgets_to_be_displayed': 'Widgets to be displayed',
  'select_groups': 'Select Groups',
  'no_groups_found': 'No groups found',
  'total_members': 'total members',
  'add_contacts_receive_message':
      'Add the contacts that should receive the message',
  'message_summary': 'Message Summary',
  'number_of_messages': 'Number of messages',
  'character_count': 'character Count',
  'sms_will_charge': 'SMS will Charge',
  'contacts_excel_info': 'Contacts Excel Info',
  'how_to_create_excel_sheet_contacts':
      'How to Create an Excel Sheet with Contacts',
  'save_sample_excel_downloads': 'Save Sample Excel to Downloads',
  'message_sent': 'Message sent',
  'error_getting_recipients': 'Error while getting recipients',
  'found_some_issue': 'Found some issue',
  'total_contacts': 'Total Contacts',
  'messages_cost_per_character':
      'Messages cost 0.8 ${CountryConfig.getCurrencyCode} per 160 characters',
  'message_empty_add_messages': 'Message is Empty, Add messages',
  'show_more': 'show more',
  'show_less': 'show less',
  'please_top_up_send_message': 'Please top up to send the message',
  'message': 'Message',
  'est_cost': 'Est. cost:',
  'message_hint': 'e.g Hello, prepare for a meeting...',
  'add_recipients': 'Add recipients',
  'enter_number_continue': 'Enter a number to continue',
  'today': 'Today',
  'yesterday': 'Yesterday',
  'at': 'at',
  'opening': 'Opening...',
  'open_signatory_approvals': 'Open Signatory Approvals',
  'view_transaction': 'View Transaction',
  'view_kitty_details': 'View Kitty Details',
  'failed_to_load_kitty_details': 'Failed to load kitty details',
  'notification_details': 'Notification Details',
  'kitty_id': 'Kitty ID',
  'delete_notification': 'Delete Notification',
  'are_you_sure_delete_notification':
      'Are you sure you want to delete this notification?',
  'no_notifications': 'No Notifications',
  'no_notifications_message':
      'You\'re all caught up! Check back later\nfor new updates.',
  'failed_to_update_transaction': 'Failed to update transaction',
  'event_ticket': 'Event Ticket',
  'quantity': 'Quantity',
  'ticket': 'Ticket',
  'total_amount': 'Total Amount',
  'thank_you_for_using_onekitty': 'Thank you for using OneKitty',
  'visit_us_at': 'Visit us at',
  'onekitty_transaction_receipt': 'OneKitty Transaction Receipt',
  'receipt_no': 'Receipt No',
  'customer': 'Customer',
  'anonymous': 'Anonymous',
  'generated': 'Generated',
  'event_link': 'Event Link',
  'transaction_statement': 'Transaction Statement',
  'total_in': 'Total In',
  'net_amount': 'Net Amount',
  'receipt_details': 'Receipt Details',
  'date_time': 'Date & Time',
  'customer_information': 'Customer Information',
  'customer_name': 'Customer Name',
  'kitty_group': 'Kitty Group',
  'transaction_type': 'Transaction Type',
  'scan_barcode_verification': 'Scan this barcode for verification',
  'thank_you_for_choosing_onekitty': 'Thank you for choosing OneKitty',
  'trusted_financial_companion': 'Your trusted financial companion',
  'onekitty': 'OneKitty',
  'to_enable_deep_linking_follow_steps':
      'To enable deep linking, please follow these steps:',
  'click_add_links_button': '1. Click on "Add links" button shown above',
  'select_all_links_onekitty':
      '2. Select all links associated with OneKitty from the list',
  'dont_show_this_again': 'Don\'t show this again',
  'continue_button': 'Continue',
  'kyc_documents_submitted_successfully':
      'KYC Documents Submitted Successfully!',
  'documents_being_reviewed':
      'Your documents are being reviewed. You will be notified once verification is complete.',
  'continue_to_app': 'Continue to App',
  
};