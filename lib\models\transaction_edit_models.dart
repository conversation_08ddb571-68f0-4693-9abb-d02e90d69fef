// Transaction Edit Models
// Models for handling transaction editing functionality across all transaction types

import 'dart:convert';

import 'package:onekitty/models/transaction_model.dart'; 

/// Request model for transaction edit API calls
/// Used to structure the data sent to the transaction-details endpoint
class TransactionEditRequest {
  final String internalId;
  final String? newFirstName;
  final String? newSecondName;
  final String? newPaymentRef;
  final bool? showNames;
  final String reason;

  TransactionEditRequest({
    required this.internalId,
    this.newFirstName,
    this.newSecondName,
    this.newPaymentRef,
    this.showNames,
    this.reason = "user request",
  });

  /// Create TransactionEditRequest from JSON
  factory TransactionEditRequest.fromJson(Map<String, dynamic> json) {
    return TransactionEditRequest(
      internalId: json['internal_id'] as String,
      newFirstName: json['new_first_name'] as String?,
      newSecondName: json['new_second_name'] as String?,
      newPaymentRef: json['new_payment_ref'] as String?,
      showNames: json['show_names'] as bool?,
      reason: json['reason'] as String? ?? "user request",
    );
  }

  /// Convert TransactionEditRequest to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'internal_id': internalId,
      'reason': reason,
    };

    // Only include fields that are not null
    if (newFirstName != null) {
      data['new_first_name'] = newFirstName;
    }
    if (newSecondName != null) {
      data['new_second_name'] = newSecondName;
    }
    if (newPaymentRef != null) {
      data['new_payment_ref'] = newPaymentRef;
    }
    if (showNames != null) {
      data['show_names'] = showNames;
    }

    return data;
  }

  /// Create a copy of this request with updated fields
  TransactionEditRequest copyWith({
    String? internalId,
    String? newFirstName,
    String? newSecondName,
    String? newPaymentRef,
    bool? showNames,
    String? reason,
  }) {
    return TransactionEditRequest(
      internalId: internalId ?? this.internalId,
      newFirstName: newFirstName ?? this.newFirstName,
      newSecondName: newSecondName ?? this.newSecondName,
      newPaymentRef: newPaymentRef ?? this.newPaymentRef,
      showNames: showNames ?? this.showNames,
      reason: reason ?? this.reason,
    );
  }

  @override
  String toString() {
    return 'TransactionEditRequest(internalId: $internalId, newFirstName: $newFirstName, newSecondName: $newSecondName, newPaymentRef: $newPaymentRef, showNames: $showNames, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionEditRequest &&
        other.internalId == internalId &&
        other.newFirstName == newFirstName &&
        other.newSecondName == newSecondName &&
        other.newPaymentRef == newPaymentRef &&
        other.showNames == showNames &&
        other.reason == reason;
  }

  @override
  int get hashCode {
    return internalId.hashCode ^
        newFirstName.hashCode ^
        newSecondName.hashCode ^
        newPaymentRef.hashCode ^
        showNames.hashCode ^
        reason.hashCode;
  }
}

/// Form data model for managing transaction edit form state
/// Used to handle form inputs and validation
class TransactionEditFormData {
  String firstName;
  String secondName;
  String? paymentRef; // Employee ID or payment reference
  bool? showNames; // Only for kitty admin

  TransactionEditFormData({
    required this.firstName,
    required this.secondName,
    this.paymentRef,
    this.showNames,
  });

  /// Create TransactionEditFormData from JSON
  factory TransactionEditFormData.fromJson(Map<String, dynamic> json) {
    return TransactionEditFormData(
      firstName: json['first_name'] as String,
      secondName: json['second_name'] as String,
      paymentRef: json['payment_ref'] as String?,
      showNames: json['show_names'] as bool?,
    );
  }

  /// Convert TransactionEditFormData to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'first_name': firstName,
      'second_name': secondName,
    };

    if (paymentRef != null && paymentRef!.isNotEmpty) {
      data['payment_ref'] = paymentRef;
    }
    if (showNames != null) {
      data['show_names'] = showNames;
    }

    return data;
  }

  /// Create a copy of this form data with updated fields
  TransactionEditFormData copyWith({
    String? firstName,
    String? secondName,
    String? paymentRef,
    bool? showNames,
  }) {
    return TransactionEditFormData(
      firstName: firstName ?? this.firstName,
      secondName: secondName ?? this.secondName,
      paymentRef: paymentRef ?? this.paymentRef,
      showNames: showNames ?? this.showNames,
    );
  }

  /// Validate form data
  Map<String, String> validate() {
    final Map<String, String> errors = {};

    if (firstName.trim().isEmpty) {
      errors['firstName'] = 'First name is required';
    } else if (firstName.trim().length < 2) {
      errors['firstName'] = 'First name must be at least 2 characters';
    } else if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(firstName.trim())) {
      errors['firstName'] = 'First name can only contain letters and spaces';
    }

    if (secondName.trim().isEmpty) {
      errors['secondName'] = 'Second name is required';
    } else if (secondName.trim().length < 2) {
      errors['secondName'] = 'Second name must be at least 2 characters';
    } else if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(secondName.trim())) {
      errors['secondName'] = 'Second name can only contain letters and spaces';
    }

    return errors;
  }

  /// Check if form data is valid
  bool get isValid => validate().isEmpty;

  @override
  String toString() {
    return 'TransactionEditFormData(firstName: $firstName, secondName: $secondName, paymentRef: $paymentRef, showNames: $showNames)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionEditFormData &&
        other.firstName == firstName &&
        other.secondName == secondName &&
        other.paymentRef == paymentRef &&
        other.showNames == showNames;
  }

  @override
  int get hashCode {
    return firstName.hashCode ^
        secondName.hashCode ^
        paymentRef.hashCode ^
        showNames.hashCode;
  }
}

/// Response model for transaction edit API responses
/// Used to handle API response data and errors
class TransactionEditResponse {
  final bool success;
  final String message;
  final dynamic updatedTransaction; // Can be any transaction model type
  final Map<String, String>? errors;
  final int? statusCode;

  TransactionEditResponse({
    required this.success,
    required this.message,
    this.updatedTransaction,
    this.errors,
    this.statusCode,
  });

  /// Create TransactionEditResponse from JSON
  factory TransactionEditResponse.fromJson(Map<String, dynamic> json) {
    return TransactionEditResponse(
      success: json['success'] as bool? ?? json['status'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      updatedTransaction: json['data'] ?? json['transaction'],
      errors: json['errors'] != null
          ? Map<String, String>.from(json['errors'] as Map)
          : null,
      statusCode: json['status_code'] as int?,
    );
  }

  /// Convert TransactionEditResponse to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'success': success,
      'message': message,
    };

    if (updatedTransaction != null) {
      data['data'] = updatedTransaction;
    }
    if (errors != null) {
      data['errors'] = errors;
    }
    if (statusCode != null) {
      data['status_code'] = statusCode;
    }

    return data;
  }

  /// Create a copy of this response with updated fields
  TransactionEditResponse copyWith({
    bool? success,
    String? message,
    dynamic updatedTransaction,
    Map<String, String>? errors,
    int? statusCode,
  }) {
    return TransactionEditResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      updatedTransaction: updatedTransaction ?? this.updatedTransaction,
      errors: errors ?? this.errors,
      statusCode: statusCode ?? this.statusCode,
    );
  }

  /// Check if response has validation errors
  bool get hasValidationErrors => errors != null && errors!.isNotEmpty;

  /// Get specific field error
  String? getFieldError(String fieldName) {
    return errors?[fieldName];
  }

  /// Check if response indicates a network error
  bool get isNetworkError {
    return statusCode == null ||
        statusCode! >= 500 ||
        (statusCode! == 0); // Connection timeout/failure
  }

  /// Check if response indicates unauthorized access
  bool get isUnauthorized => statusCode == 401;

  /// Check if response indicates forbidden access
  bool get isForbidden => statusCode == 403;

  /// Check if response indicates resource not found
  bool get isNotFound => statusCode == 404;

  /// Check if response indicates client error (4xx)
  bool get isClientError =>
      statusCode != null && statusCode! >= 400 && statusCode! < 500;

  /// Check if response indicates server error (5xx)
  bool get isServerError => statusCode != null && statusCode! >= 500;

  @override
  String toString() {
    return 'TransactionEditResponse(success: $success, message: $message, statusCode: $statusCode, hasErrors: $hasValidationErrors)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionEditResponse &&
        other.success == success &&
        other.message == message &&
        other.updatedTransaction == updatedTransaction &&
        other.errors == errors &&
        other.statusCode == statusCode;
  }

  @override
  int get hashCode {
    return success.hashCode ^
        message.hashCode ^
        updatedTransaction.hashCode ^
        errors.hashCode ^
        statusCode.hashCode;
  }
}

/// Utility functions for transaction edit models
class TransactionEditModelUtils {
  /// Check if transaction edit should be available
  /// Only for kitty transactions and admin users
  static bool canEditTransaction(TransactionModel transaction, bool isAdmin) {
    return transaction.isEditable && isAdmin;
  }

  /// Check if transaction is a 'type in' transaction (contribution)
  static bool isTypeInTransaction(TransactionModel transaction) {
    return transaction.typeInOut?.toLowerCase() == 'in';
  }
  /// Convert JSON string to TransactionEditRequest
  static TransactionEditRequest requestFromJsonString(String jsonString) {
    return TransactionEditRequest.fromJson(json.decode(jsonString));
  }

  /// Convert TransactionEditRequest to JSON string
  static String requestToJsonString(TransactionEditRequest request) {
    return json.encode(request.toJson());
  }

  /// Convert JSON string to TransactionEditFormData
  static TransactionEditFormData formDataFromJsonString(String jsonString) {
    return TransactionEditFormData.fromJson(json.decode(jsonString));
  }

  /// Convert TransactionEditFormData to JSON string
  static String formDataToJsonString(TransactionEditFormData formData) {
    return json.encode(formData.toJson());
  }

  /// Convert JSON string to TransactionEditResponse
  static TransactionEditResponse responseFromJsonString(String jsonString) {
    return TransactionEditResponse.fromJson(json.decode(jsonString));
  }

  /// Convert TransactionEditResponse to JSON string
  static String responseToJsonString(TransactionEditResponse response) {
    return json.encode(response.toJson());
  }

  /// Create TransactionEditRequest from form data and transaction
  static TransactionEditRequest createRequestFromFormData({
    required String internalId,
    required TransactionEditFormData formData,
    required TransactionModel transaction,
    String reason = "user request",
  }) {
    // Use existing payment reference from transaction
    String? paymentRef;
    if (transaction.accounNumberRef?.isNotEmpty == true) {
      paymentRef = transaction.accounNumberRef;
    } else if (transaction.transactionCode?.isNotEmpty == true) {
      paymentRef = transaction.transactionCode;
    }

    return TransactionEditRequest(
      internalId: internalId,
      newFirstName: formData.firstName.trim().isNotEmpty
          ? formData.firstName.trim()
          : null,
      newSecondName: formData.secondName.trim().isNotEmpty
          ? formData.secondName.trim()
          : null,
      newPaymentRef: paymentRef,
      showNames: formData.showNames,
      reason: reason,
    );
  }

  /// Create form data from existing transaction data
  static TransactionEditFormData createFormDataFromTransaction({
    required String firstName,
    required String secondName,
    String? paymentRef,
    bool? showNames,
  }) {
    return TransactionEditFormData(
      firstName: firstName,
      secondName: secondName,
      paymentRef: paymentRef,
      showNames: showNames,
    );
  }
}