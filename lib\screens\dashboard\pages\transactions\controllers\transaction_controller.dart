// Unified Transaction Controller
// Manages state and business logic for all transaction types

import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import '../models/transaction_type.dart';
import '../services/transaction_service.dart';

class TransactionController extends GetxController {
  final TransactionService _transactionService = Get.find<TransactionService>();
  final Logger _logger = Get.find<Logger>();

  // Observable properties
  final RxList<TransactionModel> _transactions = <TransactionModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxInt _currentPage = 0.obs; // Start from page 0
  final RxBool _hasMoreData = true.obs;
  final RxInt _totalPages = 1.obs;
  final RxInt _totalTransactions = 0.obs;

  // Filter properties - these will trigger backend API calls
  final RxString _searchFilter = ''.obs;
  final RxString _transactionCodeFilter = ''.obs;
  final RxString _startDateFilter = ''.obs;
  final RxString _endDateFilter = ''.obs;
  final RxString _accountNameFilter = ''.obs;
  final RxString _categoryFilter = ''.obs;

  // Configuration
  late TransactionPageConfig _config;
  final int _pageSize = 20;

  // Getters
  List<TransactionModel> get transactions => _transactions.toList();
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  bool get hasMoreData => _hasMoreData.value;
  TransactionPageConfig get config => _config;
  int get currentPage => _currentPage.value;
  int get totalPages => _totalPages.value;
  int get totalTransactions => _totalTransactions.value;
  final currentKittyId = 0.obs;
  // Filter getters
  String get searchFilter => _searchFilter.value;
  String get transactionCodeFilter => _transactionCodeFilter.value;
  String get startDateFilter => _startDateFilter.value;
  String get endDateFilter => _endDateFilter.value;
  String get accountNameFilter => _accountNameFilter.value;
  String get categoryFilter => _categoryFilter.value;



  /// Initialize controller with configuration
  void initialize(TransactionPageConfig config) {
    _config = config;
    _resetState();
    loadTransactions();
  }

  /// Load transactions based on configuration
  Future<void> loadTransactions({bool refresh = false}) async {
    _logger.i('Loading ${_config.transactionType.name} transactions (refresh: $refresh)');

    if (refresh) {
      _currentPage.value = 0;
      _hasMoreData.value = true;
      _transactions.clear();
    }

    if (_isLoading.value) return;

    _isLoading.value = true;

    try {
      final response = await _fetchTransactionsForType();
      _logger.i('Fetched ${response.transactions.length} new transactions, total pages: ${response.totalPages}');

      _transactions.assignAll(response.transactions);
      _totalPages.value = response.totalPages;
      _hasMoreData.value = _currentPage.value < response.totalPages - 1;

      _logger.i('Current page: ${_currentPage.value}, Total pages: ${_totalPages.value}, Has more: ${_hasMoreData.value}');
    } catch (e) {
      _logger.e('Error loading transactions: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load more transactions (pagination)
  Future<void> loadMoreTransactions() async {
    if (_isLoadingMore.value || !_hasMoreData.value) return;

    _isLoadingMore.value = true;

    try {
      _currentPage.value++;
      final response = await _fetchTransactionsForType();
      _transactions.addAll(response.transactions);
      
      _totalPages.value = response.totalPages;
      _hasMoreData.value = _currentPage.value < response.totalPages - 1;
    } catch (e) {
      _logger.e('Error loading more transactions: $e');
      _currentPage.value--; // Revert page increment on error
    } finally {
      _isLoadingMore.value = false;
    }
  }

  /// Fetch transactions based on type
  Future<TransactionResponse> _fetchTransactionsForType() async {
    // Use currentPage directly since both UI and API now use 0-based indexing
    final apiPage = _currentPage.value;

    switch (_config.transactionType) {
      case TransactionType.user:
        final userController = Get.find<UserKittyController>();
        final user = userController.getLocalUser();
        return await _transactionService.fetchTransactions(
          type: TransactionType.user,
          phoneNumber: user?.phoneNumber,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
          accountName: _accountNameFilter.value.isEmpty ? null : _accountNameFilter.value,
        );

      case TransactionType.kitty:
        // Use TransactionService for consistent filtering support
        return await _transactionService.fetchTransactions(
          type: TransactionType.kitty,
          entityId: _config.entityId,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
          accountName: _accountNameFilter.value.isEmpty ? null : _accountNameFilter.value,
        );

      case TransactionType.chama:
        return await _transactionService.fetchTransactions(
          type: TransactionType.chama,
          entityId: _config.entityId,
          accountNo: _config.accountNo,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
          accountName: _accountNameFilter.value.isEmpty ? null : _accountNameFilter.value,
          category: _categoryFilter.value.isEmpty ? null : _categoryFilter.value,
        );

      case TransactionType.event:
        return await _transactionService.fetchTransactions(
          type: TransactionType.event,
          entityId: _config.entityId,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
          accountName: _accountNameFilter.value.isEmpty ? null : _accountNameFilter.value,
        );
    }
  }

  /// Set search filter and reload transactions
  void setSearchFilter(String query) {
    _logger.i('Setting search filter: $query');
    _searchFilter.value = query;
    loadTransactions(refresh: true);
  }



  /// Set transaction code filter and reload transactions
  void setTransactionCodeFilter(String code) {
    _logger.i('Setting transaction code filter: $code');
    _transactionCodeFilter.value = code;
    loadTransactions(refresh: true);
  }

  /// Set date filters and reload transactions
  void setDateFilters(String? startDate, String? endDate) {
    _logger.i('Setting date filters: startDate=$startDate, endDate=$endDate');
    _startDateFilter.value = startDate ?? '';
    _endDateFilter.value = endDate ?? '';
    loadTransactions(refresh: true);
  }

  /// Set account name filter and reload transactions
  void setAccountNameFilter(String accountName) {
    _logger.i('Setting account name filter: $accountName');
    _accountNameFilter.value = accountName;
    loadTransactions(refresh: true);
  }

  /// Set category filter and reload transactions (chama only)
  void setCategoryFilter(String category) {
    _logger.i('Setting category filter: $category');
    _categoryFilter.value = category;
    loadTransactions(refresh: true);
  }

  /// Clear all filters and reload transactions
  void clearAllFilters() {
    _logger.i('Clearing all filters');
    _searchFilter.value = '';
    _transactionCodeFilter.value = '';
    _startDateFilter.value = '';
    _endDateFilter.value = '';
    _accountNameFilter.value = '';
    _categoryFilter.value = '';
    loadTransactions(refresh: true);
  }

  /// Legacy method for backward compatibility - now uses search filter
  void searchTransactions(String query) {
    setSearchFilter(query);
  }



  /// Go to specific page
  Future<void> goToPage(int page) async {
    if (page < 0 || page == _currentPage.value || _isLoading.value) return;

    _currentPage.value = page;
    await loadTransactions();
  }

  /// Go to next page
  Future<void> nextPage() async {
    if (_hasMoreData.value) {
      await goToPage(_currentPage.value + 1);
    }
  }

  /// Go to previous page
  Future<void> previousPage() async {
    if (_currentPage.value > 0) {
      await goToPage(_currentPage.value - 1);
    }
  }

  /// Reset state
  void _resetState() {
    _transactions.clear();
    _searchFilter.value = '';
    _transactionCodeFilter.value = '';
    _startDateFilter.value = '';
    _endDateFilter.value = '';
    _accountNameFilter.value = '';
    _categoryFilter.value = '';
    _currentPage.value = 0;
    _hasMoreData.value = true;
    _isLoading.value = false;
    _isLoadingMore.value = false;
    _totalPages.value = 1;
    _totalTransactions.value = 0;
  }

  /// Refresh transactions
  Future<void> refreshTransactions() async {
    await loadTransactions(refresh: true);
  }

  /// Get transaction count
  int get transactionCount => _transactions.length;

  /// Check if transactions are empty
  bool get isEmpty {
    return _transactions.isEmpty && !_isLoading.value;
  }

  @override
  void onClose() {
    _resetState();
    super.onClose();
  }
}
