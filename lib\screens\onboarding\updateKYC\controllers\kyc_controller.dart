import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/kyc_service.dart';

/// Production-ready KYC Controller with Firebase fallback
class KYCController extends GetxController {
  final ImagePicker _picker = ImagePicker();
  FirebaseStorage? _storage;
  final Logger _logger = Logger();
  final HttpService apiProvider = Get.find();
  late final KYCDebugService debugService;
  
  // Firebase availability
  RxBool isFirebaseAvailable = false.obs;

  // Observable states
  Rx<File?> frontID = Rx<File?>(null);
  Rx<File?> backID = Rx<File?>(null);
  Rx<File?> selfie = Rx<File?>(null);
  RxBool isUploading = false.obs;
  RxDouble uploadProgress = 0.0.obs;
  RxInt currentStep = 1.obs;
  RxString currentUpload = ''.obs;
  RxString apiMessage = ''.obs;
  RxBool hasError = false.obs;
  
  // Validation states
  RxBool isFrontValid = false.obs;
  RxBool isBackValid = false.obs;
  RxBool isSelfieValid = false.obs;
  RxBool isIdNumberValid = false.obs;

  // Pre-populated ID number from the local user
  final TextEditingController idNumber = TextEditingController();

  // Step management
  final totalSteps = 5.obs;
  RxDouble stepProgress = 0.2.obs;

  @override
  void onInit() {
    super.onInit();
    debugService = Get.put(KYCDebugService());
    _initializeFirebase();
    _initializeIdNumber();
    _setupValidation();
    _logger.i('KYC Controller initialized');
  }
  
  /// Initialize Firebase with fallback
  void _initializeFirebase() async {
    try {
      if (Firebase.apps.isNotEmpty) {
        _storage = FirebaseStorage.instance;
        isFirebaseAvailable.value = true;
        _logger.i('Firebase Storage initialized');
      }
    } catch (e) {
      _logger.w('Firebase not available, using fallback: $e');
      isFirebaseAvailable.value = false;
    }
  }
  
  /// Initialize ID number from user data
  void _initializeIdNumber() {
    try {
      final user = Get.find<Eventcontroller>().getLocalUser();
      final userIdNumber = user?.idNumber?.toString();
      if (userIdNumber != null && userIdNumber != '0' && userIdNumber.isNotEmpty) {
        idNumber.text = userIdNumber;
        _validateIdNumber(userIdNumber);
      }
    } catch (e) {
      _logger.w('Could not initialize ID number: $e');
    }
  }
  
  /// Setup real-time validation
  void _setupValidation() {
    idNumber.addListener(() => _validateIdNumber(idNumber.text));
  }
  
  /// Validate ID number format
  void _validateIdNumber(String value) {
    final trimmed = value.trim();
    final isValid = trimmed.isNotEmpty && 
                   trimmed.length >= 7 && 
                   trimmed.length <= 8 && 
                   RegExp(r'^\d+$').hasMatch(trimmed);
    isIdNumberValid.value = isValid;
  }

  @override
  void onClose() {
    idNumber.dispose();
    super.onClose();
  }

  /// Updates progress based on current step
  void updateProgress(int step) {
    currentStep.value = step;
    stepProgress.value = step / totalSteps.value;
  }

  /// Enhanced image capture with validation
  Future<File?> captureImage(String type) async {
    try {
      hasError.value = false;
      
      if ((type == 'front' || type == 'back') && !isIdNumberValid.value) {
        throw Exception('Please enter a valid ID number first');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 85,
      );

      if (image == null) return null;

      final File imageFile = File(image.path);
      await _validateImageFile(imageFile, type);
      
      switch (type) {
        case 'front':
          frontID.value = imageFile;
          isFrontValid.value = true;
          break;
        case 'back':
          backID.value = imageFile;
          isBackValid.value = true;
          break;
        case 'selfie':
          selfie.value = imageFile;
          isSelfieValid.value = true;
          break;
      }
      
      _logger.i('$type image captured successfully');
      return imageFile;
    } catch (e) {
      _logger.e('Capture error: $e');
      _setError(e.toString().replaceAll('Exception: ', ''));
      return null;
    }
  }
  
  /// Comprehensive image validation
  Future<void> _validateImageFile(File imageFile, String type) async {
    if (!await imageFile.exists()) {
      throw Exception('$type image file not found');
    }

    final fileSizeInBytes = await imageFile.length();
    if (fileSizeInBytes == 0) {
      throw Exception('$type image is empty');
    }

    final fileSizeInMB = fileSizeInBytes / (1024 * 1024);
    if (fileSizeInMB > 5) {
      throw Exception('$type image too large (${fileSizeInMB.toStringAsFixed(1)}MB). Max 5MB.');
    }

    final extension = imageFile.path.toLowerCase().split('.').last;
    if (!['jpg', 'jpeg', 'png'].contains(extension)) {
      throw Exception('$type must be JPG or PNG format');
    }

    // Basic corruption check
    final bytes = await imageFile.readAsBytes();
    if (bytes.length < 10 || !_isValidImageHeader(bytes)) {
      throw Exception('$type image appears corrupted');
    }
  }
  
  /// Validate image headers
  bool _isValidImageHeader(List<int> bytes) {
    if (bytes.length < 4) return false;
    // JPEG: FF D8 FF or PNG: 89 50 4E 47
    return (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) ||
           (bytes.length >= 8 && bytes[0] == 0x89 && bytes[1] == 0x50 && 
            bytes[2] == 0x4E && bytes[3] == 0x47);
  }

  /// Upload with Firebase fallback
  Future<void> uploadImages() async {
    // Prevent concurrent uploads
    if (isUploading.value) {
      _logger.w('Upload already in progress, ignoring duplicate request');
      return;
    }

    try {
      isUploading.value = true;
      uploadProgress.value = 0.0;
      hasError.value = false;

      if (!allDocumentsReady) {
        throw Exception('All images and ID number required');
      }

      // Check network connectivity
      final hasConnection = await _checkNetworkConnectivity();
      if (!hasConnection) {
        throw Exception('No internet connection. Please check your network and try again.');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final userId = Get.find<Eventcontroller>().getLocalUser()?.id ?? 0;

      currentUpload.value = 'Uploading Front ID...';
      final frontUrl = await _uploadWithRetry(
        frontID.value!, 'front_${userId}_$timestamp.jpg');
      uploadProgress.value = 0.33;

      currentUpload.value = 'Uploading Back ID...';
      final backUrl = await _uploadWithRetry(
        backID.value!, 'back_${userId}_$timestamp.jpg');
      uploadProgress.value = 0.66;

      currentUpload.value = 'Uploading Selfie...';
      final selfieUrl = await _uploadWithRetry(
        selfie.value!, 'selfie_${userId}_$timestamp.jpg');
      uploadProgress.value = 0.90;

      currentUpload.value = 'Finalizing...';
      await _updateProfileAPI(frontUrl, backUrl, selfieUrl);
      uploadProgress.value = 1.0;

      Get.snackbar('success'.tr, 'kyc_documents_submitted_successfully'.tr,
        backgroundColor: Colors.green[200]);

      // Clean up temporary files after successful upload
      await _cleanupTempFiles();

      Get.offAll(() => const AlreadySubmittedPage());
    } catch (e) {
      _logger.e('Upload error: $e');
      _setError('Upload failed: ${e.toString().replaceAll('Exception: ', '')}');
    } finally {
      isUploading.value = false;
      currentUpload.value = '';
    }
  }

  /// Upload with retry and fallback
  Future<String> _uploadWithRetry(File image, String filename, {int maxRetries = 3}) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (isFirebaseAvailable.value) {
          return await _uploadToFirebase(image, filename);
        } else {
          return await _uploadToServer(image, filename);
        }
      } catch (e) {
        _logger.w('Upload attempt $attempt failed: $e');
        if (attempt == maxRetries) {
          throw Exception('Upload failed after $maxRetries attempts');
        }
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }
    throw Exception('All upload attempts failed');
  }

  /// Upload to Firebase Storage with progress tracking
  Future<String> _uploadToFirebase(File image, String filename) async {
    if (_storage == null) throw Exception('Firebase Storage not available');

    final userPhone = Get.find<Eventcontroller>().getLocalUser()?.phoneNumber ?? 'unknown';
    final path = kDebugMode ? 'verificationDebug/$userPhone/$filename' : 'verification/$userPhone/$filename';

    final ref = _storage!.ref().child(path);
    final uploadTask = ref.putFile(image);

    // Listen to upload progress (optional - for detailed progress tracking)
    uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
      final progress = snapshot.bytesTransferred / snapshot.totalBytes;
      _logger.d('Firebase upload progress for $filename: ${(progress * 100).toStringAsFixed(1)}%');
    });

    final snapshot = await uploadTask;
    if (snapshot.state != TaskState.success) {
      throw Exception('Firebase upload failed with state: ${snapshot.state}');
    }

    final downloadUrl = await ref.getDownloadURL();
    _logger.i('Firebase upload successful: $downloadUrl');
    return downloadUrl;
  }
  
  /// Fallback server upload using base64 encoding
  Future<String> _uploadToServer(File image, String filename) async {
    try {
      _logger.i('Uploading to server: $filename');

      // Read image as bytes and encode to base64
      final bytes = await image.readAsBytes();
      final base64Image = base64Encode(bytes);

      // Get file extension
      final extension = filename.split('.').last.toLowerCase();
      final mimeType = extension == 'png' ? 'image/png' : 'image/jpeg';

      final response = await apiProvider.request(
        url: ApiUrls.UPLOADFILE,
        method: Method.POST,
        params: {
          'file_data': 'data:$mimeType;base64,$base64Image',
          'filename': filename,
          'type': 'kyc_document',
          'userId': Get.find<Eventcontroller>().getLocalUser()?.id ?? 0,
        },
      );

      if (response.data['status'] == true) {
        final uploadUrl = response.data['data']['url'] as String?;
        if (uploadUrl == null || uploadUrl.isEmpty) {
          throw Exception('Server returned empty upload URL');
        }
        _logger.i('Server upload successful: $uploadUrl');
        return uploadUrl;
      } else {
        throw Exception(response.data['message'] ?? 'Server upload failed');
      }
    } catch (e) {
      _logger.e('Server upload error: $e');
      throw Exception('Server upload failed: ${e.toString()}');
    }
  }

  /// API integration for KYC submission with method selection
  Future<void> _updateProfileAPI(
      String frontUrl, String backUrl, String selfieUrl) async {
    try {
      final user = Get.find<Eventcontroller>().getLocalUser();
      if (user == null) {
        throw Exception('User not found. Please login again.');
      }

      final int kycStatus = user.kycStatus ?? 0;
      final Method method = kycStatus == 2 ? Method.PUT : Method.POST;

      // Validate all required data
      if (frontUrl.isEmpty || backUrl.isEmpty || selfieUrl.isEmpty) {
        throw Exception('All image uploads must be completed before submission');
      }

      if (idNumber.text.trim().isEmpty) {
        throw Exception('ID number is required');
      }

      final response = await apiProvider.request(
        url: ApiUrls.updateKYC,
        method: method,
        params: {
          'userId': user.id ?? 0,
          'front': frontUrl,
          'back': backUrl,
          'selfie': selfieUrl,
          'idNumber': idNumber.text.trim(),
          'identityType': "NATIONAL_ID",
        },
      );

      _logger.i('KYC API Response: ${response.data}');

      if (response.data["status"] ?? false) {
        apiMessage.value = response.data['message'] ?? 'KYC submitted successfully';
      } else {
        final errorMessage = response.data['message'] ?? 'KYC submission failed';
        apiMessage.value = errorMessage;
        throw Exception(errorMessage);
      }
    } catch (e) {
      _logger.e('API Error: $e');
      final errorMessage = e.toString().replaceAll('Exception: ', '');
      apiMessage.value = errorMessage;
      rethrow;
    }
  }

  /// Check if all documents are ready
  bool get allDocumentsReady =>
      isFrontValid.value &&
      isBackValid.value &&
      isSelfieValid.value &&
      isIdNumberValid.value;

  /// Check if all images are captured
  bool get allImagesCaptured =>
      frontID.value != null && backID.value != null && selfie.value != null;
      
  /// Check network connectivity
  Future<bool> _checkNetworkConnectivity() async {
    try {
      // Use a simple HTTP request to check connectivity
      final result = await InternetAddress.lookup('google.com');
      final isConnected = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      _logger.i('Network connectivity: $isConnected');
      return isConnected;
    } catch (e) {
      _logger.w('Connectivity check failed: $e');
      // Return false to show proper error message
      return false;
    }
  }

  /// Clean up temporary files after successful upload
  Future<void> _cleanupTempFiles() async {
    try {
      final filesToCleanup = [frontID.value, backID.value, selfie.value];

      for (final file in filesToCleanup) {
        if (file != null && await file.exists()) {
          await file.delete();
          _logger.i('Cleaned up temp file: ${file.path}');
        }
      }
    } catch (e) {
      _logger.w('Failed to cleanup temp files: $e');
      // Don't throw error as this is not critical
    }
  }

  /// Set error state with enhanced error handling
  void _setError(String message, {String? errorCode, bool isRetryable = true}) {
    hasError.value = true;

    // Log error with additional context
    _logger.e('KYC Error: $message', error: errorCode);

    // Determine error type and provide appropriate user message
    String userMessage = message;
    IconData errorIcon = Icons.error;
    Color backgroundColor = Colors.red[200]!;

    if (message.toLowerCase().contains('network') ||
        message.toLowerCase().contains('internet') ||
        message.toLowerCase().contains('connection')) {
      userMessage = 'Network error. Please check your internet connection and try again.';
      errorIcon = Icons.wifi_off;
      backgroundColor = Colors.orange[200]!;
    } else if (message.toLowerCase().contains('firebase')) {
      userMessage = 'Upload service temporarily unavailable. Please try again.';
      errorIcon = Icons.cloud_off;
    } else if (message.toLowerCase().contains('file') ||
               message.toLowerCase().contains('image')) {
      userMessage = 'Image processing error. Please try capturing the image again.';
      errorIcon = Icons.image_not_supported;
    } else if (message.toLowerCase().contains('server')) {
      userMessage = 'Server error. Please try again in a few moments.';
      errorIcon = Icons.dns;
    }

    Get.snackbar(
      'Upload Error',
      userMessage,
      snackPosition: SnackPosition.bottom,
      backgroundColor: backgroundColor,
      duration: const Duration(seconds: 5),
      icon: Icon(errorIcon, color: Colors.white),
      shouldIconPulse: true,
      mainButton: isRetryable ? TextButton(
        onPressed: () {
          Get.back();
          // Could add retry logic here
        },
        child: const Text('Retry', style: TextStyle(color: Colors.white)),
      ) : null,
    );
  }

  /// Reset controller state
  void resetKYCProcess() {
    frontID.value = null;
    backID.value = null;
    selfie.value = null;
    idNumber.clear();
    
    isFrontValid.value = false;
    isBackValid.value = false;
    isSelfieValid.value = false;
    isIdNumberValid.value = false;
    
    currentStep.value = 1;
    uploadProgress.value = 0.0;
    apiMessage.value = '';
    hasError.value = false;
  }
}

/// KYC submission success page
class AlreadySubmittedPage extends StatelessWidget {
  const AlreadySubmittedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('KYC Submitted'),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle, size: 100, color: Colors.green[600]),
            const SizedBox(height: 20),
            Text('kyc_documents_submitted_successfully'.tr,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold),
              textAlign: TextAlign.center),
            const SizedBox(height: 16),
            Text('documents_being_reviewed'.tr,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () => Get.offAllNamed('/bottom_navigation'),
              child: Text('continue_to_app'.tr)),
          ],
        ),
      ),
    );
  }
}