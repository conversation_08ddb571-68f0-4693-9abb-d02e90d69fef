// Model class for M-PESA transaction response
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/utils/my_button.dart';

class MpesaTransferResponseModel {
  final bool status;
  final String message;
  final String checkoutRequestId;
  final String customerMessage;
  final String detail;
  final String paymentGateway;
  final String responseDescription;

  MpesaTransferResponseModel({
    required this.status,
    required this.message,
    required this.checkoutRequestId,
    required this.customerMessage,
    required this.detail,
    required this.paymentGateway,
    required this.responseDescription,
  });

  factory MpesaTransferResponseModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>;

    return MpesaTransferResponseModel(
      status: json['status'] as bool,
      message: json['message'] as String,
      checkoutRequestId: data['checkout_request_id'] as String,
      customerMessage: data['customer_message'] as String,
      detail: data['detail'] as String,
      paymentGateway: data['payment_gateway'] as String,
      responseDescription: data['response_description'] as String,
    );
  }
}

// Stateless widget for displaying M-PESA transaction dialog
class MPESATransactionWidget extends StatelessWidget {
  final MpesaTransferResponseModel response;

  const MPESATransactionWidget({
    super.key,
    required this.response,
  });

  @override
  Widget build(BuildContext context) {
    final manualMethod = false.obs;
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: IntrinsicHeight(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Status Icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: response.status
                      ? Colors.green.shade50
                      : Colors.red.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  response.status ? Icons.phone_android : Icons.error_outline,
                  size: 48,
                  color: response.status ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 16),

              // Title
              Text(
                response.status ? 'Payment Request Sent' : 'Payment Failed',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Main Message
              Container(
                width: 300.w,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      response.responseDescription,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (!response.customerMessage
                        .toLowerCase()
                        .contains('wait')) ...[
                      const SizedBox(height: 14),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Didn\'t receive the code?  ',
                            style: TextStyle(fontSize: 12),
                          ),
                          TextButton(
                            onPressed: () {
                              manualMethod(true);
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: const Text(
                              'Manual Method',
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      )
                    ] else ...[
                      const SizedBox(height: 8),
                      Text(
                        formatInstructions(response.customerMessage),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      )
                    ],
                    const SizedBox(height: 8),
                    Obx(
                      () => manualMethod.value
                          ? Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Text(
                                formatInstructions(response.customerMessage),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Action Button
              SizedBox(
                width: double.infinity,
                child: MyButton(
                  onClick: () => Navigator.of(context).pop(),
                  label: 'OK',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String formatInstructions(String text) {
    // Replace numbered patterns (digit followed by period) with newline + the pattern
    return text
        .replaceAllMapped(RegExp(r'(\d+\.)'), (match) => '\n${match.group(0)}')
        .trim(); // trim() removes the leading newline from the first item
  }
}
