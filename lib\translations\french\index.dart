import 'common.dart';
import 'categories_filters.dart';
import 'chama_management.dart';
import 'kitty_management.dart';
import 'user_profile.dart';
import 'messaging_communication.dart';
import 'events.dart';
import 'extras_four.dart';
import 'extras_one.dart';
import 'extras_three.dart';
import 'extra_twos.dart';

const Map<String, String> french = {
  ...commonFrench,
  ...categoriesFiltersFrench,
  ...chamaManagementFrench,
  ...kittyManagementFrench,
  ...userProfileFrench,
  ...messagingCommunicationFrench,
  ...eventsFrench,
  ...extrasFourFrench,
  ...extrasOneFrench,
  ...extrasThreeFrench,
  ...extraTwosFrench,
};