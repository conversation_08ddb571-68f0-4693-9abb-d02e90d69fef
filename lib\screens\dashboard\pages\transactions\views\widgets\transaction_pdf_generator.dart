import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/utils/pdf_null_safety_helper.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:flutter/services.dart' show rootBundle;
import '../../../../../../utils/utils_exports.dart';
import '../../models/transaction_type.dart';

Future<Uint8List> generateTransactionsPdf(
  List<TransactionModel> transactions,
  TransactionType transactionType,
  {String? entityTitle}
) async {
  try {
    UserKittyController userController;
    try {
      userController = Get.find<UserKittyController>();
    } catch (e) {
      userController = Get.put(UserKittyController());
    }
 

    final user = userController.getLocalUser();
    final userName = "${user?.firstName ?? ''} ${user?.secondName ?? ''}".trim();
    final userPhone = user?.phoneNumber?.replaceRange(6, 9, "***") ?? '';
    
    final title = entityTitle ?? transactionType.displayName;

    final pdf = Document(
      title: "${title.replaceAll(' ', '_')}_transactions_statement",
      author: "onekitty.co.ke",
      producer: "onekitty.co.ke",
      subject: "$title Transaction Statement",
      theme: ThemeData(
        defaultTextStyle: TextStyle(font: Font.courier()),
      ),
    );

    MemoryImage? imageLogo;
    try {
      final logoData = await rootBundle.load(AssetUrl.logo2);
      imageLogo = MemoryImage(logoData.buffer.asUint8List());
    } catch (e) {
      print('Logo loading failed: $e');
    }

    pdf.addPage(
      MultiPage(
        footer: (Context context) {
          return Padding(
            padding: const EdgeInsets.all(10),
            child: Center(
              child: Text(
                'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
                style: Theme.of(context).header3.copyWith(fontStyle: FontStyle.italic),
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
        header: (Context context) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Text(userName.isNotEmpty ? userName : 'User'),
                  Text(userPhone.isNotEmpty ? userPhone : 'Phone'),
                ],
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
              if (imageLogo != null)
                SizedBox(height: 150, width: 150, child: Image(imageLogo))
            ],
          );
        },
        build: (context) {
          return [
            Padding(
              child: Text(
                "$title ${'transaction_statement'.tr}",
                textAlign: TextAlign.center,
                style: Theme.of(context).header2,
              ),
              padding: const EdgeInsets.all(16),
            ),
            Text(
              "${'generated'.tr}: ${DateFormat('dd MMM yyyy, hh:mm a').format(DateTime.now())}",
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            Table(
              border: TableBorder.all(color: PdfColors.black),
              children: [
                TableRow(
                  children: [
                    _buildHeader('date'.tr),
                    _buildHeader('reference'.tr),
                    _buildHeader('details'.tr),
                    _buildHeader('amount_label'.tr),
                  ],
                ),
                ...transactions.map(_buildRow),
              ],
            ),
            SizedBox(height: 20),
            _buildSummary(transactions),
            Expanded(
              child: Padding(
                child: Text('thank_you_for_using_onekitty'.tr, textAlign: TextAlign.center),
                padding: const EdgeInsets.all(20),
              ),
            ),
          ];
        },
      ),
    );
    return pdf.save();
  } catch (e) {
    print('PDF error: $e');
    rethrow;
  }
}

Widget _buildHeader(String text) {
  return Padding(
    child: Text(text, textAlign: TextAlign.center, style:   TextStyle(fontWeight: FontWeight.bold)),
    padding: const EdgeInsets.all(12),
  );
}

TableRow _buildRow(TransactionModel t) {
  return TableRow(
    decoration: BoxDecoration(
      color: (t.id ?? 0) % 2 == 0 ? PdfColor.fromHex("#F8F9FA") : null,
    ),
    children: [
      _buildCell(PDFNullSafetyHelper.formatDate(t.createdAt)),
      _buildCell(t.transactionCode ?? t.transactionRef ?? 'N/A'),
      _buildCell(PDFNullSafetyHelper.getFullName(t.firstName, t.secondName)),
      _buildAmountCell(t),
    ],
  );
}

Widget _buildCell(String text) {
  return Padding(
    padding: const EdgeInsets.all(8),
    child: Text(text, style: const TextStyle(fontSize: 10)),
  );
}

Widget _buildAmountCell(TransactionModel t) {
  final isPositive = t.typeInOut == "IN" || t.transactionType == "Contribution";
  return Padding(
    padding: const EdgeInsets.all(8),
    child: Text(
      "${isPositive ? '+' : '-'} ${FormattedCurrency.getFormattedCurrency(t.amount)}",
      textAlign: TextAlign.right,
      style: TextStyle(
        fontSize: 10,
        color: isPositive ? PdfColors.green : PdfColors.red,
        fontWeight: FontWeight.bold,
      ),
    ),
  );
}

Widget _buildSummary(List<TransactionModel> transactions) {
  final totalIn = transactions
      .where((t) => t.typeInOut == "IN" || t.transactionType == "Contribution")
      .fold(0.0, (sum, t) => sum + (t.amount ?? 0));
  
  final totalOut = transactions
      .where((t) => t.typeInOut == "OUT" && t.transactionType != "Contribution")
      .fold(0.0, (sum, t) => sum + (t.amount ?? 0));

  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      border: Border.all(color: PdfColors.black),
      color: PdfColor.fromHex("#F0F0F0"),
    ),
    child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("${'total_in'.tr}:", style:   TextStyle(fontWeight: FontWeight.bold)),
            Text(FormattedCurrency.getFormattedCurrency(totalIn), 
                 style: TextStyle(color: PdfColors.green, fontWeight: FontWeight.bold)),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("${'net_amount'.tr}:", style:   TextStyle(fontWeight: FontWeight.bold)),
            Text(FormattedCurrency.getFormattedCurrency(totalIn - totalOut), 
                 style:   TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
      ],
    ),
  );
}