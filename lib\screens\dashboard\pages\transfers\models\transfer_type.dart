// Transfer Type Enum and Configuration
// Defines different types of transfers and their configurations

enum TransferType {
  event,
  chama,
  penalty, // Chama penalty transfers
}

extension TransferTypeExtension on TransferType {
  String get displayName {
    switch (this) {
      case TransferType.event:
        return 'Event Transfer';
      case TransferType.chama:
        return 'Chama Transfer';
      case TransferType.penalty:
        return 'Penalty Transfer';
    }
  }

  String get apiEndpoint {
    switch (this) {
      case TransferType.event:
        return 'transfer/event';
      case TransferType.chama:
        return 'transfer/chama';
      case TransferType.penalty:
        return 'transfer/penalty';
    }
  }

  bool get requiresSignatoryApproval {
    switch (this) {
      case TransferType.event:
        return false; // Direct transfer
      case TransferType.chama:
      case TransferType.penalty:
        return true; // Requires signatory approval
    }
  }

  List<String> get supportedModes {
    return ['WALLET', 'PAYBILL', 'TILL', 'BANK'];
  }
}

/// Configuration class for transfer page parameters
class TransferPageConfig {
  final TransferType transferType;
  final int entityId; // kittyId, chamaId, eventId
  final String? title;
  final bool isPenaltyTransfer;
  final bool showApprovalFlow;

  const TransferPageConfig({
    required this.transferType,
    required this.entityId,
    this.title,
    this.isPenaltyTransfer = false,
    this.showApprovalFlow = true,
  });

  TransferPageConfig copyWith({
    TransferType? transferType,
    int? entityId,
    String? title,
    bool? isPenaltyTransfer,
    bool? showApprovalFlow,
  }) {
    return TransferPageConfig(
      transferType: transferType ?? this.transferType,
      entityId: entityId ?? this.entityId,
      title: title ?? this.title,
      isPenaltyTransfer: isPenaltyTransfer ?? this.isPenaltyTransfer,
      showApprovalFlow: showApprovalFlow ?? this.showApprovalFlow,
    );
  }
}