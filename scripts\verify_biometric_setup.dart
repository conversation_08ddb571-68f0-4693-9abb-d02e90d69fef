#!/usr/bin/env dart

import 'dart:io';

/// <PERSON>ript to verify biometric authentication setup in OneKitty app
/// Run this script to check if all biometric configurations are correct

void main() {
  print('🔍 OneKitty Biometric Authentication Verification');
  print('=' * 50);
  
  bool allChecksPass = true;
  
  // Check 1: iOS Face ID permission
  print('\n📱 Checking iOS Configuration...');
  final iosInfoPlist = File('ios/Runner/Info.plist');
  if (iosInfoPlist.existsSync()) {
    final content = iosInfoPlist.readAsStringSync();
    if (content.contains('NSFaceIDUsageDescription')) {
      print('✅ iOS Face ID permission found');
    } else {
      print('❌ iOS Face ID permission missing');
      allChecksPass = false;
    }
  } else {
    print('⚠️  iOS Info.plist not found');
  }
  
  // Check 2: Android biometric permissions
  print('\n🤖 Checking Android Configuration...');
  final androidManifest = File('android/app/src/main/AndroidManifest.xml');
  if (androidManifest.existsSync()) {
    final content = androidManifest.readAsStringSync();
    
    if (content.contains('USE_FINGERPRINT')) {
      print('✅ Android fingerprint permission found');
    } else {
      print('❌ Android fingerprint permission missing');
      allChecksPass = false;
    }
    
    if (content.contains('USE_BIOMETRIC')) {
      print('✅ Android biometric permission found');
    } else {
      print('❌ Android biometric permission missing');
      allChecksPass = false;
    }
  } else {
    print('⚠️  Android manifest not found');
  }
  
  // Check 3: local_auth dependency
  print('\n📦 Checking Dependencies...');
  final pubspec = File('pubspec.yaml');
  if (pubspec.existsSync()) {
    final content = pubspec.readAsStringSync();
    if (content.contains('local_auth:')) {
      print('✅ local_auth dependency found');
      
      // Extract version
      final lines = content.split('\n');
      for (final line in lines) {
        if (line.trim().startsWith('local_auth:')) {
          print('   Version: ${line.trim()}');
          break;
        }
      }
    } else {
      print('❌ local_auth dependency missing');
      allChecksPass = false;
    }
  } else {
    print('⚠️  pubspec.yaml not found');
  }
  
  // Check 4: Biometric implementation files
  print('\n📄 Checking Implementation Files...');
  final files = [
    'lib/screens/onboarding/login_screen.dart',
    'lib/screens/onboarding/passwd_req_screen.dart',
    'lib/services/auth_manager.dart',
    'lib/utils/cache_keys.dart',
  ];
  
  for (final filePath in files) {
    final file = File(filePath);
    if (file.existsSync()) {
      print('✅ $filePath exists');
    } else {
      print('❌ $filePath missing');
      allChecksPass = false;
    }
  }
  
  // Check 5: Translation files
  print('\n🌐 Checking Translation Files...');
  final translationFiles = [
    'lib/translations/english/events.dart',
    'lib/translations/english/user_profile.dart',
  ];
  
  for (final filePath in translationFiles) {
    final file = File(filePath);
    if (file.existsSync()) {
      final content = file.readAsStringSync();
      if (content.contains('biometric') || content.contains('fingerprint')) {
        print('✅ $filePath contains biometric translations');
      } else {
        print('⚠️  $filePath missing biometric translations');
      }
    } else {
      print('❌ $filePath missing');
      allChecksPass = false;
    }
  }
  
  // Check 6: Test files
  print('\n🧪 Checking Test Files...');
  final testFiles = [
    'test/biometric_test.dart',
    'test/integration_test/biometric_flow_test.dart',
  ];
  
  for (final filePath in testFiles) {
    final file = File(filePath);
    if (file.existsSync()) {
      print('✅ $filePath exists');
    } else {
      print('⚠️  $filePath missing (recommended)');
    }
  }
  
  // Final result
  print('\n' + '=' * 50);
  if (allChecksPass) {
    print('🎉 All critical biometric checks passed!');
    print('✅ Your biometric authentication setup is ready');
  } else {
    print('⚠️  Some critical checks failed');
    print('❌ Please review the issues above');
  }
  
  print('\n📚 For detailed information, see:');
  print('   docs/biometric_authentication_analysis.md');
  
  exit(allChecksPass ? 0 : 1);
}
