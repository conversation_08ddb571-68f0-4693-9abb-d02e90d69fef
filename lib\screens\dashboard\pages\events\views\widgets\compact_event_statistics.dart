import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/event_statistics_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/event_statistics.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/helpers/colors.dart';

/// Compact statistics widget for event organizer view
/// Shows: Total Events, Total Revenue, Tickets Sold, Total Attendees
class CompactEventStatistics extends StatefulWidget {
  final int eventId;

  const CompactEventStatistics({
    super.key,
    required this.eventId,
  });

  @override
  State<CompactEventStatistics> createState() => _CompactEventStatisticsState();
}

class _CompactEventStatisticsState extends State<CompactEventStatistics>
    with SingleTickerProviderStateMixin {
  late final EventStatisticsController controller;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    controller = Get.put(EventStatisticsController());

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _loadStatistics();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    _animationController.reset();
    await controller.fetchEventStatistics(eventId: widget.eventId);

    if (!controller.hasError.value && controller.hasOverviewData) {
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoadingStatistics.value) {
        return _buildLoadingState();
      }

      if (controller.hasError.value || !controller.hasOverviewData) {
        return const SizedBox.shrink();
      }

      return FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: _buildStatisticsRow(),
        ),
      );
    });
  }

  Widget _buildLoadingState() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      padding: EdgeInsets.all(16.spMin),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Colors.grey.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20.spMin,
            height: 20.spMin,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
            ),
          ),
          SizedBox(width: 12.w),
          Text(
            'loading_statistics'.tr,
            style: TextStyle(
              fontSize: 14.spMin,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsRow() {
    final statistics = [
      _StatisticData(
        title: 'total_events'.tr,
        value: controller.totalEvents.toString(),
        icon: Icons.event_rounded,
        color: primaryColor,
      ),
      _StatisticData(
        title: 'total_revenue'.tr,
        value: FormattedCurrency.getFormattedCurrency(controller.totalRevenue),
        icon: Icons.attach_money_rounded,
        color: Colors.green,
      ),
      _StatisticData(
        title: 'tickets_sold'.tr,
        value: controller.totalTicketsSold.toString(),
        icon: Icons.confirmation_number_rounded,
        color: Colors.blue,
      ),
      _StatisticData(
        title: 'total_attendees'.tr,
        value: controller.totalAttendees.toString(),
        icon: Icons.people_rounded,
        color: Colors.orange,
      ),
    ];

    return Hero(
      tag: 'event_statistics_${widget.eventId}',
      child: Material(
        color: Colors.transparent,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                primaryColor.withOpacity(0.05),
                Colors.blue.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
              color: primaryColor.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Statistics Row
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: statistics.asMap().entries.map((entry) {
                    final index = entry.key;
                    final stat = entry.value;
                    return Expanded(
                      child: TweenAnimationBuilder<double>(
                        duration: Duration(milliseconds: 400 + (index * 100)),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: 0.8 + (0.2 * value),
                            child: Opacity(
                              opacity: value,
                              child: _buildStatisticItemContent(stat),
                            ),
                          );
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
              // View All Button
              _buildViewAllButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticItemContent(_StatisticData data) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(8.spMin),
          decoration: BoxDecoration(
            color: data.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Icon(
            data.icon,
            size: 20.spMin,
            color: data.color,
          ),
        ),
        SizedBox(height: 8.h),
        FittedBox(
          child: Text(
            data.value,
            style: TextStyle(
              fontSize: 16.spMin,
              fontWeight: FontWeight.w700,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          data.title,
          style: TextStyle(
            fontSize: 10.spMin,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildViewAllButton() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      EventStatistics(eventId: widget.eventId),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                  transitionDuration: const Duration(milliseconds: 400),
                ),
              );
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: primaryColor.withOpacity(0.1),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    size: 16.spMin,
                    color: primaryColor,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'view_all_statistics'.tr,
                    style: TextStyle(
                      fontSize: 13.spMin,
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 12.spMin,
                    color: primaryColor,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _StatisticData {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  _StatisticData({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });
}

