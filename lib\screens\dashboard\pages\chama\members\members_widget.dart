import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/configs/country_specifics.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/add_signatory_request.dart';
import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/utils/transaction_navigation.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/app_bar/custom_app_bar.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:onekitty/main.dart' show isLight;

import 'penalty_cards.dart';

class MembersWidget extends StatelessWidget {
  final int index;
  final List<ChamaMembers> filteredmembers;
  final List<String> roles;

   const MembersWidget(
      {super.key,
      required this.index,
      required this.roles,
      required this.filteredmembers});

  @override
  Widget build(BuildContext context) {
    final ChamaDataController chamaDataController =
        Get.find<ChamaDataController>();
    final ChamaController chamaController = Get.find<ChamaController>();
    PhoneNumber num = CountryConfig.phoneNumber;
    final member = filteredmembers[index];
    final isSignatory = false.obs;
    TextEditingController whatsappNumberController = TextEditingController();
    TextEditingController emailController =
        TextEditingController(text: member.email);
    String? _dropdownValue = member.notificationType;
    String myPhone = "";
    String? phoneNumber;
    // ignore: unused_element
    Future addSignatory() async {
      // if (formKey.currentState!.validate()) {
      SignatoryRequest request = SignatoryRequest(
        chamaId: chamaDataController.chama.value.chama?.id,
        memberId: member.id,
        phoneNumber: phoneNumber,
        notificationType: _dropdownValue,
        email: emailController.text.trim(),
        whatsAppNumber: myPhone != "" ? myPhone.substring(1) : null,
      );
      bool res = await chamaController.addSignatory(request: request);
      if (res) {
        Snack.show(res, chamaController.apiMessage.string);
        // Get.offAndToNamed(NavRoutes.signatories);
      } else {
        Snack.show(res, chamaController.apiMessage.string);
      }
      // }
    }

    deleteSignatory(index) async {
      bool res = await chamaController.deleteSignatory(
          signatoryId: member.id ?? 0, chamaId: member.chamaId ?? 0);
      if (res) {
        Snack.show(res, chamaController.apiMessage.string);
        // setState(() {
        chamaController.signatories.removeAt(index);
        // });
      }
    }

    void showDeleteDialog(index) {
      showDialog(
          context: context,
          builder: (context) {
            return AlertDialog(
              actions: [
                OutlinedButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: Text('no'.tr)),
                Obx(() => CustomKtButton(
                    width: 55.w,
                    height: 35.h,
                    isLoading: chamaController.isDeleteSignatoryLoading.isTrue,
                    onPress: () {
                      deleteSignatory(index);
                      Navigator.pop(context);
                    },
                    btnText: 'yes'.tr))
              ],
              content:
                  Text('are_you_sure_delete_signatory'.tr),
            );
          });
    }

    void showAlertDialog({
      required String user,
      required Function()? onTap1,
      required Function()? onTap2,
      required Function()? onTap3,
    }) {
      showDialog(
          context: context,
          builder: (context) {
            return AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  rowWidget(
                    imagePath: AssetUrl.contact,
                    text: "${'view_all_penalties'.tr} $user ${'penalties'.tr}",
                    onTap: onTap1,
                  ),
                  rowWidget(
                    imagePath: AssetUrl.chamaTransactions,
                    text: "${'view_all_transactions'.tr} $user ${'transactions'.tr}",
                    onTap: onTap3,
                  ),
                  if (chamaDataController.chama.value.member?.role ==
                      "CHAIRPERSON")
                    rowWidget(
                      imagePath: AssetUrl.penalty,
                      text: "${'issue_penalty_to'.tr} $user",
                      onTap: onTap2,
                    ),
                ],
              ),
            );
          });
    }

    void _memberOptionsDialog(
        BuildContext context, int index, ChamaMembers member) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            bool showForm = false;
            final showDropdown = false.obs;
            TextEditingController firstNameController = TextEditingController();
            TextEditingController lastNameController = TextEditingController();
            TextEditingController role = TextEditingController();

            firstNameController.text = member.firstName!;
            lastNameController.text = member.secondName!;
            String? selectedRole = member.role;
            final ChamaDataController chamaDataController =
                Get.put(ChamaDataController());
            final ChamaController chamaController = Get.put(ChamaController());
            final formKey = GlobalKey<FormState>();

            return StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                void dropdownCallback(String? selectedValue) {
                  if (selectedValue is String) {
                    setState(() {
                      _dropdownValue = selectedValue;
                    });
                  }
                }

                return Align(
                  alignment: Alignment.centerRight,
                  child: Form(
                    key: formKey,
                    child: AlertDialog(
                      title: Row(
                        children: [
                           Expanded(
                            child: Text(
                              'update_member_details'.tr,
                              style:  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                           const SizedBox(
                            width: 4,
                          ),
                          InkWell(
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: CustomImageView(
                              imagePath: AssetUrl.imgIconoirCancel,
                              height: 18.h,
                              width: 18.w,
                              margin: EdgeInsets.symmetric(
                                  vertical: 9.h, horizontal: 5.h),
                            ),
                          ),
                        ],
                      ),
                      content: SingleChildScrollView(
                        child: Column(
                          children: [
                            Row(
                              children: [
                                 const CustomImageView(
                                  imagePath: AssetUrl.crownsv,
                                ),
                                InkWell(
                                  onTap: () {
                                    showDropdown(true);
                                  },
                                  child:  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text('update_role'.tr),
                                  ),
                                ),
                              ],
                            ),
                            Obx(() => showDropdown.value
                                ? Column(
                                    children: [
                                      DropdownButtonFormField<String>(
                                        decoration: InputDecoration(
                                          labelText: 'select_role'.tr,
                                          fillColor: Colors.blueAccent
                                              .withOpacity(0.1),
                                        ),
                                        isExpanded: true,
                                        items: roles
                                            .map(
                                              (String item) =>
                                                  DropdownMenuItem<String>(
                                                value: item,
                                                child: Text(
                                                  item,
                                                  style:  const TextStyle(
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        value: selectedRole,
                                        onChanged: (String? value) {
                                          setState(() {
                                            selectedRole = value;
                                            role.text = value!;
                                          });
                                        },
                                      ),
                                      member.isSignatory ?? false
                                          ? Padding(
                                              padding:
                                                   const EdgeInsets.all(8.0),
                                              child: MyButton(
                                                  padding: 4,
                                                  fontSize: 12,
                                                  icon: Icons.delete,
                                                  color: Colors.red,
                                                  outlined: true,
                                                  onClick: () {
                                                    showDeleteDialog(index);
                                                  },
                                                  width: 150,
                                                  label:
                                                      'remove as \n signatory'),
                                            )
                                          : Row(
                                              children: [
                                                Checkbox(
                                                  value: isSignatory.value,
                                                  onChanged: (value) {
                                                    isSignatory.value =
                                                        value ?? false;
                                                  },
                                                ),
                                                Text('is_signatory'.tr),
                                              ],
                                            ),
                                      Obx(
                                        () => isSignatory.value
                                            ? Row(children: [
                                                  Expanded(
                                                  child: Text(
                                                    'select_notification_type'.tr,
                                                  ),
                                                ),
                                                Container(
                                                  constraints: BoxConstraints(
                                                      maxWidth:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .width *
                                                              0.9),
                                                  decoration: BoxDecoration(
                                                      border: Border.all(
                                                          color: AppColors
                                                              .blueButtonColor),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12)),
                                                  child: DropdownButton(
                                                      underline:
                                                           const SizedBox(),
                                                      value: _dropdownValue,
                                                      items:   [
                                                        DropdownMenuItem(
                                                          value: "SMS",
                                                          child: Text('sms'.tr),
                                                        ),
                                                        DropdownMenuItem(
                                                          value: "WHATSAPP",
                                                          child:
                                                              Text('whatsapp'.tr),
                                                        ),
                                                        DropdownMenuItem(
                                                          value: "EMAIL",
                                                          child: Text('email'.tr),
                                                        ),
                                                        DropdownMenuItem(
                                                          value: "ALL",
                                                          child: Text('all'.tr),
                                                        ),
                                                      ],
                                                      // value: _dropdownValue,
                                                      onChanged:
                                                          dropdownCallback),
                                                )
                                              ])
                                            :  const SizedBox(),
                                      ),
                                       const SizedBox(
                                        height: 12,
                                      ),
                                      if (_dropdownValue == "EMAIL" ||
                                          _dropdownValue == "ALL")
                                        CustomTextField(
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'email_required_validation'.tr;
                                            }
                                            if (!RegExp(
                                                    r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
                                                .hasMatch(value)) {
                                              return 'enter_valid_email'.tr;
                                            }
                                            return null;
                                          },
                                          controller: emailController,
                                          hintText: 'enter_email'.tr,
                                          labelText: 'email'.tr,
                                        ),
                                      if (_dropdownValue == "WHATSAPP" ||
                                          _dropdownValue == "ALL")
                                        CustomInternationalPhoneInput(
                                          onInputChanged: (num) {
                                            setState(() {
                                              myPhone = num.phoneNumber!;
                                            });
                                          },
                                         ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceEvenly,
                                        children: [
                                          TextButton(
                                            onPressed: () {
                                              showDropdown(false);
                                            },
                                            child: Text('cancel'.tr),
                                          ),
                                           const Spacer(),
                                          Obx(
                                            () => CustomKtButton(
                                              isLoading: chamaController
                                                  .isUpdatingM.isTrue,
                                              onPress: () async {
                                                final res = await chamaController
                                                    .updateRole(
                                                        chId: chamaDataController
                                                                .singleChamaDts
                                                                .value
                                                                .id ??
                                                            0,
                                                        mId: member.id ?? 0,
                                                        whatsApp: phoneNumber,
                                                        email: emailController
                                                            .text
                                                            .trim(),
                                                        notificationType:
                                                            _dropdownValue,
                                                        isSignatory:
                                                            isSignatory.value,
                                                        role:
                                                            selectedRole ?? "");
                                                // if (isSignatory.value) {
                                                //   if (formKey.currentState
                                                //           ?.validate() ??
                                                //       false) {
                                                //     await addSignatory();
                                                //   } else {
                                                //     return;
                                                //   }
                                                // }

                                                if (res) {
                                                  ToastUtils.showInfoToast(
                                                    context,
                                                    chamaController.apiMessage
                                                        .toString(),
                                                    'info'.tr,
                                                  );
                                                  chamaController.getChamaMembers(
                                                      chamaId:
                                                          chamaDataController
                                                                  .singleChamaDts
                                                                  .value
                                                                  .id ??
                                                              0,
                                                      sort: "LEADERS");
                                                  chamaController.reset();

                                                  Navigator.of(context).pop();
                                                } else {
                                                  ToastUtils.showErrorToast(
                                                    context,
                                                    chamaController.apiMessage
                                                        .toString(),
                                                    'error'.tr,
                                                  );
                                                }
                                              },
                                              height: 20.h,
                                              width: 60.w,
                                              btnText: 'save'.tr,
                                            ),
                                          ),
                                          SizedBox(
                                            height: 10.sp,
                                          ),
                                        ],
                                      ),
                                    ],
                                  )
                                :  const SizedBox()),
                            SizedBox(
                              height: 8.h,
                            ),
                            InkWell(
                              onTap: () {
                                // Navigate to member transactions using unified system
                                Get.toChamaTransactions(
                                  chamaId: chamaDataController.chama.value.chama?.id ?? 0,
                                  accountNo: member.phoneNumber,
                                  title: '${member.firstName} Transactions',
                                );
                              },
                              child: Row(
                                children: [
                                   const CustomImageView(
                                    imagePath: AssetUrl.scroll,
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding:  const EdgeInsets.all(8.0),
                                      child: Text(
                                          "${'view_all_member_transactions'.tr} ${member.firstName} ${'member_transactions'.tr}"),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            InkWell(
                              onTap: () {
                                setState(
                                  () {
                                    showForm = true;
                                  },
                                );
                              },
                              child:  Row(
                                children: [
                                  const CustomImageView(
                                    imagePath: AssetUrl.contact,
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text('update_member_info'.tr),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            if (showForm)
                              Column(
                                children: [
                                  CustomTextField(
                                    labelText: 'enter_first_name'.tr,
                                    controller: firstNameController,
                                  ),
                                  CustomTextField(
                                    labelText: 'enter_last_name'.tr,
                                    controller: lastNameController,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      TextButton(
                                        onPressed: () {
                                          setState(
                                            () {
                                              showForm = false;
                                            },
                                          );
                                        },
                                        child: Text('cancel'.tr),
                                      ),
                                       const Spacer(),
                                      Obx(
                                        () => CustomKtButton(
                                          isLoading: chamaController
                                              .isUpdatingM.isTrue,
                                          onPress: () async {
                                            final updtDto = UpdtMemDto(
                                                chamaId: member.chamaId ?? 0,
                                                memberId: member.id ?? 0,
                                                firstName:
                                                    firstNameController.text,
                                                secondName:
                                                    lastNameController.text,
                                                profileUlr: "",
                                                role: selectedRole ?? "",
                                                status: member.status ?? "");
                                            final res = await chamaController
                                                .updateMember(updtDto: updtDto);
                                            if (res) {
                                              setState(() {
                                                showForm = false;
                                              });
                                              ToastUtils.showSuccessToast(
                                                  context,
                                                  chamaController.apiMessage
                                                      .toString(),
                                                  'success'.tr);
                                              chamaController.getChamaMembers(
                                                  chamaId: chamaDataController
                                                          .singleChamaDts
                                                          .value
                                                          .id ??
                                                      0,
                                                  sort: "LEADERS");
                                              chamaController.reset();
                                              Navigator.of(context).pop();
                                            } else {
                                              setState(() {
                                                showForm = false;
                                              });
                                              ToastUtils.showErrorToast(
                                                  context,
                                                  chamaController.apiMessage
                                                      .toString(),
                                                  'error'.tr);
                                            }
                                          },
                                          height: 20.h,
                                          width: 60.w,
                                          btnText: 'save'.tr,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 10.sp,
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            InkWell(
                              onTap: () {
                                setState(
                                  () {
                                    showPenaltiesBottomSheet(
                                        members: member, context: context);
                                  },
                                );
                              },
                              child: Row(
                                children: [
                                   const CustomImageView(
                                    imagePath: AssetUrl.gravel,
                                  ),
                                  Padding(
                                    padding:  const EdgeInsets.all(8.0),
                                    child: Text(
                                        "${'issue_penalty_to_member'.tr} ${member.firstName}"),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            InkWell(
                              onTap: () {
                                showMemberPenalties(
                                    context: context,
                                    memberId: member.id ?? 0,
                                    memberName:
                                        "${member.firstName} ${member.secondName}");
                              },
                              child: Row(
                                children: [
                                   const CustomImageView(
                                    imagePath: AssetUrl.gravel,
                                  ),
                                  Padding(
                                    padding:  const EdgeInsets.all(8.0),
                                    child: Text(
                                        "${'view_all_member_penalties'.tr} ${member.firstName} ${'member_penalties'.tr}"),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            InkWell(
                              onTap: () async {
                                // Show the confirmation dialog
                                bool confirmed = await _showConfirmationDialog(
                                    context, member.firstName ?? "");

                                if (confirmed) {
                                  final res = await chamaController.RmMember(
                                      chId: member.chamaId ?? 0,
                                      mId: member.id ?? 0,
                                      status: "REMOVED");
                                  if (res) {
                                    ToastUtils.showSuccessToast(
                                        context,
                                        chamaController.apiMessage.toString(),
                                        'success'.tr);
                                    chamaController.getChamaMembers(
                                        chamaId: chamaDataController
                                                .singleChamaDts.value.id ??
                                            0,
                                        sort: "LEADERS");
                                    chamaController.reset();

                                    Navigator.pop(context);
                                  } else {
                                    ToastUtils.showErrorToast(
                                        context,
                                        chamaController.apiMessage.toString(),
                                        'error'.tr);
                                    Navigator.pop(context);
                                  }
                                }
                              },
                              child: Row(
                                children: [
                                   const CustomImageView(
                                    imagePath: AssetUrl.trash,
                                  ),
                                  Padding(
                                    padding:  const EdgeInsets.all(8.0),
                                    child: Text("${'remove_member'.tr} ${member.firstName}"),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          });
    }

    return InkWell(
      onTap: () {
        showAlertDialog(
          onTap2: () {
            showPenaltiesBottomSheet(members: member, context: context);
          },
          user: member.firstName ?? "",
          onTap1: () {
            if (chamaDataController.chama.value.member?.id == member.id ||
                chamaDataController.chama.value.member?.role == "CHAIRPERSON" ||
                chamaDataController.chama.value.member?.role == "TREASURER") {
              showMemberPenalties(
                  context: context,
                  memberId: member.id ?? 0,
                  memberName: "${member.firstName} ${member.secondName}");
            }
          },
          onTap3: () {
            if (chamaDataController.chama.value.member?.id == member.id ||
                chamaDataController.chama.value.member?.role == "CHAIRPERSON" ||
                chamaDataController.chama.value.member?.role == "TREASURER") {
              Get.toChamaTransactions(
                chamaId: chamaDataController.chama.value.chama?.id ?? 0,
                accountNo: member.phoneNumber,
                title: '${member.firstName} Transactions',
              );
            }
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadiusStyle.roundedBorder8,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomImageView(
                  imagePath: AssetUrl.dotSix,
                  height: 25.h,
                  width: 25.w,
                  margin: EdgeInsets.only(right: 3.h),
                ),
                Opacity(
                  opacity: 0.5,
                  child: Padding(
                    padding: EdgeInsets.only(top: 6.h, bottom: 8.h),
                    child: Text(
                      "${index + 1}",
                      style: theme.textTheme.titleSmall!.copyWith(
                        color: isLight.value
                            ? appTheme.blueGray700.withOpacity(0.53)
                            : Colors.white70,
                      ),
                    ),
                  ),
                ),
                CustomImageView(
                  imagePath: AssetUrl.imgPerson,
                  height: 25.h,
                  width: 25.w,
                  margin: EdgeInsets.only(left: 3.h),
                ),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding:
                            EdgeInsets.only(left: 6.h, top: 1.h, bottom: 1.h),
                        child: Text(
                          "${member.firstName} ${member.secondName}",
                          style: CustomTextStyles.titleSmallGray90001.copyWith(
                            color: isLight.value
                                ? appTheme.gray90001
                                : Colors.white,
                          ),
                          softWrap: true,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.only(left: 6.h, top: 1.h, bottom: 1.h),
                        child: Text(
                          member.phoneNumber ?? "",
                          style: CustomTextStyles.titleSmallGray90001.copyWith(
                            color: isLight.value
                                ? appTheme.gray90001.withOpacity(0.53)
                                : Colors.white70,
                          ),
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.only(left: 6.h, top: 1.h, bottom: 1.h),
                        child: Text(
                          "${'joined_on'.tr}: ${DateTimeFormat.format(member.updatedAt?.toLocal() ?? DateTime.now(), format: 'M j')}\n",
                          overflow: TextOverflow.ellipsis,
                          style: CustomTextStyles.titleSmallGray90001.copyWith(
                            color: isLight.value
                                ? appTheme.gray90001.withOpacity(0.53)
                                : Colors.white70,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        //             if (member.role != "CHAIRPERSON")
                        //  Spacer(),
                        member.role == "CHAIRPERSON"
                            ?  const SizedBox.shrink()
                            : SizedBox(
                                width: 10.w,
                              ),
                        if (member.role == "CHAIRPERSON")
                           const CustomImageView(
                            imagePath: AssetUrl.crownsv,
                          ),

                        Text(
                          member.role ?? "",
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              color: getRoleColors(member.role ?? ""),
                              fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    if (member.isSignatory ?? false)
                      Container(
                        padding:  const EdgeInsets.symmetric(
                            horizontal: 6.0, vertical: 2.0),
                        decoration: BoxDecoration(
                          border: Border.all(color:  const Color(0xFFD4AF37)),
                          borderRadius: BorderRadius.circular(25),
                          color: Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.verified_outlined,
                                color:  const Color(0xFFD4AF37), size: 12.sp),
                             const SizedBox(width: 4),
                            Text(
                              'signatory'.tr,
                              style: TextStyle(
                                color:  const Color(0xFFD4AF37),
                                fontWeight: FontWeight.bold,
                                fontSize: 10.spMin,
                              ),
                            ),
                          ],
                        ),
                      ),
                    chamaDataController.chama.value.member?.role ==
                            "CHAIRPERSON"
                        ? IconButton(
                            icon:  const Icon(
                              Icons.edit,
                              color: AppColors.blueButtonColor,
                            ),
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            onPressed: () {
                              _memberOptionsDialog(
                                  context, member.id ?? 0, member);
                            },
                          )
                        :  const SizedBox.shrink()
                  ],
                ),
              ],
            ),
             const Divider(height: 2),
          ],
        ),
      ),
    );
  }
}

Future<bool> _showConfirmationDialog(
    BuildContext context, String memberName) async {
  return showDialog<bool>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.r),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset:  const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Warning icon
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(30.r),
                ),
                child: Icon(
                  Icons.warning_amber_rounded,
                  size: 32.w,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              
              SizedBox(height: 20.h),
              
              // Title
              Text(
                'confirm_removal'.tr,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              
              SizedBox(height: 12.h),
              
              // Message
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    height: 1.5,
                  ),
                  children: [
                    TextSpan(text: '${'are_you_sure_remove'.tr} '),
                    TextSpan(
                      text: memberName,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    TextSpan(text: ' ${'from_group'.tr}'),
                  ],
                ),
              ),
              
              SizedBox(height: 32.h),
              
              // Action buttons
              Row(
                children: [
                  // Cancel button
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 14.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        side: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  
                  SizedBox(width: 12.w),
                  
                  // Remove button
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.error,
                        foregroundColor: Theme.of(context).colorScheme.onError,
                        padding: EdgeInsets.symmetric(vertical: 14.h),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        'remove_member'.tr,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onError,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  ).then((value) => value ?? false);
}

void showPenaltiesBottomSheet({
  required ChamaMembers members,
  required BuildContext context,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
        ),
        child: Column(
          children: [
            // Handle bar and header
            Container(
              padding: EdgeInsets.fromLTRB(24.w, 12.h, 24.w, 16.h),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 40.w,
                    height: 4.h,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(2.r),
                    ),
                  ),
                  
                  SizedBox(height: 20.h),
                  
                  // Header with member info
                  Row(
                    children: [
                      // Member avatar
                      Container(
                        width: 48.w,
                        height: 48.w,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(24.r),
                        ),
                        child: Icon(
                          Icons.person,
                          size: 24.w,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      
                      SizedBox(width: 16.w),
                      
                      // Member details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${members.firstName ?? ''} ${members.secondName ?? ''}'.trim(),
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              'member_penalties_title'.tr,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Close button
                      InkWell(
                        onTap: () => Navigator.pop(context),
                        borderRadius: BorderRadius.circular(20.r),
                        child: Container(
                          padding: EdgeInsets.all(8.w),
                          child: Icon(
                            Icons.close,
                            size: 20.w,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 16.h),
                  
                  // Divider
                  Divider(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                    thickness: 1,
                  ),
                ],
              ),
            ),
            
            // Content area
            Expanded(
              child: PenaltiesBottomSheet(
                member: members,
              ),
            ),
          ],
        ),
      );
    },
  );
}
Widget rowWidget(
    {required String imagePath,
    required String text,
    required Function()? onTap}) {
  return Padding(
    padding:  const EdgeInsets.symmetric(vertical: 5.0),
    child: ListTile(
      onTap: onTap,
      dense: true,
      contentPadding:  const EdgeInsets.symmetric(horizontal: 12.0),
    
      visualDensity:  const VisualDensity(horizontal: 0, vertical: -4),
      leading: CircleAvatar(
        radius: 24,
        child: CustomImageView(
          imagePath: imagePath,
        ),
      ),
      title: Text(text, style:  const TextStyle(
        fontSize: 14, 
        fontWeight: FontWeight.bold

      ),),
      subtitle:  const Text(''),
    ),
  );
}

void showMemberPenalties(
    {required int memberId,
    required String memberName,
    required BuildContext context}) {
  showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        final ChamaDataController chamaDataController =
            Get.find<ChamaDataController>();
        return DraggableScrollableSheet(
            maxChildSize: 0.97,
            initialChildSize: 0.7,
            expand: false,
            builder: (context, scrollController) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:  const EdgeInsets.symmetric(
                        horizontal: 12.0, vertical: 5),
                    child: Text(
                      "${memberName.toUpperCase()} ${'penalties_uppercase'.tr}",
                      style: context.titleText
                          ?.copyWith(decoration: TextDecoration.underline),
                    ),
                  ),
                   const SizedBox(height: 7),
                  Expanded(
                    child: GetX(
                        init: ChamaController(),
                        initState: (state) {
                          Future.delayed(Duration.zero, () async {
                            try {
                              await state.controller?.getMemberPenalties(
                                  memeberId: memberId,
                                  chamaId: chamaDataController
                                          .chama.value.chama?.id ??
                                      0);
                            } catch (e) {
                              throw e;
                            }
                          });
                        },
                        builder: (ChamaController chamaController) {
                          if (chamaController
                              .isGetMemberPenaltiesLoading.isTrue) {
                            return SizedBox(
                              height: SizeConfig.screenHeight * .33,
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SpinKitDualRing(
                                      color: ColorUtil.blueColor,
                                      lineWidth: 4.sp,
                                      size: 40.0.sp,
                                    ),
                                     Text(
                                      'loading'.tr,
                                      style: const TextStyle(
                                        color: Colors.white,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            );
                          } else if (chamaController.memberPenalties.isEmpty) {
                            return Padding(
                              padding:  const EdgeInsets.all(13.0),
                              child: Text('you_dont_have_penalties'.tr),
                            );
                          } else if (chamaController
                              .memberPenalties.isNotEmpty) {
                            return ListView.separated(
                              separatorBuilder: (context, index) {
                                return  const Divider();
                              },
                              itemBuilder: (context, index) {
                                final memberPenalty =
                                    chamaController.memberPenalties[index];
                                return Container(
                                  margin:  const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 12),
                                  child: ListTile(
                                    leading: Text("${index + 1}"),
                                    title: Text(
                                        "${'reason'.tr} ${memberPenalty.reason}",
                                        style: context.dividerTextLarge),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("${'status'.tr} ${memberPenalty.status}"),
                                        Text(
                                            "${'amount_paid'.tr} ${memberPenalty.paidAmount ?? "0"}"),
                                        Text(
                                            "${'issued_on'.tr}: ${DateFormat("yyyy-MM-dd").format(memberPenalty.createdAt ?? DateTime.now())}")
                                      ],
                                    ),
                                    trailing: Text("${memberPenalty.amount}",
                                        style: context.dividerTextLarge
                                            ?.copyWith(color: Colors.red)),
                                  ),
                                );
                              },
                              itemCount: chamaController.memberPenalties.length,
                            );
                          }
                          return Text('no_penalties_here_yet'.tr);
                        }),
                  ),
                ],
              );
            });
      });
}
