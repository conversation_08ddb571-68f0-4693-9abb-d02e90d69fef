import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/contr_kitty_model.dart';

import '../../../../../../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class WhatsAppWidget extends StatefulWidget {
  final String whatsappName;
  final Notifications whatsapp;
  final String whatsappProfile;
  const WhatsAppWidget(
      {super.key,
      required this.whatsappName,
      required this.whatsapp,
      required this.whatsappProfile});

  @override
  State<WhatsAppWidget> createState() => _WhatsAppWidgetState();
}

class _WhatsAppWidgetState extends State<WhatsAppWidget> {
  bool value = true;
  final ContributeController singleKitty = Get.put(ContributeController());
  final DataController dataController = Get.find<DataController>();
  final NotificationDataController controller =
      Get.put<NotificationDataController>(NotificationDataController());
  final WhatsappController whatsappController = Get.put(WhatsappController());
  String status = "ACTIVE";
  bool isLoading = false;
  final isRloading = false.obs;

  setValue() async {
    if (widget.whatsapp.whatsappStatus == "ACTIVE") {
      value = true;
    } else {
      value = false;
    }
  }

  @override
  void initState() {
    super.initState();
    setValue();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 7.w,
        vertical: 4.h,
      ),
      decoration: AppDecoration.outlineBlueGray.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          widget.whatsapp.whatsAppProfile != null &&
                  widget.whatsapp.whatsAppProfile!.isNotEmpty
              ? CircleAvatar(
                  backgroundColor: Colors.transparent,
                  backgroundImage: CachedNetworkImageProvider(
                    widget.whatsapp.whatsAppProfile ?? '',
                    maxHeight: 30,
                    maxWidth: 30,
                    errorListener: (p0) {
                      return;
                    },
                    // fit: BoxFit.fill,
                    // fadeInCurve: Curves.bounceIn,
                    // height: 30,
                    // width: 30,
                    // url: widget.whatsapp.whatsAppProfile!,
                    // placeholder: (context, url) => CircularProgressIndicator(),
                    // errorWidget: (context, url, error) => CustomImageView(
                    //   imagePath: AssetUrl.whatsapp,
                    //   height: 40.h,
                    //   width: 40.w,
                    //   radius: BorderRadius.circular(
                    //     20.w,
                    //   ),
                    // ),
                  ),
                )
              : CustomImageView(
                  imagePath: AssetUrl.whatsapp,
                  height: 40.h,
                  width: 40.w,
                  radius: BorderRadius.circular(
                    20.w,
                  ),
                ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                left: 6.w,
                top: 8.h,
                bottom: 10.h,
              ),
              child: Text(
                widget.whatsappName,
                overflow: TextOverflow.ellipsis,
                style: CustomTextStyles.titleSmallGray900,
              ),
            ),
          ),
          isLoading
              ? SpinKitDualRing(
                  color: Theme.of(context).primaryColor,
                  size: 25.0,
                )
              : Switch.adaptive(
                  value: value,
                  onChanged: (value) async {
                    setState(() {
                      isLoading = true;
                    });
                    bool res = await singleKitty.toggleWhatsapp(
                        id: widget.whatsapp.id ?? 0,
                        kittyid: dataController.kitty.value.kitty?.iD ?? 0,
                        status: value ? "ACTIVE" : "INACTIVE");
                    if (res) {
                      setState(() {
                        isLoading = false;
                        this.value = value;
                      });
                      Snack.show(res, singleKitty.apiMessage.string);
                    } else {
                      Snack.show(res, singleKitty.apiMessage.string);
                    }
                  }),
          Obx(
            () => InkWell(
              onTap: () async {
                bool confirm = await _showConfirmationDialog(context);
                if (confirm) {
                  isRloading(true);
                  bool res = await singleKitty.RmWhatsapp(
                    notificationId: widget.whatsapp.id ?? 0,
                    kittyId: dataController.kitty.value.kitty?.iD ?? 0,
                  );

                  if (res) {
                    isRloading(false);
                  } else {
                    isRloading(false);
                  }
                }
              },
              child: isRloading.value
                  ? SpinKitDualRing(
                      color: Theme.of(context).primaryColor,
                      size: 25.0,
                    )
                  : CustomImageView(
                      height: 20.h,
                      color: Colors.red,
                      imagePath: AssetUrl.imgIconoirCancel,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Future<bool> _showConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Confirm Removal'),
              content: const Text(
                  'Are you sure you want to remove this WhatsApp?\nYou will no longer be receiving transaction updates in your WhatsApp group'),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                  child: const Text('Confirm'),
                ),
              ],
            );
          },
        ) ??
        false;
  }
}
