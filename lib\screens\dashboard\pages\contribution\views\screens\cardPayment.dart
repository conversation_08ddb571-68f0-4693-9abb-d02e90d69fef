import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/payments_page_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/in_app_browser.dart';

class CardPayment extends StatefulWidget {
  final bool isChamaContribute;
  final bool? isPurchasingTicket;

  const CardPayment(
      {super.key, required this.isChamaContribute, this.isPurchasingTicket});
  @override
  State<CardPayment> createState() => _CardPaymentState();
}

class _CardPaymentState extends State<CardPayment> {
  final ContributeController contributeController =
      Get.put(ContributeController());

  final UserKittyController topupcontroller = Get.put(UserKittyController());

  final ChamaController chamaController = Get.put(ChamaController());
  final ticketsPurchase = Get.put(PaymentsController());

  @override
  Widget build(BuildContext context) {
    final url = widget.isPurchasingTicket ?? false
        ? ticketsPurchase.cardPayment['checkout_url']
        : widget.isChamaContribute
            ? chamaController.contributeData["checkout_url"]
            : contributeController.contributeData["checkout_url"] ??
                topupcontroller.topUpData["checkout_url"];

    return WebViewCustom(
      url: url,
      isBackDrop: false,
    );
  }
}
