import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';

class TillPage extends StatelessWidget {
  final TextEditingController tillController;
  final TextEditingController date;
  final TextEditingController time;
  const TillPage(
      {super.key,
      required this.tillController,
      required this.date,
      required this.time});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "mpesa_till_number_label".tr,
              style: context.titleText?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
          ),
          CustomTextField(
            labelText: "till_number_label".tr,
            controller: tillController,
            showNoKeyboard: true,
            isRequired: true,
            hintText: "till_number_hint".tr,
            validator: (value) {
              RegExp regex = RegExp(r'[a-zA-Z]');
              if (value!.isEmpty) {
                return "enter_till_number_validation".tr;
              } else if (regex.hasMatch(value)) {
                return "till_no_alphabets".tr;
              } else {
                return null;
              }
            },
          ),
          SingleLineRow(text: "expected_contribution_end_date_label".tr, popup: KtStrings.endDateInfo,),
          DatePicker(date: date, time: time, isAllow: true,)
        ],
      ),
    );
  }
}
