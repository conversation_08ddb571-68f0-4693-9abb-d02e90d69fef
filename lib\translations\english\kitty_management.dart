import 'package:onekitty/configs/country_specifics.dart';

const Map<String, String> kittyManagementEnglish = {
  'create_contribution_kitty': 'Create a contribution Kitty',
  'create_first_kitty_today': 'Create your first kitty today and fuel the spark for something big.',
  'create_a_kitty': 'Create a Kitty',
  'contribute_to_kitty': 'Contribute to <PERSON>',
  'contribute_to_kitty_title': 'Contribute to <PERSON>',
  'your_information': 'Your Information',
  'full_name': 'Full Name',
  'john_doe': '<PERSON>',
  'hide_name_in_contributions': 'Hide  name in contributions',
  'hide_number_in_contributions': 'hide number in contributions',
  'payment_method': 'Payment Method',
  'name_example_com': '<EMAIL>',
  'make_contribution': 'Make Contribution',
  'amount_kes': 'Amount (${CountryConfig.getCurrencyCode})',
  'enter_amount': 'Enter amount',
  'your_email_com': '<EMAIL>',
  'brief_description_issue': 'Brief description of the issue',
  'detailed_description_incident': 'Detailed description of the incident',
  'share_kitty_contribution_link': 'Share the Kitty contribution link to invite friends to start contributing',
  'contribute_to_a_kitty': 'Contribute to a kitty',
  'join_hands_today': 'Join hands today and be part of something special',
  'kitty_url_or_id': 'Kitty url or Kitty ID',
  'url_example': 'e.g. https://onekitty.co.ke/kitty/674/',
  'create_a_kitty_title': 'Create a Kitty',
  'whatsapp_group_link_optional': 'Whatsapp group link(optional)',
  'kitty_name': 'Kitty Name',
  'wedding_contribution_example': 'e.g Wedding Contribution',
  'kitty_name_length_validation': 'Kitty Name must be between 5 and 300',
  'kitty_description': 'Kitty Description',
  'purpose_contribution_example': 'e.g purpose of contribution',
  'describe_purpose_kitty': 'Describe the purpose of your kitty ',
  'target_amount_optional': 'Target Amount (Optional)',
  'amount_example': 'e.g 10000',
  'kitty_category': 'Kitty Category',
  'select_categories': 'Select Categories',
  'referral_code_optional': 'Referral code(optional)',
  'phone_number_label': 'Phone Number',
  'beneficiary_payment_channel': 'Beneficiary payment channel',
  'enter_required_fields_paybill': 'Enter all required fields for Paybill',
  'enter_till_number': 'Enter Till number',
  'enter_bank_account_number': 'Enter Bank account number',
  'my_kitties': 'My Kitties',
  'beneficiaries': 'Beneficiaries',
  'create_beneficiary': 'Create beneficiary',
  'delete_beneficiary': 'Delete Beneficiary',
  'add_beneficiary': 'Add Beneficiary',
  'edit_beneficiary': 'Edit Beneficiary',
  'account_name': 'Account Name',
  'account_number': 'Account Number',
  'bank_account_number': 'Bank Account Number',
  'enter_phone_number': 'Enter phone number',
  'scan_to_pay': 'Scan to Pay',
  'about': 'About: ',
  'services': 'Services',
  'advanced_settings': 'Advanced Settings',
  'activate_kitty': 'Activate Kitty',
  'deactivate_kitty': 'Deactivate Kitty',
  'reactivate_kitty_message': 'You can reactivate this kitty at any time',
  'delete_kitty': 'Delete Kitty',
  'deactivate': 'Deactivate',
  'make_withdrawal_request': 'Make a withdrawal request',
  'funds_transferred_message': 'Funds shall be transferred to the specified beneficiary channel',
  'amount_to_withdraw': 'Amount to withdraw',
  'kitty_not_active': 'Kitty is not Active',
  'share_kitty_link_message': 'Share the kitty contribution link to invite friends to start contributing',
  'kitty': 'Kitty',
  'connect_whatsapp_group_event_notifications': 'Connect a WhatsApp group to receive event notifications and updates',
  'connect_whatsapp_groups_chama_notifications': 'Connect WhatsApp groups to enable notifications and communication',
  'connect_whatsapp_group_transaction_notifications': 'Connect a WhatsApp group to receive transaction notifications',
  'remove_whatsapp_group_event_confirmation': 'Are you sure you want to remove this WhatsApp group?\nYou will no longer receive transaction updates for this event.',
  'remove_whatsapp_group_confirmation': 'Are you sure you want to remove this WhatsApp?\nYou will no longer be receiving transaction updates in your WhatsApp group',
  'add_group': 'Add Group',
  'my_recent_kitty': 'My Recent Kitty',
  'see_all_kitties': 'See all kitties',
  'next_beneficiary': 'Next Beneficiary',
  'connected_whatsapp_groups': 'Connected WhatsApp groups',
  'earnings': 'Earnings',
  'share_onekitty_message': 'Hey, are you worried about your financial contributions? Have a look at Onekitty {url} easy and secure platform to manage your contributions.',
  'connect_whatsapp_group': 'Connect WhatsApp Group',
  'enter_whatsapp_link': 'Enter WhatsApp link',
  'provide_a_link': 'Provide a link',
  'whatsapp_group': 'WhatsApp Group',
  'remove_whatsapp_group': 'Remove WhatsApp Group',
  'whatsapp_groups': 'WhatsApp Groups',
  'otp_verification': 'OTP verification',
  'enter_otp_code_sent': 'Enter the OTP code sent to the number provided',
  'manual_method': 'Manual Method.',
};