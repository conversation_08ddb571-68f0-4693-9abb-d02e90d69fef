const Map<String, String> chamaManagementFrench = {
  'my_chama': 'Mon Chama',
  'create_a_chama': 'Créer un chama',
  'chama_name': 'Nom du chama',
  
  'ready_to_grow_together': 'Prêt à grandir ensemble?',
  'start_your_chama_build_wealth': 'Commencez votre chama, construisez la richesse et soutenez-vous mutuellement.',
  'create_a_chama_title': 'Créer un Chama',
  'chama_details': 'Détails du\\n Chama',
  'members': 'Membres',
  'chama_name_label': 'Nom du Chama',
  'chama_name_hint': 'ex. Chama Wasafi',
  'chama_name_required': 'Le nom du Chama ne peut pas être vide',
  'chama_name_length': 'Le nom du Chama doit contenir entre 5 et 300 caractères',
  'chama_description': 'Description du Chama',
  'chama_description_hint': 'ex. objectif du chama',
  'whatsapp_group_link': 'Lien du groupe WhatsApp (optionnel)',
  'group_link': 'Lien du groupe',
  'enter_referer_code': 'Saisir le code de parrain (Optionnel)',
  'referral_code': 'Code de parrainage',
  'referral_code_hint': 'ex. 12',
  'enter_chama_email': 'Saisir l\'email du Chama',
  'email_hint': 'ex. <EMAIL>',
  'how_much_contribute': 'Combien chaque membre contribue-t-il',
  'amount_hint': '1000',
  'invite_chama_members': 'Inviter des membres du Chama',
  'update_member_details': 'Mettre à jour les détails du membre',
  'enter_first_name': 'Saisir le prénom',
  'enter_last_name': 'Saisir le nom de famille',
  'payer_phone_number': 'Numéro de téléphone du payeur',
  'chama_member_phone_number': 'Numéro de téléphone du membre du chama',
  'pay_penalty': 'Payer une pénalité',
  'select_penalty_to_pay': 'Sélectionner la pénalité à payer',
  'it_looks_like_has_penalties': 'Il semble que',
  'has_penalties': 'a',
  'penalties': 'pénalités.',
  'meetings': 'Réunions',
  'meetings_description': 'Voici les réunions fixées par l\'admin du chama',
  'add_meeting': 'Ajouter une réunion',
  'calender': 'Calendrier',
  'view_all_penalties': 'Voir tout',
  'view_all_transactions': 'Voir tout',
  'transactions': 'transactions',
  'issue_penalty_to': 'Infliger une pénalité à',
  'update_member_info': 'Mettre à jour les infos du membre',
  'remove_as_signatory': 'retirer comme \\n signataire',
  'view_all_member_transactions': 'Voir tout',
  'member_transactions': 'Transactions',
  'issue_penalty_to_member': 'Infliger une pénalité à',
  'view_all_member_penalties': 'Voir tout',
  'member_penalties': 'pénalités',
  'remove_member': 'Retirer',
  'member_phone_required': 'Veuillez saisir le numéro de téléphone du membre du chama',
  'penalties_uppercase': 'PÉNALITÉS',
  'chama_title': 'TITRE DU CHAMA:',
  'member_label': 'MEMBRE:',
  'penalty_label': 'PÉNALITÉ:',
  'meeting': 'réunion',
  'penalty': 'pénalité',
  'failed_to_load_chamas': 'Échec du chargement des chamas. Veuillez réessayer.',
  'members_count': 'Membres',
  'add_chama_members': 'Ajouter des membres du Chama',
  'add_members': 'Ajouter des membres',
  'an_error_occurred_adding_member': 'Une erreur s\'est produite lors de l\'ajout du membre.',
  'add_chama_member': 'Ajouter un membre du Chama',
  'set_receiving_order': 'Définir l\'ordre de réception',
  'issue_penalties': 'Infliger une/des pénalité(s)',
  'update_penalty': 'Mettre à jour la pénalité',
  'set_penalties': 'Définir les pénalités',
  'create_penalties_description': 'Créez des pénalités qui peuvent être infligées aux membres plus tard',
  'select_common_penalties': 'Sélectionner parmi les pénalités communes',
  'click_to_select_penalty': 'Cliquer pour sélectionner la pénalité',
  'enter_penalty_name': 'Saisir le nom de la pénalité',
  'penalty_name_hint': 'ex. Absence à la réunion',
  'enter_penalty_description': 'Saisir la description de la pénalité (Optionnel)',
  'penalty_description_hint': 'ex. description',
  'enter_penalty_amount': 'Saisir le montant de la pénalité',
  'penalty_amount_hint': 'ex. 100',
  'common_penalties': 'PÉNALITÉS COMMUNES',
  'chama_settings': 'Paramètres du Chama',
  'settings_description': 'Ce sont les paramètres définis par l\'admin du chama',
  'beneficiary_per_cycle': 'Bénéficiaire par cycle',
  'beneficiary_percentage': 'Pourcentage du bénéficiaire',
  'signatories': 'Signataires',
  'signatory_threshold': 'Seuil de signataire',
  'get_to_know_beneficiaries': 'Apprenez-en plus sur ce que sont les bénéficiaires.',
  'beneficiaries_per_cycle': 'Bénéficiaires par cycle:',
  'get_to_know_signatories': 'Apprenez-en plus sur les signataires',
  'update_button': 'Mettre à jour',
  'save_button': 'Enregistrer',
  'documents': 'Documents',
  'share_documents_description': 'Partagez tous les documents avec les membres de votre chama',
  'error_getting_documents': 'Erreur lors de l\'obtention des documents',
  'add_document': 'Ajouter un document',
  'signatories_title': 'Signataires',
  'signatories_description': 'Voici les signataires définis par l\'admin du chama',
  'add_signatory': 'Ajouter un signataire',
  'first_name': 'Prénom',
  'last_name': 'Nom de famille',
  'whatsapp_number': 'Numéro WhatsApp',
  'chama': 'CHAMA',
  'created_by': 'Créé par: ',
  'chama_email': 'Email du Chama',
  'enter_your_chama_email': 'Saisir l\'email de votre Chama',
  'transfer_from_chama_account': 'Transférer des fonds du compte chama',
  'transfer_from_penalty_kitty': 'Transférer du kitty de pénalité',
  'transfer_from_penalty': 'Transférer de la pénalité',
  'transfer_from_chama': 'Transférer du Chama',
  'enter_beneficiary_phone_number': 'Saisir le numéro de téléphone du bénéficiaire',
  'enter_reason_for_transaction': 'Saisir la raison de la transaction',
  'make_a_transfer': 'Faire un transfert',
  'make_transactions_with_ease': 'Effectuez des transactions facilement',
  'group_name': 'Nom du groupe',
  'please_enter_group_name': 'Veuillez saisir le nom du groupe',
  'group_members': 'Membres du groupe',
  'add_members_to_group': 'Ajouter des membres au groupe',
  'delete_selected_members': 'Supprimer les membres sélectionnés',
  'edit_member': 'Modifier le membre',
  'phone_number_format_hint': 'Format: 0722XXX ou 722XXX',
  'delete_member': 'Supprimer le membre',
  'please_add_one_member_group': 'Veuillez ajouter au moins un membre au groupe',
  'transfer_funds_from_chama_account': 'Transférer des fonds du compte chama',
};