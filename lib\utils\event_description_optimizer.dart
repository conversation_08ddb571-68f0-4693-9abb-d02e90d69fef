import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../screens/dashboard/pages/events/views/widgets/my_quill_editor.dart';

/// Utility class to help optimize event description rendering performance
class EventDescriptionOptimizer {
  static const String _tag = 'EventDescriptionOptimizer';
  
  /// Clear all cached documents and controllers when switching between events
  static void clearAllCaches() {
    if (kDebugMode) {
      print('$_tag: Clearing all caches');
    }

    // Clear read more controllers
    try {
      QuillReadMoreController.cleanupAllControllers();
    } catch (e) {
      if (kDebugMode) {
        print('$_tag: Error clearing controllers: $e');
      }
    }
  }
  
  /// Optimize memory usage by clearing unused controllers
  static void optimizeMemory() {
    if (kDebugMode) {
      print('$_tag: Optimizing memory usage');
    }
    
    // Force garbage collection of unused GetX controllers
    Get.deleteAll(force: true);
    
    // Clean up read more controllers
    QuillReadMoreController.cleanupAllControllers();
  }
  
  /// Generate a unique tag for event descriptions to prevent conflicts
  static String generateUniqueTag(int? eventId, String? suffix) {
    final timestamp = DateTime.now().microsecondsSinceEpoch;
    return 'event_${eventId ?? 0}_${suffix ?? ''}_$timestamp';
  }
  
  /// Check if a description needs complex rendering (HTML/Delta)
  static bool needsComplexRendering(String? description) {
    if (description == null || description.isEmpty) {
      return false;
    }
    
    // Check for HTML tags
    if (description.contains('<') && description.contains('>')) {
      return true;
    }
    
    // Check for Delta format
    if (description.trim().startsWith('[{') || description.trim().startsWith('{"ops"')) {
      return true;
    }
    
    return false;
  }
  
  /// Get a simplified version of description for preview
  static String getSimplifiedDescription(String? description, {int maxLength = 200}) {
    if (description == null || description.isEmpty) {
      return '';
    }
    
    // Remove HTML tags for simple preview
    String simplified = description
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    if (simplified.length > maxLength) {
      simplified = '${simplified.substring(0, maxLength)}...';
    }
    
    return simplified;
  }
}
