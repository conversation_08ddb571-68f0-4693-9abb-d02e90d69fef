import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ClayProgress extends StatelessWidget {
  final int currentStep;
  final int totalSteps;

  const ClayProgress({
    super.key,
    required this.currentStep,
    required this.totalSteps,
  });

  @override
  Widget build(BuildContext context) {
    final progress = currentStep / totalSteps;
    final theme = Theme.of(context);

    return Column(
      children: [
        Text(
          'step_of'
              .tr
              .replaceAll('{current}', currentStep.toString())
              .replaceAll('{total}', totalSteps.toString()),
          style: theme.textTheme.labelLarge?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'percent_complete'.tr
              .replaceAll('{percent}', (progress * 100).round().toString()),
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 20),
        Container(
          height: 25,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: theme.colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: theme.brightness == Brightness.light
                    ? Colors.white.withOpacity(0.8)
                    : Colors.black12,
                offset: const Offset(-4, -4),
                blurRadius: 8,
              ),
              BoxShadow(
                color: theme.brightness == Brightness.light
                    ? Colors.grey.shade400
                    : Colors.black26,
                offset: const Offset(4, 4),
                blurRadius: 8,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
              minHeight: 25,
            ),
          ),
        ),
      ],
    );
  }
}
