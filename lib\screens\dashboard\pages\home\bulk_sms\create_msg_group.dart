import 'dart:convert';
import 'dart:io';
import 'package:csv/csv.dart';
import 'package:dio/dio.dart' as d;
import 'package:excel/excel.dart' as ex;
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/configs/country_specifics.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/bulk_sms/sms_groups.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/helper_page.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/utils/my_button.dart';import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_quill_editor.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/widgets/getx_contact_picker.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CreateMsgController extends GetxController {
  final groupMembers = <SmsGroupMember>[].obs;
  final groups = <SmsGroups>[].obs;
  final isloading = false.obs;
  final logger = Get.find<Logger>();
  String? mAccessToken;
  // final selectedGroups = <SmsGroups>[].obs;

  bool hasDuplicate(String value) {
    int count = 0;
    final list = groupMembers.map((e) => e.phoneNumber ?? '').toList();
    for (var item in list) {
      if (item == value) {
        count++;
        if (count > 1) return true;
      }
    }
    return false;
  }

  Future<d.Response> request(
      {required String url,
      required Method method,
      Map<String, dynamic>? params,
      d.Options? options,
      FormData? formdata}) async {
    d.Response response;
    mAccessToken = await FirebaseAuth.instance.currentUser?.getIdToken();
    final d.Dio dio = d.Dio(
      d.BaseOptions(
        baseUrl:'https://apisalticon.onekitty.co.ke/notification/',
        receiveDataWhenStatusError: false,
        headers: mAccessToken != null
            ? {
                "Content-Type": "application/json",
                "Authorization": "Bearer $mAccessToken"
              }
            : {"Content-Type": "application/json"},
        followRedirects: true,
        receiveTimeout: const Duration(minutes: 2),
        sendTimeout: const Duration(minutes: 1),
        validateStatus: (status) {
          return status! < 500;
        },
      ),
    );

    try {
      if (method == Method.POST) {
        if (formdata == null) {
          response = await dio.post(url, data: params, options: d.Options(
            validateStatus: (status) {
              return true;
            },
          ));
        } else {
          response = await dio.post(url, data: formdata, options: options);
        }
      } else if (method == Method.PUT) {
        response = await dio.put(url, data: params);
      } else if (method == Method.DELETE) {
        response = await dio.delete(url);
      } else if (method == Method.PATCH) {
        response = await dio.patch(url);
      } else {
        response = await dio.get(url);
      }
      return response;
    } on SocketException catch (e) {
      logger.e(e);
      throw Exception("Not Internet Connection");
    } on FormatException catch (e) {
      logger.e(e);

      throw Exception("Bad response format");
    } on d.DioError catch (e) {
      logger.e(e);

      rethrow;
    } catch (e) {
      logger.e(e);
      throw Exception("Something went wrong");
    }
  }

  Future<void> fetchMessageGroups() async {
    // print({
    //   "phone_number": Get.find<Eventcontroller>().getLocalUser()?.phoneNumber,
    //   "external_user_id": Get.find<Eventcontroller>().getLocalUser()?.id,
    // });
    try {
      Future.microtask(() => isloading(true));
      // "sms-group/?external_user_id=1508"
      var resp = await request(
          url:
              "${ApiUrls.get_msg_groups}?external_user_id=${Get.find<Eventcontroller>().getLocalUser()?.id}",
          method: Method.GET,
          params: {
            "phone_number":
                Get.find<Eventcontroller>().getLocalUser()?.phoneNumber,
            "external_user_id": Get.find<Eventcontroller>().getLocalUser()?.id,
          });
      debugPrint(resp.toString());
      if (resp.data['status']) {
        final _data = resp.data['data']['items'] as List;
        groups.value = _data.map((e) => SmsGroups.fromJson(e)).toList();
      }
      Future.microtask(() => isloading(false));
    } catch (e) {
      logger.e(e);
      Future.microtask(() => isloading(false));
    }
  }

  Future<void> fetchGroupMembers(int groupId) async {
    isloading(true);

    try {
      var resp = await request(
          url: '${ApiUrls.get_msg_groups}$groupId',
          method: Method.GET,
          params: {
            "phone_number":
                Get.find<Eventcontroller>().getLocalUser()?.phoneNumber,
            "external_user_id": Get.find<Eventcontroller>().getLocalUser()?.id,
          });
      if (resp.data['status']) {
        final _data = resp.data['data'] as List;
        groupMembers.value =
            _data.map((e) => SmsGroupMember.fromJson(e)).toList();
      }
      isloading(false);
    } catch (e) {
      logger.e(e);
      isloading(false);
    }
  }

  final isAddingMember = false.obs;
  Future<void> addGroupMember(Map<String, dynamic> payload) async {
    try {
      isAddingMember(true);
      var resp = await request(
          url: ApiUrls.add_sms_group_member,
          method: Method.POST,
          params: payload);
      logger.log(Level.debug, resp.data);
      if (resp.data['status']) {
        // Get.back();
        Get.snackbar('success', resp.data['message'],
            backgroundColor: Colors.green);

        groupMembers.add(SmsGroupMember.fromJson(resp.data['data'] ?? {}));
        fetchGroupMembers(payload['sms_group_id']);
        if (resp.data['data'] == null || resp.data['data'] == {}) {
          return;
        }
      } else {
        ToastUtils.showToast(
            '${resp.data['message'] ?? "Failed to add member"}');
      }
    } catch (e) {
      logger.e(e);
      throw Exception('Failed to add member: $e');
    } finally {
      isAddingMember(false);
    }
  }

  final isEditingMember = false.obs;
  Future<void> editGroupMember(Map<String, dynamic> payload) async {
    try {
      isEditingMember(true);
      var resp = await request(
          url: ApiUrls.add_sms_group_member,
          method: Method.PUT,
          params: payload);
      logger.log(Level.debug, resp.data);
      if (resp.data['status']) {
        // Get.back();
        Get.snackbar('success', resp.data['message'],
            backgroundColor: Colors.green);
        final index = groupMembers.indexWhere((e) => e.id == payload['ID']);
        if (index != -1) {
          groupMembers[index] = SmsGroupMember.fromJson(resp.data['data']);
        }
        fetchGroupMembers(payload['ID']);
      }
    } catch (e) {
      logger.e(e);
      Get.snackbar('success', 'Failed to edit member',
          backgroundColor: Colors.red);
      fetchGroupMembers(payload['ID']);
    } finally {
      isEditingMember(false);
    }
  }

  final isDeletingMember = false.obs;
  Future<void> deleteGroupMember(
      Map<String, dynamic> payload, int groupId) async {
    try {
      isDeletingMember(true);
      var resp = await request(
          url: "${ApiUrls.add_sms_group_member}$groupId",
          method: Method.DELETE,
          params: payload);
      logger.log(Level.debug, resp.data);
      if (resp.data['status']) {
        // Get.back();
        Get.snackbar('success', resp.data['message'],
            backgroundColor: Colors.green);
        groupMembers.removeWhere((e) => e.toJson() == payload);
        fetchGroupMembers(groupId);
      }
    } catch (e) {
      logger.e(e);
      Get.snackbar('success', 'Failed to delete member',
          backgroundColor: Colors.red);
    } finally {
      isDeletingMember(false);
    }
  }

  Future<bool> createMessageGroup(
      {required Map<String, dynamic> payload,
      edit = false,
      close = true}) async {
    isloading(true);
    try {
      var resp = await request(
          url: ApiUrls.create_msg_group,
          method: edit ? Method.PUT : Method.POST,
          params: payload);
      logger.log(Level.debug, resp.data);
      if (resp.data['status']) {
        if (close) {
          Get.back();
        }
        Get.snackbar('success', resp.data['message'],
            backgroundColor: Colors.green);
        if (edit && resp.data['data'] != null) {
          final index =
              groups.indexWhere((e) => e.id == resp.data['data']['ID']);
          if (index != -1) {
            groups[index] =
                SmsGroups.fromJson(resp.data['data'] as Map<String, dynamic>);
          }
        }
        fetchMessageGroups();
      }
      isloading(false);
      return resp.data['status'];
    } catch (e) {
      logger.e(e);
      isloading(false);
      return false;
    }
  }

  Future<bool> deleteGroup(int groupId, Map<String, dynamic> payload) async {
    isloading(true);
    try {
      var resp = await request(
          url: "${ApiUrls.create_msg_group}$groupId",
          method: Method.DELETE,
          params: payload);
      logger.log(Level.debug, resp.data);
      if (resp.data['status']) {
        Get.back();
        Get.snackbar('success', resp.data['message'],
            backgroundColor: Colors.green);
        fetchMessageGroups();
      }
      isloading(false);
      return resp.data['status'];
    } catch (e) {
      logger.e(e);
      isloading(false);
      return false;
    }
  }
}

class CreateMessageGroup extends StatefulWidget {
  final SmsGroups? smsGroup;
  final bool view;
  final bool isEditing;
  final bool isChecking;
  final bool isCheckingAdding;
  const CreateMessageGroup(
      {super.key,
      this.smsGroup,
      this.view = false,
      this.isChecking = false,
      this.isCheckingAdding = false,
      this.isEditing = false});

  @override
  State<CreateMessageGroup> createState() => CreateMessageGroupState();
}

class CreateMessageGroupState extends State<CreateMessageGroup> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  //filter
  String firstNameFilter = '';
  String lastNameFilter = '';
  String phoneFilter = '';

  String myPhone = "";

  /// Your existing controllers
  final userController = Get.put(UserKittyController());
  final smsController = Get.put(BulkSMSController());
  final CreateMsgController controller = Get.put(CreateMsgController());

  /// Whether we are in "selection mode" (show checkboxes)
  bool _selectionMode = false;

  /// Keep track of indices that are selected
  Set<int> _selectedIndices = {};
  final editMode = false.obs;
  final title = "".obs;
  final desc = "".obs;
  @override
  void initState() {
    editMode((!widget.isEditing));
    if (widget.smsGroup != null) {
      final smsGroup = widget.smsGroup!;
      _nameController.text = smsGroup.name ?? '';
      _descriptionController.text = smsGroup.description ?? '';
      title.value = widget.view
          ? widget.smsGroup!.name ?? ''
          : '${widget.smsGroup != null ? "Edit" : "Create"} Message Group';
      desc.value = widget.smsGroup!.description ?? '';
    }
    if (widget.isChecking) {
      setState(() {
        _selectionMode = true;
        _selectedIndices = {
          for (int i = 0; i < controller.groupMembers.length; i++) i
        };
      });
    }
    // editMode(!widget.view);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: !widget.isChecking
          ? null
          : MyButton(
              width: 150.spMin,
              icon: Icons.done,
              onClick: () {
                List<SmsGroupMember> selectedContacts = [];
                for (var index in _selectedIndices) {
                  selectedContacts.add(controller.groupMembers[index]);
                }

                if (widget.isCheckingAdding) {
                  smsController.addSelectedContact(
                    context: context,
                    type: 'group',
                    group: SmsGroups(
                      id: widget.smsGroup?.id,
                      createdAt: widget.smsGroup?.createdAt,
                      updatedAt: widget.smsGroup?.updatedAt,
                      name: widget.smsGroup?.name,
                      description: widget.smsGroup?.description,
                      smsGroupMembers: selectedContacts,
                    ),
                  );
                  Get.back();
                  Get.back();
                } else {
                  smsController.selectedContacts
                      .where((e) => e.group?.id == widget.smsGroup?.id)
                      .toList()
                      .first
                      .group = SmsGroups(
                    id: widget.smsGroup?.id,
                    createdAt: widget.smsGroup?.createdAt,
                    updatedAt: widget.smsGroup?.updatedAt,
                    name: widget.smsGroup?.name,
                    description: widget.smsGroup?.description,
                    smsGroupMembers: selectedContacts,
                  );

                  Get.back();
                }
              },
              label: 'Done',
            ),
      appBar: AppBar(
        title: Obx(
          () => Text(   
            title.value != '' ? title.value :           '${widget.smsGroup != null ? "Edit" : "Create"} Group'
                
            
          ),
        ),
        elevation: 0,
        actions: widget.isChecking
            ? [
                IconButton(
                    tooltip: 'help',
                    onPressed: () => Get.to(() => const InfoPage()),
                    icon: const Icon(Icons.help)),
                if (!_selectionMode)
                  TextButton(
                      onPressed: () {
                        if (widget.smsGroup?.smsGroupMembers?.isEmpty ??
                            false) {
                          ToastUtils.showErrorToast(
                              context, 'Error', 'No members in group');
                          return;
                        }
                        smsController.addSelectedContact(
                          context: context,
                          type: 'group',
                          group: widget.smsGroup,
                        );
                        Get.back();
                        Get.back();
                      },
                      child: Text('Continue',
                          style: TextStyle(
                              color:
                                  isLight.value ? Colors.white : null))),
                if (!widget.isChecking) ...[
                  IconButton(
                      tooltip: 'help',
                      onPressed: () => Get.to(() => const InfoPage()),
                      icon: const Icon(Icons.help)),
                  // In the appBar actions section:

                  if (!_selectionMode)
                    PopupMenuButton(
                      icon: const Icon(Icons.more_vert),
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          child: Row(children: [
                            Icon(_selectionMode
                                ? Icons.close
                                : Icons.check_box_outlined),
                            Text(_selectionMode
                                ? 'Exit Selection'
                                : 'Multi-Select'),
                          ]),
                          onTap: () {
                            setState(() {
                              _selectionMode = !_selectionMode;
                              if (!_selectionMode) {
                                _selectedIndices.clear();
                              }
                            });
                          },
                        ),
                      ],
                    ),
                  _selectionMode
                      ? IconButton(
                          icon: const Icon(Icons.select_all),
                          tooltip: 'Select All / Deselect All',
                          onPressed: _toggleSelectAll,
                        )
                      : const SizedBox(),
                  _selectionMode
                      ? IconButton(
                          icon: const Icon(Icons.delete),
                          tooltip: 'Delete Selected',
                          onPressed: _selectedIndices.isEmpty
                              ? null
                              : () async {
                                  if (widget.isChecking) {}
                                  final confirmed =
                                      await _confirmDeleteSelected(context);
                                  if (confirmed) _deleteSelectedMembers();
                                },
                        )
                      : const SizedBox(),
                ]
              ]
            : !widget.view
                ? [
                    IconButton(
                        tooltip: 'help',
                        onPressed: () => Get.to(() => const InfoPage()),
                        icon: const Icon(Icons.help)),
                  ]
                : [
                    IconButton(
                        tooltip: 'help',
                        onPressed: () => Get.to(() => const InfoPage()),
                        icon: const Icon(Icons.help)),
                    if (!_selectionMode)
                      Obx(
                        () => IconButton(
                            onPressed: () {
                              editMode.value = !editMode.value;
                              if (editMode.value) {
                                _saveGroup();
                              }
                            },
                            icon: !editMode.value
                                ? const Icon(Icons.save)
                                : const Icon(Icons.edit)),
                      ),
                    Obx(
                      () => controller.isloading.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Row(
                              children: [
                                _selectionMode
                                    ? IconButton(
                                        icon: const Icon(Icons.select_all),
                                        tooltip: 'Select All / Deselect All',
                                        onPressed: _toggleSelectAll,
                                      )
                                    : const SizedBox(),
                                _selectionMode
                                    ? IconButton(
                                        icon: const Icon(Icons.delete),
                                        tooltip: 'Delete Selected',
                                        onPressed: _selectedIndices.isEmpty
                                            ? null
                                            : () async {
                                                final confirmed =
                                                    await _confirmDeleteSelected(
                                                        context);
                                                if (confirmed) {
                                                  _deleteSelectedMembers();
                                                }
                                              },
                                      )
                                    : const SizedBox(),
                                if (!_selectionMode)
                                  PopupMenuButton(
                                    icon: const Icon(Icons.more_vert),
                                    itemBuilder: (context) => [
                                      PopupMenuItem(
                                        child: Row(children: [
                                          Icon(_selectionMode
                                              ? Icons.close
                                              : Icons.check_box_outlined),
                                          Text(_selectionMode
                                              ? 'Exit Selection'
                                              : 'Multi-Select'),
                                        ]),
                                        onTap: () {
                                          setState(() {
                                            _selectionMode = !_selectionMode;
                                            if (!_selectionMode) {
                                              _selectedIndices.clear();
                                            }
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                              ],
                            ),
                    )
                  ],
      ),
      bottomNavigationBar: Padding(
        padding:   EdgeInsets.only(bottom:  30.0.h, left: 20.w, right: 20.w),
        child: widget.view
            ? null
            : SizedBox(
                width: double.infinity,
                child: Obx(
                  () => MyButton(
                    onClick: _saveGroup,
                    showLoading: isSaving.value,
                    label:
                        ('${widget.smsGroup != null ? "Edit" : "Create"}  Group'),
                  ),
                ),
              ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!widget.isChecking)
                Obx(
                  () => Column(children: [
                    if (editMode.value) ...[
                      Obx(
                        () =>
                            QuillEditorWidget(readMore: true, text: desc.value),
                      ),
                    ],
                    if (!editMode.value) ...[
                      TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: 'group_name'.tr,
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) => value?.isEmpty ?? true
                            ? 'please_enter_group_name'.tr
                            : null,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          labelText: 'description'.tr,
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ],
                    const SizedBox(height: 24),
                  ]),
                ),
              Obx(
                () => Row(
                  children: [
                    Text(
                      'group_members'.tr,
                      style:
                          const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    if (!editMode.value) ...[
                      const Spacer(),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                              onPressed: _addContact,
                              icon: const Icon(Icons.person_add_outlined)),
                          Text('add_contact'.tr,
                              style: const TextStyle(fontSize: 8)),
                        ],
                      ),
                      const SizedBox(width: 10),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            tooltip: 'Upload CSV/XLSX',
                            onPressed: _pickFileAndImportContacts,
                            icon: const Icon(Icons.upload_file),
                          ),
                          Text('csv_excel_file'.tr,
                              style: const TextStyle(fontSize: 8)),
                        ],
                      ),
                    ]
                  ],
                ),
              ),
              const SizedBox(height: 16),
              if (!_selectionMode && !widget.isChecking)
                Align(
                  alignment: Alignment.bottomRight,
                  child: IconButton(
                    icon: const Icon(Icons.search),
                    onPressed: () => _showFilterDialog(context),
                    tooltip: 'Search Members',
                  ),
                ),
              // const SizedBox(height: 16),
              Obx(
                () => controller.groupMembers.isEmpty
                    ? Center(
                        child: InkWell(
                          onTap: () {
                            _addContact();
                          },
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              CustomImageView(
                                imagePath: AssetUrl.imgGroup13,
                                height: 150.h,
                                width: 254.w,
                              ),
                              SizedBox(height: 16.h),
                              Text(
                                'add_members_to_group'.tr,
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : filteredMembers.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: filteredMembers.length + 1,
                            itemBuilder: (context, index) {
                              if (index == filteredMembers.length) {
                                return Obx(
                                  () => controller.isAddingMember.value
                                      ? const Skeletonizer(
                                          child: ListTile(
                                              title:
                                                  Text('adding adding adding'),
                                              subtitle: Text('${CountryConfig.dialCode}7000000000')),
                                        )
                                      : const SizedBox(height: 80),
                                );
                              }
                              final member = filteredMembers[index];
                              final isSelected =
                                  _selectedIndices.contains(index);
                              return Card(
                                color: isSelected
                                    ? AppColors.primary.withOpacity(0.6)
                                    : null,
                                child: ListTile(
                                  leading: _selectionMode
                                      ? Checkbox(
                                          value: isSelected,
                                          onChanged: (bool? value) {
                                            setState(() {
                                              if (value == true) {
                                                _selectedIndices.add(index);
                                              } else {
                                                _selectedIndices.remove(index);
                                              }
                                            });
                                          },
                                        )
                                      : null,
                                  title: Text(
                                    '${member.firstName} ${member.secondName}',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w600),
                                  ),
                                  subtitle: Text(member.phoneNumber ?? ''),
                                  onLongPress: () {
                                    if (!widget.isChecking) {
                                      setState(() {
                                        _selectionMode = true;
                                        _selectedIndices.add(index);
                                      });
                                    }
                                  },
                                  trailing: Obx(
                                    () => editMode.value
                                        ? const SizedBox()
                                        : _selectionMode
                                            ? const SizedBox()
                                            : Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  IconButton(
                                                    icon:
                                                        const Icon(Icons.edit),
                                                    onPressed: () =>
                                                        showEditMemberDialog(
                                                            context, member),
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(
                                                        Icons.delete),
                                                    onPressed: () =>
                                                        showDeleteMemberDialog(
                                                            context, member),
                                                  ),
                                                ],
                                              ),
                                  ),
                                ),
                              );
                            },
                          ),
              ),
              const SizedBox(height: 16),
              if (_selectionMode && !widget.isChecking)
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: _cancelSelectionMode,
                    child: const Text('Cancel Selection'),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Toggles select-all/deselect-all.
  void _toggleSelectAll() {
    setState(() {
      if (_selectedIndices.length == controller.groupMembers.length) {
        _selectedIndices.clear();
      } else {
        _selectedIndices = {
          for (int i = 0; i < controller.groupMembers.length; i++) i
        };
      }
    });
  }

  /// Cancels selection mode and clears selected indices.
  void _cancelSelectionMode() {
    setState(() {
      _selectionMode = false;
      _selectedIndices.clear();
    });
  }

  /// Opens a confirmation dialog to delete selected members
  Future<bool> _confirmDeleteSelected(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (_) => AlertDialog(
            title: const Text('Delete Selected Members'),
            content: const Text(
                'Are you sure you want to delete all selected members?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              Obx(
                () => TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: controller.isloading.value
                      ? const CircularProgressIndicator()
                      : const Text('Delete'),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// Deletes selected members from the controller
  void _deleteSelectedMembers() async {
    final itemsToRemove = _selectedIndices
        .map((idx) => controller.groupMembers[idx])
        .toList(growable: false);

    controller.groupMembers
        .removeWhere((member) => itemsToRemove.contains(member));
    if (widget.isChecking) {
      setState(() {
        _selectedIndices.clear();
        _selectionMode = false;
      });
      return;
    }

    // _saveGroup();
    controller.deleteGroup(widget.smsGroup?.id ?? 0, {
      "sms_group_members": itemsToRemove.map((e) => e.toJson()).toList(),
    });
    setState(() {
      _selectedIndices.clear();
      _selectionMode = false;
    });
  }

  Future<void> _addContact() async {
    showDialog(
        context: context,
        builder: (context) {
          return Dialog(child: _buildAddMemberSection(context));
        });
  }

  // clear filter
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          CustomImageView(
            imagePath: AssetUrl.imgGroup13,
            height: 150.h,
            width: 254.w,
          ),
          SizedBox(height: 16.h),
          Text(
            'No members found matching your filter',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (firstNameFilter.isNotEmpty ||
              lastNameFilter.isNotEmpty ||
              phoneFilter.isNotEmpty)
            TextButton(
              onPressed: () {
                setState(() {
                  firstNameFilter = '';
                  lastNameFilter = '';
                  phoneFilter = '';
                });
              },
              child: const Text('Clear Filters'),
            ),
        ],
      ),
    );
  }

  /// Picks CSV/XLSX file using FilePicker, parses, and imports into group
  Future<void> _pickFileAndImportContacts() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['csv', 'xlsx'],
    );
    if (result == null) return;

    File file = File(result.files.single.path!);
    String extension = result.files.single.extension!.toLowerCase();
    bool foundDuplicates = false;

    if (extension == 'csv') {
      final input = file.openRead();
      final fields = await input
          .transform(utf8.decoder)
          .transform(const CsvToListConverter())
          .toList();
      setState(() {
        for (var row in fields.skip(1)) {
          final contact = SmsGroupMember(
            firstName: row[0] ?? '',
            secondName: row[1] ?? '',
            phoneNumber: row[2] ?? '',
          );
          if (!controller.groupMembers.contains(contact)) {
            controller.groupMembers.add(contact);
          } else {
            foundDuplicates = true;
          }
        }
        if (foundDuplicates) {
          ToastUtils.showToast('Duplicates found');
        }
      });
    } else if (extension == 'xlsx') {
      var bytes = file.readAsBytesSync();
      var excel = ex.Excel.decodeBytes(bytes);
      var sheet = excel.tables[excel.tables.keys.first]!;
      for (var row in sheet.rows.skip(1)) {
        final contact = SmsGroupMember(
          firstName: row[0]?.value?.toString() ?? '',
          secondName: row[1]?.value?.toString() ?? '',
          phoneNumber: row[2]?.value?.toString() ?? '',
        );
        if (!controller.groupMembers.contains(contact)) {
          controller.groupMembers.add(contact);
        } else {
          foundDuplicates = true;
        }
      }
      if (foundDuplicates) {
        ToastUtils.showToast('Duplicates found');
      }
    }
  }

  /// Shows a dialog to edit an existing member
  void showEditMemberDialog(BuildContext context, SmsGroupMember member) {
    // final member = controller.groupMembers[index];
    final firstNameController = TextEditingController(text: member.firstName);
    final secondNameController = TextEditingController(text: member.secondName);
    final phoneController = TextEditingController(
        text: member.phoneNumber?.replaceFirst(CountryConfig.dialCode, ''));
    final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Member'),
        content: Form(
          key: _formKey2,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: firstNameController,
                decoration: const InputDecoration(labelText: 'First Name'),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: secondNameController,
                decoration: const InputDecoration(labelText: 'Second Name'),
              ),
              const SizedBox(height: 8),
              // TextField(
              //   controller: ,
              //   decoration: const InputDecoration(labelText: 'Phone Number'),
              // ),
              TextFormField(
                keyboardType: TextInputType.number,
                maxLength: 14,
                controller: phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  prefixText: '+${CountryConfig.dialCode} ',
                  border: OutlineInputBorder(),
                  counterText: '',
                  helperText: 'Format: 0722XXX or 722XXX',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Phone number is required';
                  }
                  String number = value.startsWith('0') ? value : '0$value';
                  if (!RegExp(r'^0[17][0-9]{8}$').hasMatch(number)) {
                    return 'Enter valid phone number';
                  }
                  return null;
                },
                onChanged: (value) {
                  String normalized =
                      value.startsWith('0') ? value.substring(1) : value;
                  setState(() {
                    myPhone = '${CountryConfig.dialCode}$normalized';
                  });
                },
              ),

              const SizedBox(height: 8),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (_formKey2.currentState!.validate()) {
                if (controller.hasDuplicate(myPhone)) {
                  ToastUtils.showErrorToast(
                      context, 'Error', 'Duplicate found');
                  return;
                }
               
                controller.editGroupMember({
                  "ID": member.id,
                  "sms_group_id": widget.smsGroup?.id,
                  "first_name": firstNameController.text,
                  "second_name": secondNameController.text,
                  "phone_number": myPhone,
                });
                 controller.groupMembers
                    .where((e) => e.id == member.id)
                    .toList()
                    .first = SmsGroupMember(
                  firstName: firstNameController.text,
                  secondName: secondNameController.text,
                  phoneNumber: myPhone,
                );
                Navigator.pop(context);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// Shows a dialog to confirm deleting a single member
  void showDeleteMemberDialog(BuildContext context, SmsGroupMember member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Member'),
        content: const Text('Are you sure you want to delete this member?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              controller.deleteGroupMember(
                  member.toJson(), widget.smsGroup?.id ?? 0);
              controller.groupMembers
                  .removeWhere((element) => element == member);
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// "Save" (Create or Edit) the Group
  ///
  final isSaving = false.obs;
  Future<void> _saveGroup() async {
    if (_formKey.currentState?.validate() ?? false) {
      isSaving(true);
      if (controller.groupMembers.isEmpty) {
        Get.snackbar(
          'Error',
          'Please add at least one member to the group',
          backgroundColor: Colors.red,
        );
        isSaving(false);
        return;
      }
      final groupData = widget.smsGroup != null
          ? {
              "ID": widget.smsGroup?.id,
              "phone_number": widget.smsGroup?.phoneNumber,
              "external_user_id": widget.smsGroup?.externalUserId,
              "is_active": widget.smsGroup?.isActive,
              "name": _nameController.text,
              "description": _descriptionController.text,
              "sms_group_members": controller.groupMembers,
            }
          : {
              "name": _nameController.text,
              "phone_number":
                  Get.find<Eventcontroller>().getLocalUser()?.phoneNumber,
              "external_user_id":
                  Get.find<Eventcontroller>().getLocalUser()?.id,
              "is_active": true,
              "description": _descriptionController.text,
              "sms_group_members": controller.groupMembers,
            };
      title(_nameController.text);
      desc(_descriptionController.text);
      await controller
          .createMessageGroup(
              payload: groupData,
              edit: widget.smsGroup != null,
              close: !widget.view)
          .whenComplete(() => isSaving(false));
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// Build widget for phone number input and "Add Member" button
  Widget _buildAddMemberSection(BuildContext context) {
    final phoneController = TextEditingController();
    final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
    final firstName = TextEditingController();
    final lastName = TextEditingController();
    return Form(
      key: _formKey2,
      child: Container(
        width: double.infinity,
        height: 300,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: firstName,
                    decoration: const InputDecoration(
                      labelText: 'First Name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: lastName,
                    decoration: const InputDecoration(
                      labelText: 'Last Name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              keyboardType: TextInputType.number,
              maxLength: 14,
              controller: phoneController,
              decoration: InputDecoration(
                labelText: 'Phone Number',
                prefixText: '${CountryConfig.dialCode} ',
                border: const OutlineInputBorder(),
                counterText: '',
                helperText: 'Format: 0722XXX or 722XXX',
                suffixIcon: IconButton(
                  icon: const Icon(Icons.contacts_outlined),
                  onPressed: () async {
                    final List<Contact>? selectedContacts = await Get.to(() => const GetXContactPicker(
                      mode: ContactPickerMode.multiple,
                      display: ContactPickerDisplay.fullScreen,
                      title: 'Select Group Contacts',
                    ));

                    if (selectedContacts != null && selectedContacts.isNotEmpty) {
                      // Handle the selected contacts - add them to the group
                      for (var contact in selectedContacts) {
                        if (contact.phones.isNotEmpty) {
                          String phoneNumber = contact.phones.first.number;
                          // Remove any formatting and ensure it starts with 0
                          phoneNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
                          if (phoneNumber.startsWith('${CountryConfig.dialCodeMinus}')) {
                            phoneNumber = '0${phoneNumber.substring(3)}';
                          } else if (!phoneNumber.startsWith('0')) {
                            phoneNumber = '0$phoneNumber';
                          }

                          // Add the contact to the group
                          // You may need to call a method to add the contact to your group here
                          // For example: addContactToGroup(contact, phoneNumber);
                        }
                      }
                      setState(() {}); // Refresh UI to show selected contacts
                    }
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Phone number is required';
                }
                String number = value.startsWith('0') ? value : '0$value';
                if (!RegExp(r'^0[17][0-9]{8}$').hasMatch(number)) {
                  return 'Enter valid phone number';
                }
                return null;
              },
              onChanged: (value) {
                String normalized =
                    value.startsWith('0') ? value.substring(1) : value;
                setState(() {
                  myPhone = '${CountryConfig.dialCode}$normalized';
                });
              },
            ),
            const SizedBox(height: 24),
            MyButton(
              onClick: () {
                if (_formKey2.currentState!.validate()) {
                  if (controller.hasDuplicate(myPhone)) {
                    ToastUtils.showErrorToast(
                        context, 'Error', 'Duplicate found');
                    return;
                  }

                  if (myPhone ==
                      Get.find<Eventcontroller>().getLocalUser()?.phoneNumber) {
                    ToastUtils.showErrorToast(
                        context, 'Error', 'You cannot add yourself');
                    return;
                  }
                  if (controller.groupMembers.any((member) => member.phoneNumber == myPhone)) {
                    ToastUtils.showErrorToast(context, 'Error', 'Duplicate member found');
                    return;
                  }
                  if (widget.smsGroup != null) {
                    controller.addGroupMember(
                      {
                        'phone_number': myPhone,
                        'first_name': firstName.text,
                        'second_name': lastName.text,
                        "sms_group_id": widget.smsGroup?.id ?? 0,
                      },
                    ).onError((e, s) {
                      Get.snackbar('Error', 'An error occured');
                      return;
                    });
                  } else {
                    controller.groupMembers.add(SmsGroupMember(
                      firstName: firstName.text,
                      secondName: lastName.text,
                      phoneNumber: myPhone,
                    ));
                  }

                  setState(() {
                    phoneController.clear();
                    firstName.clear();
                    lastName.clear();
                  });
                  Get.back();
                } else {
                  ToastUtils.showErrorToast(
                      context, "Error", "Enter a valid number");
                }
              },
              label: 'Add Member',
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    final firstNameController = TextEditingController(text: firstNameFilter);
    final lastNameController = TextEditingController(text: lastNameFilter);
    final phoneController = TextEditingController(text: phoneFilter);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Members'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            MyTextField(
              controller: firstNameController,
              title: 'First Name',
              hint: 'Search by first name',
            ),
            MyTextField(
              controller: lastNameController,
              title: 'Last Name',
              hint: 'Search by last name',
            ),
            MyTextField(
              controller: phoneController,
              title: 'Phone Number',
              hint: 'Search by phone',
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                firstNameFilter = firstNameController.text;
                lastNameFilter = lastNameController.text;
                phoneFilter = phoneController.text;
              });
              Navigator.pop(context);
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  List<SmsGroupMember> get filteredMembers {
    return controller.groupMembers.where((member) {
      final firstNameMatch = member.firstName
              ?.toLowerCase()
              .contains(firstNameFilter.toLowerCase()) ??
          false;
      final lastNameMatch = member.secondName
              ?.toLowerCase()
              .contains(lastNameFilter.toLowerCase()) ??
          false;
      final phoneMatch = member.phoneNumber?.contains(phoneFilter) ?? false;

      return firstNameMatch && lastNameMatch && phoneMatch;
    }).toList();
  }
}
