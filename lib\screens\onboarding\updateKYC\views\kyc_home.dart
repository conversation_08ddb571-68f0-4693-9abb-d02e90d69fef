import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/kyc_controller.dart';
import 'capture_front.dart';
import '../widgets/clay_button.dart';
import '../widgets/clay_progress_bar.dart';

class KYCHomePage extends StatelessWidget {
  

const    KYCHomePage({super.key});

  @override
  Widget build(BuildContext context) {
  final KYCController controller = Get.find<KYCController>();
    return Scaffold(
      appBar: AppBar(title: Text('kyc_verification'.tr)),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: <PERSON>umn(
          children: [
            const ClayProgress(currentStep: 1, totalSteps: 5),
            const <PERSON><PERSON><PERSON><PERSON>(height: 30),
            Text<PERSON>ield(
              controller: controller.idNumber,
              decoration: InputDecoration(
                labelText: 'id_number'.tr,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
              ),
            ),),
              const Si<PERSON><PERSON><PERSON>(height: 40),
            Clay<PERSON>utton(
              text: 'start_verification'.tr,
              onPressed: () => Get.to(() => const CaptureFrontIDPage()
              ),
            )
          ],
        ),
      ),
    );
  }
}