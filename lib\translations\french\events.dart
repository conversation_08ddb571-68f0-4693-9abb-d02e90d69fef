import 'package:onekitty/configs/country_specifics.dart';

const Map<String, String> eventsFrench = {
  'step_of': 'ÉTAPE {current} SUR {total}',
  'scan_fingerprint_authenticate': 'Scannez votre empreinte digitale pour vous authentifier',
  'biometric_authentication_not_available':
      'L\'authentification biométrique n\'est pas disponible sur cet appareil',
  'biometric_credentials_not_setup':
      'Les identifiants biométriques ne sont pas configurés sur cet appareil',
  'no_biometric_credentials_enrolled':
      'Aucun identifiant biométrique n\'est enregistré sur cet appareil',
  'biometric_authentication_failed':
      'L\'authentification biométrique a échoué : {message}',
  'website_url_not_valid':
      'L\'URL {field} n\'est pas valide. Veuillez entrer une URL valide (ex. https://example.com)',
  'url_not_valid_platform':
      'L\'URL {field} ne semble pas être un lien {platform} valide',
  'media_upload_incomplete':
      'Le téléchargement du média {index} est incomplet. Veuillez attendre la fin du téléchargement ou le supprimer.',
  'media_invalid_url':
      'Le média {index} a une URL invalide. Veuillez re-télécharger le média.',
  'event_minimum_30_minutes': 'L\'événement doit durer au moins 30 minutes',
  'please_select_location_map': 'Veuillez sélectionner un emplacement sur la carte',
  'invalid_event_details': 'Détails de l\'événement invalides',
  'failed_to_share_content': 'Échec du partage du contenu',
  'failed_to_launch_url': 'Échec du lancement de l\'URL',
  'check_out_this_event': 'Découvrez cet événement : {url}',
  'event_default': 'Événement',
  'kenya_awards_night_example': 'ex. Soirée des prix du ${CountryConfig.name}',
  'info_awards_example': 'ex. <EMAIL>',
  'website_hint': 'https://votresite.com',
  'facebook_hint': 'https://facebook.com/votrepage',
  'x_hint': 'https://x.com/votrecompte',
  'instagram_hint': 'https://instagram.com/votrecompte',
  'tiktok_hint': 'https://tiktok.com/@votrecompte',
  'failed_to_add_ticket': 'Échec de l\'ajout du billet : {error}',
  'club_member_example': 'ex. Pour membre du club',
  'size_label': 'Taille',
  'price_500_example': '500',
  'date_time_example': '13/2/2024 14:00',
  'ticket_type_label': 'Type de billet',
  'group_size_label': 'Taille du groupe',
  'ticket_description_label': 'Description du billet',
  'ticket_description_required_msg': 'La description du billet est requise',
  'slots_available_label': 'places disponibles',
  'limited_slots_label': 'Places limitées',
  'slots_required_msg': 'Les places sont requises',
  'price_required_msg': 'Le prix est requis',
  'price_label': 'Prix',
  'purchase_start_date_required_msg': 'La date de début d\'achat est requise',
  'purchase_start_date_label': 'Date de début d\'achat',
  'date_example': '13/2/2024',
  'purchase_end_date_required_msg': 'La date de fin d\'achat est requise',
  'purchase_end_date_label': 'Date de fin d\'achat',
  'edit_ticket_label': 'Modifier le billet',
  'must_pick_ticket_type_proceed': 'Vous devez choisir un type de billet pour continuer',
  'event_date_time_example': '13/2/2024 2:00 PM',
  'kicc_example': 'ex. KICC',
  'enter_location': 'Entrer l\'emplacement',
  'enter_referral_code': 'Entrer le code de parrainage',
  'wait_for_media_uploads_complete':
      'Veuillez attendre que tous les téléchargements de médias soient terminés',
  'please_fill_all_required_fields': 'Veuillez remplir tous les champs requis',
  'end_date_must_be_after_start_date': 'La date de fin doit être après la date de début',
  'events_media_limit': 'Médias d\'événements : {message}',
  'file_size_exceeds_5mb': 'La taille du fichier dépasse la limite de 5 Mo',
  'event_start_date_time_required': 'La date et l\'heure de début de l\'événement sont requises',
  'event_start_date_time_label': 'Date et heure de début de l\'événement',
  'event_end_date_time_required': 'La date et l\'heure de fin de l\'événement sont requises',
  'event_end_date_time_label': 'Date et heure de fin de l\'événement',
  'event_venue_is_required': 'Le lieu de l\'événement est requis',
  'venue_label': 'Lieu',
  'location_is_required': 'L\'emplacement est requis',
  'location_tip_label': 'Conseil d\'emplacement',
  'search_location': 'Rechercher un emplacement',
  'date_time_pm_example': '13/2/2024 02:00 PM',
  'collected': 'Collecté',
  'please_wait_fetching_data': 'Veuillez patienter pendant que nous récupérons les données',
  'please_try_again_later': 'Veuillez réessayer plus tard',
  'transaction_id': 'ID de transaction',
  'contribution_details': 'Détails de la contribution',
  'error_generating_poster': 'Erreur lors de la génération de l\'affiche. Veuillez réessayer.',
  'user_details_for_phone': 'Détails de l\'utilisateur {phone}',
  'first_name_is_required': 'Le prénom est requis',
  'email_is_required': 'L\'email est requis',
  'please_enter_valid_email': 'Veuillez entrer une adresse email valide',
  'invite_member_phone': 'Inviter un membre - {phone}',
  'first_name_label': 'Prénom : ',
  'please_enter_first_name': 'Veuillez entrer un prénom',
  'please_enter_last_name': 'Veuillez entrer un nom de famille',

  'admin_lock_warning':
      'Une fois qu\'un administrateur est verrouillé, il ne peut pas être modifié ou supprimé sans contacter le support client. Il s\'agit d\'une fonctionnalité de sécurité pour empêcher les modifications non autorisées par les comptes administrateur.',

  'view_location': 'Voir l\'emplacement',
  'pick_location': 'Choisir l\'emplacement',
  'please_select_location': 'Veuillez sélectionner un emplacement',
  'select_payment_method': 'Sélectionner la méthode de paiement',
  'full_names_optional': 'Noms complets (Optionnel)',
  'email_address_optional': 'Adresse email (Optionnel)',
  'full_names': 'Noms complets',
  'success_stk_push_sent': 'Succès. STK PUSH ENVOYÉ',
  'didnt_receive_stk_push': 'Vous n\'avez pas reçu ce STK push ?',
  'kindly_wait_stk_push': 'Veuillez attendre le STK PUSH',
  'stk_sent_enter_pin':
      'STK {method} envoyé. Entrez votre PIN pour terminer la transaction',
  'grant_permission': 'Accorder la permission',
  'cancel_scanning': 'Annuler le scan',
  'loading_transactions': 'Chargement des transactions...',
  'approved': 'Approuvé',
  'no_pending_transactions': 'Aucune transaction en attente',
  'no_approved_transactions': 'Aucune transaction approuvée',
  'enter_your_comment': 'Entrez votre commentaire',
  'verify_ticket': 'Vérifier le billet',
  'enter_ticket_code': 'Entrer le code du billet',
  'enter_code_here': 'Entrer le code ici',
  'verify': 'Vérifier',
  'swipe_to_scan': 'Glisser pour scanner ',
  'verify_ticket_confirm': 'Confirmer la vérification du billet',
  'qty': 'Qté : {quantity}',
  'verify_ticket_button': 'Vérifier le billet',
  'error_generating_qr_code': 'Erreur lors de la génération du code QR : {error}',
  'share_url': 'Partager l\'URL',
  'organizer': 'Organisateur',
  'buy_ticket': 'Acheter un billet',
  'available_tickets': 'Billets disponibles :',
  'no_tickets_available_moment': 'Aucun billet disponible pour le moment',
  'reserve': 'Réserver',
  'checkout': 'Payer {amount}',
  'successfully_payed': 'Paiement réussi !',
  'error_occurred_while_paying': 'Une erreur s\'est produite lors du paiement',
  'contributions': 'Contributions',
  // Time-related translations for highPrecisiontimeSince function
  'left': 'restant',
  // Singular forms
  'second': 'seconde',
  'minute': 'minute',
  'hour': 'heure',
  'day': 'jour',
  'week': 'semaine',
  'month': 'mois',
  'year': 'année',

  // Plural forms
  'seconds': 'secondes',
  'minutes': 'minutes',
  'hours': 'heures',
  'days': 'jours',
  'weeks': 'semaines',
  'months': 'mois',
  'years': 'années',
  'view_all_statistics': 'Voir toutes les statistiques',
};