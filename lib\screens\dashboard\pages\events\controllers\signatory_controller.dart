import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';

import '../../../../../models/events/signatory_transaction_model.dart';
import '../../../../../services/api_urls.dart';
import 'events_controller.dart';

class SignatoryTransactionController extends GetxController
    implements GetxService {
      final kittyId = 0.obs;
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  RxBool isLoading = false.obs;
  RxBool isLoadingMore = false.obs;
  RxBool isProcessing = false.obs;
  RxBool comment = true.obs;
  RxList<SignatoryTransaction> signatoryTransactions =
      <SignatoryTransaction>[].obs;
  final GetStorage box = Get.find();
  Future processTransaction(
      {required bool isApproved,
      required String comment,
      required SignatoryTransaction signatoryTransaction}) async {
    isProcessing(true);
    try {
      final eventController = Get.find<Eventcontroller>();

      var response = await apiProvider.request(
          url:
              "${ApiUrls.PROCESSSIGNATORYTRANSACTIONS}?kitty_id=${signatoryTransaction.kittyId}",
          method: Method.POST,
          params: {
            "signatory_transaction_id": signatoryTransaction.id,
            "delegate_phone": eventController.getLocalUser()?.phoneNumber,
            "checkout_request_id": "",
            "is_approved": isApproved,
            "comment": comment,
            "user_id": eventController.getLocalUser()?.id,
            "kitty_id": signatoryTransaction.kittyId,
            "latitude": box.read(CacheKeys.lat),
            "longitude": box.read(CacheKeys.long),
            "device_id": box.read(CacheKeys.deviceId),
            "device_model": box.read(CacheKeys.deviceModel)
          });
      if (response.data['status'] ?? false) {
        isProcessing(false);
        Get.snackbar('Success', '', backgroundColor: Colors.green);
      } else {
        Get.snackbar('error', '${response.data['message']}', backgroundColor: Colors.red);
        
      }
    } catch (e) {
      
      Get.snackbar('error', 'An error occured', backgroundColor: Colors.red);
      logger.e('$e');
    } finally {
      isProcessing(false);
    }
  }
// Add these controller variables
final currentPagePending = 1.obs;
final currentPageApproved = 1.obs;
final hasMorePending = true.obs;
final hasMoreApproved = true.obs;
final perPage = 10.obs;
final ScrollController pendingScrollController = ScrollController();
final ScrollController approvedScrollController = ScrollController();

@override
void onInit() {
  super.onInit();
  pendingScrollController.addListener(_scrollListenerPending);
  approvedScrollController.addListener(_scrollListenerApproved);
}

final isFetchngSignatories = false.obs;

Future fetchSignatoryTransactions( int kittyId,{int page = 0, String? status}) async {
  if (page == 1) {
    isLoading(true);
    if (status == null) {
      signatoryTransactions.clear();
    }
  } else {
    isLoadingMore(true);
  }

  try {
    isFetchngSignatories(true);    
    final url = "${ApiUrls.SIGNATORYTRANSACTIONS}?"
      "kitty_id=$kittyId&"
      "page=$page&"
      "per_page=${perPage.value}"
      // "${status != null ? '&status=$status' : ''}"
      
      ;

    var response = await apiProvider.request(
      url: url,
      method: Method.GET,
    );

    if (response.data['status'] ?? false) {
      final newItems = (response.data['data']['items'] as List)
          .map((item) => SignatoryTransaction.fromJson(item))
          .toList();

      if (page == 1) {
        signatoryTransactions.assignAll(newItems);
      } else {
        signatoryTransactions.addAll(newItems);
      }

      // Update pagination flags
      if (status == null) {
        hasMorePending.value = newItems.length >= perPage.value;
      } else {
        hasMoreApproved.value = newItems.length >= perPage.value;
      }
    }
  } finally {
    isFetchngSignatories(false);  
  }
}


void _scrollListenerPending() {
  if (pendingScrollController.position.pixels == 
      pendingScrollController.position.maxScrollExtent) {
    if (hasMorePending.value && !isLoadingMore.value) {
      currentPagePending.value++;
      fetchSignatoryTransactions(kittyId.value,page: currentPagePending.value);
    }
  }
}

void _scrollListenerApproved() {
  if (approvedScrollController.position.pixels == 
      approvedScrollController.position.maxScrollExtent) {
    if (hasMoreApproved.value && !isLoadingMore.value) {
      currentPageApproved.value++;
      fetchSignatoryTransactions(kittyId.value,page: currentPageApproved.value, status: 'approved');
    }
  }
}
    }
    
