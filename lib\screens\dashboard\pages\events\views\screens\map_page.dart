import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:latlong2/latlong.dart';
import 'package:onekitty/utils/my_button.dart';

class MapScreen extends StatefulWidget {
  final double? latitude;
  final double? longitude;
  final bool? viewOnly;
  const MapScreen(
      {super.key, this.latitude, this.longitude, this.viewOnly = false});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  late LatLng _initialPosition;
  late MapController _mapController;
  LatLng? _selectedLocation;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
    _initialPosition = widget.latitude != null && widget.longitude != null
        ? LatLng(widget.latitude!, widget.longitude!)
        : const LatLng(-1.2921, 36.8219);
    _selectedLocation = widget.latitude != null && widget.longitude != null
        ? LatLng(widget.latitude!, widget.longitude!)
        : null;
    if (kDebugMode) {
      print(
          '###########################\ninitialPosition: $_initialPosition\nselectedLocation: $_selectedLocation');
    }
  }

  @override
  void dispose() {
    _mapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: widget.viewOnly ?? false
            ? Text('view_location'.tr)
            : Text('pick_location'.tr),
        actions: [
          if ((widget.viewOnly ?? false) == false)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: MyButton(
                label: 'done'.tr,
                icon: Icons.check,
                onClick: () {
                  if (_selectedLocation != null) {
                    Navigator.of(context).pop({
                      "latitude": _selectedLocation!.latitude,
                      "longitude": _selectedLocation!.longitude,
                    });
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('please_select_location'.tr)),
                    );
                  }
                },
              ),
            ),
        ],
      ),
      body: FlutterMap(
        mapController: _mapController,
        options: MapOptions(
          initialCenter: _initialPosition,
          initialZoom: 12.0,
          onTap: (tapPosition, latLng) {
            if (widget.viewOnly ?? false) {
              return;
            }
            setState(() {
              _selectedLocation = latLng;
            });
          },
        ),
        children: [
          TileLayer(
            urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            subdomains: const ['a', 'b', 'c'],
          ),
          MarkerLayer(
            markers: _selectedLocation != null
                ? [
                    Marker(
                      width: 80.0,
                      height: 80.0,
                      point: _selectedLocation!,
                      child: const Icon(Icons.location_pin,
                          color: Colors.red, size: 40),
                    ),
                  ]
                : [],
          ),
        ],
      ),
    );
  }
}
