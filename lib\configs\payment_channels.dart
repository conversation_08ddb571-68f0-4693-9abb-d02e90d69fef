import 'package:get/get.dart';
import 'package:onekitty/models/auth/payments_channels.dart';

class PaymentChannel extends GetxController {
  final supportsTill = true.obs;
  final supportsPaybill = true.obs;
  final supportBanks = true.obs;
  final supportsMobile = true.obs;
  List<String> paymentGateways() {
    final list = <String>[];
    if (supportsMobile.value) {
      list.add("Mobile");
    }
  
    if (supportsPaybill.value) {
      list.add("Paybill");
    }
    if (supportsTill.value) {
      list.add("Till");
    }  if (supportBanks.value) {
      list.add("Bank");
    }
    return list;
  }

  final paymentChannels = [
    //KENYA
    PaymentChannels(
      name: "Mpesa",
      imageUrl: "assets/images/mpesa.png",
      channelCode: 63902,
    ),
    PaymentChannels(
      name: "Airtel Money",
      imageUrl: "assets/images/airtel-money.png",
      channelCode: 63903,
    ),
    PaymentChannels(
        name: "Card",
        imageUrl: "assets/images/credit_card.png",
        channelCode: 55,
        typeIn: false),

    //BURUNDI
    /*
    PaymentChannels(
      name: "EcoCash",
      imageUrl: "assets/images/burundi/ecocash.png",
      channelCode: 63901,
    ),
    PaymentChannels(
      name: "IBB Mobile +",
      imageUrl: "assets/images/burundi/ibbpng.png",
      channelCode: 63902,
    ),
    PaymentChannels(
      name: "LumiCash",
      imageUrl: "assets/images/burundi/lumicashpng.png",
      channelCode: 63903,
    ),*/
    
  ].obs;

  List<PaymentChannels> get getPaymentChannels => paymentChannels;

  void setPaymentChannels(List<PaymentChannels> newpaymentChannels) {
    paymentChannels.value = newpaymentChannels;
    update();
  }

  String getPaymentChannelName(int channelCode) {
    final channel = paymentChannels.firstWhere(
      (element) => element.channelCode == channelCode,
      orElse: () => PaymentChannels(
        name: "Card",
        imageUrl: "assets/images/credit_card.png",
        channelCode: 55,
      ),
    );
    return channel.name;
  }
}
