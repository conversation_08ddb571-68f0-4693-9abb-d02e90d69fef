import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/contribution_kitties/beneficiaries_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/withdraw/withdraw.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/edit_kitty/end_date.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/edit_kitty/step1.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/manage_delegates.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/signatory_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/view_single_event_organizer.dart' as e;
import 'package:onekitty/screens/dashboard/pages/transactions/models/transaction_type.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/views/widgets/transaction_export_widget.dart';
import '../../../../../../../../utils/utils_exports.dart';

    

class ServicesWidget extends StatelessWidget {
  const ServicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final hasSignatoryTransactions =
        Get.find<ContributeController>().hasSignatoryTransactions;
    return Column(
      children: [
        SizedBox(height: 8.h),
        GridView.count(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          children: [
            e.ServicesWidget(
                icon: Icons.person,
                image: AssetUrl.imgCalendar,
                label: "edit_end_date".tr,
                onTap: () {
                  Get.to(()=> const EndDate()
                      );
                }),
            e.ServicesWidget(
              icon: Icons.person,
              image: AssetUrl.imgEdit,
              label: "edit_details".tr,
              onTap: () {
                Get.to(()=>const EditKittyDetails());
              },
            ),
            e.ServicesWidget(
              icon: Icons.person,
              image: AssetUrl.imgUser,
              label: "withdraw".tr,
              onTap: () {
              Get.to(()=>const WithdrawPage());
              },
            ),
            e.ServicesWidget(
                icon: Icons.group_outlined,
                label: "beneficiaries".tr,
                onTap: () {
                 Get.to(() => const BeneficiariesPage());
                }),
            e.ServicesWidget(
                icon: Icons.manage_accounts_outlined,
                label: "delegates".tr,
                onTap: () {
                 Get.to(() =>  InvitePage(
                                type: 'kitty',
                                kittyId: Get.find<DataController>()
                                        .kitty
                                        .value
                                        .kitty
                                        ?.iD ??
                                    0,
                                eventname: Get.find<DataController>()
                                        .kitty
                                        .value
                                        .kitty
                                        ?.title ??
                                    '',
                              ))
                    ;
                }),
            Obx(() => e.ServicesWidget(
                  flash: hasSignatoryTransactions.value,
                  image: AssetUrl.imgGroup7,
                  icon: Icons.person,
                  label: "signatory_approvals".tr,
                  onTap: () {
                  Get.to(()=> SignatoryTransactions(
                                  kittyId: Get.find<DataController>()
                                          .kitty
                                          .value
                                          .kitty
                                          ?.iD ??
                                      0,
                                ))
                       ;
                  },
                )),
            e.ServicesWidget(
                icon: Icons.monetization_on_outlined,
                label: "contribute".tr,
                onTap: () {
                  final kittyId =
                      Get.find<DataController>().kitty.value.kitty?.iD ?? 0;
                  String route = NavRoutes.kittycontributionScreen;
                  route = route.replaceAll(":id", kittyId.toString());
                  Get.toNamed(route);
                }),
            e.ServicesWidget(
                icon: Icons.import_export,
                label: "export_transactions".tr,
                onTap: () {
                  final kittyId =
                      Get.find<DataController>().kitty.value.kitty?.iD ?? 0;

                  showDialog(
                    context: context,
                    barrierDismissible: true,
                    builder: (context) =>  Dialog(
                      child: SizedBox(
                        height: 100,
                        width: 100,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Center(child: CircularProgressIndicator()),
                            const SizedBox(height: 16),
                            Text('fetching_transactions'.tr),
                          ],
                        ),
                      ),
                    ),
                  );
                  Get.put(KittyController())
                      .getKittyContributions(kittyId: kittyId)
                      .then((_) {
                    Navigator.pop(context); // Dismiss loading dialog
                    showModalBottomSheet(
                        isScrollControlled: true,
    
                      context: context,
                      builder: (BuildContext context) {
                        return TransactionExportWidget(
                          transactions : Get.find<KittyController>().transactionsKitty,
                          singleTrans: false,
                          entityTitle: Get.find<DataController>().kitty.value.kitty?.title, 
                          entityId: Get.find<DataController>().kitty.value.kitty?.iD,
                          kitty: Get.find<DataController>().kitty.value.kitty,
                          transactionType: TransactionType.kitty,
                           controllerTag: '',
                        
                        );
                      },
                    );
                  }).catchError((_) {
                    Navigator.pop(context); // Dismiss loading dialog on error
                  });
                }),
          ],
        ),
      ],
    );
  }
}


