import 'dart:convert' show jsonDecode;
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_quill_delta_from_html/parser/html_to_delta.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../utils/iswysiwyg.dart';  
class QuillEditorWidget extends StatelessWidget {
  final String? text;
  final int maxLines;
  final bool readMore;
  final String? tag; // Add tag parameter for unique controller instances

  const QuillEditorWidget({
    super.key,
    this.text,
    this.readMore = true,
    this.maxLines = 3,
    this.tag, // Optional tag parameter
  });

  @override
  Widget build(BuildContext context) {
    if (text == null || text!.isEmpty) {
      return const SizedBox.shrink();
    }

    // Generate a unique tag if none provided
    final controllerTag = tag ?? '${text.hashCode}_${DateTime.now().microsecondsSinceEpoch}';

    // Use GetBuilder for better performance instead of creating controller on every build
    return GetBuilder<QuillReadMoreController>(
      tag: controllerTag,
      init: QuillReadMoreController(tag: controllerTag),
      builder: (controller) {
        return FutureBuilder<Document?>(
          future: _parseDocumentAsync(text!),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Container(
                  constraints: BoxConstraints(maxHeight: 80.h),
                  child: const Center(
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                ),
              );
            }

            if (snapshot.hasError || !snapshot.hasData) {
              return _buildFallbackWidget(text!, controller);
            }

            final document = snapshot.data!;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Material(
                    color: Colors.transparent,
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        if (!readMore) {
                          return _buildQuillEditor(document, controller);
                        }

                        final needsReadMore = _needsReadMore(document);

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Obx(() {
                              final double maxHeightValue = needsReadMore && !controller.isExpanded.value
                                  ? 80.h
                                  : 10000.0;

                              return AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                                constraints: BoxConstraints(
                                  maxHeight: maxHeightValue,
                                ),
                                child: _buildQuillEditor(
                                      document,
                                      controller,
                                      maxHeight: controller.isExpanded.value
                                          ? maxHeightValue
                                          : (needsReadMore ? 80.h : maxHeightValue),
                                    ),
                              );
                            }),
                            if (needsReadMore)
                              Obx(
                                () => Container(
                                  alignment: Alignment.centerRight,
                                  child: TextButton(
                                    onPressed: () {
                                      controller.toggleExpanded();
                                    },
                                    style: TextButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12.0,
                                        vertical: 2.0,
                                      ),
                                    ),
                                    child: Text(
                                      controller.isExpanded.value
                                          ? 'Read Less'
                                          : 'Read More',
                                      style: TextStyle(
                                        color: Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Async document parsing to avoid blocking UI
  Future<Document?> _parseDocumentAsync(String text) async {
    try {
      // Check cache first
      final cacheKey = text.hashCode.toString();
      final cachedDoc = _DocumentCacheManager.getDocument(cacheKey);
      if (cachedDoc != null) {
        return cachedDoc;
      }

      // Parse in compute isolate for heavy operations
      return await Future.microtask(() {
        late Document document;

        if (isWysiwygFormat(text)) {
          final htmlToDelta = HtmlToDelta();
          final delta = htmlToDelta.convert(text);
          document = Document.fromDelta(delta);
        } else {
          final operations = _parseContent(text);
          document = Document.fromDelta(Delta.fromJson(operations));
        }

        _DocumentCacheManager.setDocument(cacheKey, document);
        return document;
      });
    } catch (e) {
      return null;
    }
  }

  Widget _buildQuillEditor(
    Document document,
    QuillReadMoreController controller, {
    double? maxHeight,
  }) {
    final scrollController = ScrollController();
    final isScrollable = controller.isExpanded.value;
    
    // Always use a finite constraint value
    final double constraintHeight = maxHeight ?? 10000.0;
    
    return Container(
      constraints: BoxConstraints(
        maxHeight: constraintHeight,
      ),
      decoration: const BoxDecoration(),  // Add empty decoration to satisfy the assertion
      clipBehavior: Clip.antiAlias,
      child: QuillEditor(
        
        config: QuillEditorConfig(
         
          autoFocus: false,
          scrollable: isScrollable,
          showCursor: false,
          padding: EdgeInsets.zero,
          
          enableInteractiveSelection: false,
          enableSelectionToolbar: false,
          customStyles: DefaultStyles(
            h1: DefaultTextBlockStyle(
  TextStyle(
    fontSize: 24.sp,
    fontWeight: FontWeight.bold,
  ),
  const HorizontalSpacing( 16.0,  0.0), // Use HorizontalSpacing
  const VerticalSpacing( 0.0,  0.0),   // Use VerticalSpacing
  const VerticalSpacing( 0.0,  0.0),   // Use VerticalSpacing
  null, // BoxDecoration (optional)
),

            sizeSmall: TextStyle(fontSize: 12.sp),
            subscript: TextStyle(
              fontSize: 10.sp,
              fontWeight: FontWeight.normal,
            ),
          ),
          embedBuilders: [
            QuillEditorImageEmbedBuilder(),
          ],
        ),
        scrollController: scrollController,
        focusNode: FocusNode(canRequestFocus: false), controller:   QuillController(
            document: document,
            selection: const TextSelection.collapsed(offset: 0),
            config: const QuillControllerConfig (),
          )..readOnly = true,
      ),
    );
  }

  List<dynamic> _parseContent(String inputText) {
    if (inputText.isEmpty) {
      return [{"insert": "\n"}];
    }

    String cleanedText = inputText.trim();
    List<dynamic> operations;

    try {
      if (cleanedText.startsWith('[{') && cleanedText.endsWith('}]')) {
        operations = jsonDecode(cleanedText);
      } else {
        dynamic decodedContent = jsonDecode(cleanedText);
        operations = (decodedContent is Map && decodedContent.containsKey('ops'))
            ? decodedContent['ops']
            : decodedContent;
      }
    } catch (_) {
      operations = [
        {"insert": "$cleanedText\n"}
      ];
    }

    if (operations.isEmpty) {
 
      return [{"insert": "\n"}];
    }
 
    final lastOp = operations.last;
    if (lastOp is Map && lastOp.containsKey('insert')) {
      String lastText = lastOp['insert'] as String;
      if (!lastText.endsWith('\n')) {
        lastOp['insert'] = '$lastText\n';
      }
    } else {
      operations.add({"insert": "\n"});
    }

    return operations;
  }

  bool _needsReadMore(Document document) {
    try {
      final plainText = document.toPlainText();
      final lineCount = plainText.split('\n').length;
      final textLength = plainText.characters.length;

      return lineCount > maxLines || textLength > 200;
    } catch (_) {
      return false;
    }
  }

  Widget _buildFallbackWidget(
    String fallbackText,
    QuillReadMoreController controller,
  ) {
    try {
      final delta = Delta.fromJson([
        {"insert": "$fallbackText\n"}
      ]);

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Material(
          color: Colors.transparent,
          child: Obx(
            () => _buildQuillEditor(
              Document.fromDelta(delta),
              controller,
              maxHeight: controller.isExpanded.value ? 10000.0 : 25, // Use finite value instead of null
            ),
          ),
        ),
      );
    } catch (_) {
      return const SizedBox.shrink();
    }
  }
}

class QuillReadMoreController extends GetxController {
  final isExpanded = false.obs;
  final shouldShowReadMore = false.obs;
  final String? tag;

  // Use a static map to track controllers by tag for cleanup
  static final Map<String, QuillReadMoreController> _controllers = {};

  QuillReadMoreController({this.tag});

  @override
  void onInit() {
    super.onInit();
    // Store controller reference by tag for proper cleanup
    if (tag != null) {
      _controllers[tag!] = this;
    }
  }

  @override
  void onClose() {
    // Remove controller reference when closed
    if (tag != null) {
      _controllers.remove(tag!);
    }
    super.onClose();
  }

  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
    update();
  }

  void updateShouldShowReadMore(bool value) => shouldShowReadMore.value = value;

  // Static method to cleanup all controllers
  static void cleanupAllControllers() {
    final controllersToRemove = <String>[];
    for (final entry in _controllers.entries) {
      if (entry.value.isClosed) {
        controllersToRemove.add(entry.key);
      }
    }
    for (final key in controllersToRemove) {
      _controllers.remove(key);
    }
  }
}

class QuillEditorImageEmbedBuilder extends EmbedBuilder {
  @override
  String get key => 'image';

  @override
  Widget build(BuildContext context, EmbedContext embedContext) {
    final imageUrl = embedContext.node.value.data as String?;

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 300),
        child: imageUrl == null
            ? const Icon(Icons.broken_image)
            : Image.network(
                imageUrl,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) =>
                    const Icon(Icons.error),
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  );
                },
              ),
      ),
    );
  }
}

// Optimized document cache with proper cleanup
class _DocumentCacheManager {
  static final Map<String, Document> _cache = <String, Document>{};
  static const int _maxCacheSize = 30; // Reduced cache size for better memory management

  static Document? getDocument(String key) {
    return _cache[key];
  }

  static void setDocument(String key, Document document) {
    if (_cache.length >= _maxCacheSize) {
      // Remove oldest entries (simple FIFO)
      final keysToRemove = _cache.keys.take(_cache.length - _maxCacheSize + 1).toList();
      for (final keyToRemove in keysToRemove) {
        _cache.remove(keyToRemove);
      }
    }
    _cache[key] = document;
  }


}

class QuillEditorShortWidget extends StatefulWidget {
  final String? text;
  final String tag;
  final int maxLines;

  const QuillEditorShortWidget({
    super.key,
    this.text,
    this.maxLines = 3,
    required this.tag,
  });

  @override
  State<QuillEditorShortWidget> createState() => _QuillEditorShortWidgetState();
}

class _QuillEditorShortWidgetState extends State<QuillEditorShortWidget> {
  Document? _cachedDocument;
  QuillController? _controller;
  late final ScrollController _scrollController;
  late final FocusNode _focusNode;
  String? _currentTextHash;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _focusNode = FocusNode(canRequestFocus: false);
    _parseDocument();
  }

  @override
  void didUpdateWidget(QuillEditorShortWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only reparse if text actually changed or tag changed
    if (oldWidget.text != widget.text || oldWidget.tag != widget.tag) {
      _parseDocument();
    }
  }

  void _parseDocument() {
    if (_isDisposed) return;

    if (widget.text == null || widget.text!.isEmpty) {
      _clearDocument();
      return;
    }

    // Create a unique cache key combining text content and tag
    final textHash = widget.text!.hashCode.toString();
    final cacheKey = '${widget.tag}_$textHash';

    // Skip parsing if we already have this exact content
    if (_currentTextHash == textHash && _cachedDocument != null) {
      return;
    }

    _currentTextHash = textHash;

    // Check cache first
    final cachedDoc = _DocumentCacheManager.getDocument(cacheKey);
    if (cachedDoc != null) {
      _cachedDocument = cachedDoc;
      _createController();
      return;
    }

    // Parse document in a microtask to avoid blocking UI
    Future.microtask(() {
      if (_isDisposed) return;

      try {
        late Document document;

        if (isWysiwygFormat(widget.text!)) {
          final htmlToDelta = HtmlToDelta();
          final delta = htmlToDelta.convert(widget.text!);
          document = Document.fromDelta(delta);
        } else {
          final operations = _parseContent(widget.text!);
          document = Document.fromDelta(Delta.fromJson(operations));
        }

        if (!_isDisposed) {
          _DocumentCacheManager.setDocument(cacheKey, document);
          setState(() {
            _cachedDocument = document;
            _createController();
          });
        }
      } catch (e) {
        if (!_isDisposed) {
          setState(() {
            _clearDocument();
          });
        }
      }
    });
  }

  void _clearDocument() {
    _cachedDocument = null;
    _controller?.dispose();
    _controller = null;
    _currentTextHash = null;
  }

  void _createController() {
    if (_isDisposed || _cachedDocument == null) return;

    _controller?.dispose();
    _controller = QuillController(
      document: _cachedDocument!,
      selection: const TextSelection.collapsed(offset: 0),
      config: const QuillControllerConfig(),
    )..readOnly = true;
  }

  @override
  void dispose() {
    _isDisposed = true;
    _controller?.dispose();
    _scrollController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isDisposed || widget.text == null || widget.text!.isEmpty) {
      return const SizedBox.shrink();
    }

    // Show loading indicator while parsing
    if (_cachedDocument == null || _controller == null) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: Container(
          constraints: BoxConstraints(maxHeight: 80.h),
          child: const Center(
            child: SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Material(
        color: Colors.transparent,
        child: Hero(
          tag: widget.tag,
          child: Container(
            constraints: BoxConstraints(
              maxHeight: 80.h,
            ),
            child: QuillEditor(
              config: QuillEditorConfig(
                autoFocus: false,
                scrollable: false,
                showCursor: false,
                padding: EdgeInsets.zero,
                enableInteractiveSelection: false,
                enableSelectionToolbar: false,
                customStyles: DefaultStyles(
                  h1: DefaultTextBlockStyle(
                    TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    const HorizontalSpacing(16.0, 0.0),
                    const VerticalSpacing(0.0, 0.0),
                    const VerticalSpacing(0.0, 0.0),
                    null,
                  ),
                  sizeSmall: TextStyle(fontSize: 12.sp),
                  subscript: TextStyle(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                embedBuilders: [
                  QuillEditorImageEmbedBuilder(),
                ],
              ),
              scrollController: _scrollController,
              focusNode: _focusNode,
              controller: _controller!,
            ),
          ),
        ),
      ),
    );
  }

  List<dynamic> _parseContent(String inputText) {
    if (inputText.isEmpty) {
      return [{"insert": "\n"}];
    }

    String cleanedText = inputText.trim();
    List<dynamic> operations;

    try {
      if (cleanedText.startsWith('[{') && cleanedText.endsWith('}]')) {
        operations = jsonDecode(cleanedText);
      } else {
        dynamic decodedContent = jsonDecode(cleanedText);
        operations = (decodedContent is Map && decodedContent.containsKey('ops'))
            ? decodedContent['ops']
            : decodedContent;
      }
    } catch (_) {
      operations = [
        {"insert": "$cleanedText\n"}
      ];
    }

    if (operations.isEmpty) {
      return [{"insert": "\n"}];
    }

    final lastOp = operations.last;
    if (lastOp is Map && lastOp.containsKey('insert')) {
      String lastText = lastOp['insert'] as String;
      if (!lastText.endsWith('\n')) {
        lastOp['insert'] = '$lastText\n';
      }
    } else {
      operations.add({"insert": "\n"});
    }

    return operations;
  }
}