import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/configs/country_specifics.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import 'transaction_excel_generator.dart';
import 'package:onekitty/services/share_whatsapp_service.dart';
import 'package:onekitty/services/whatsapp_statement_export_service.dart';
import 'package:open_file/open_file.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/view_single_event.dart';
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/see_all_transactions_screen/widgets/excel/excel_func.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/see_all_transactions_screen/widgets/statement_widget.dart';

import '../../controllers/transaction_controller.dart';
import '../../models/transaction_type.dart';
import 'transaction_pdf_generator.dart';

class TransactionExportWidget extends StatefulWidget {
  final TransactionType transactionType;
  final String controllerTag;
  final String? entityTitle;
  final int? entityId;
  final int? kittyId;
  final TransactionModel? singleTransaction;
  final List<TransactionModel>? transactions;
  final Kitty? kitty;
  final bool? singleTrans;

  const TransactionExportWidget({
    super.key,
    required this.transactionType,
    required this.controllerTag,
    this.entityTitle,
    this.entityId,
    this.singleTransaction,
    this.kitty,
    this.singleTrans,
    this.transactions,  this.kittyId,
  });

  @override
  State<TransactionExportWidget> createState() =>
      _TransactionExportWidgetState();
}

class _TransactionExportWidgetState extends State<TransactionExportWidget> {
  UserKittyController? userController;
  KittyController? kittyController;
  DataController? dataController;
  String shareMsg = "";

  bool get isSingleTransaction =>
      widget.singleTrans == true || widget.singleTransaction != null;
  bool get isEvent =>
      widget.entityId != null &&
      widget.transactionType == TransactionType.event;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    if (!isEvent && !isSingleTransaction) {
      _fetchMessage();
    }
  }

  void _initializeControllers() {
    try {
      Get.isRegistered<TransactionController>()
          ? null
          : Get.put(TransactionController());
      Get.isRegistered<WhatsAppStatementExportService>()
          ? null
          : Get.put(WhatsAppStatementExportService());

      if (Get.isRegistered<UserKittyController>()) {
        userController = Get.find<UserKittyController>();
      }
      if (Get.isRegistered<KittyController>()) {
        kittyController = Get.find<KittyController>();
      }
      if (Get.isRegistered<DataController>()) {
        dataController = Get.find<DataController>();
      }
    } catch (e) {
      // Controllers not available
    }
  }

  Future<void> _fetchMessage() async {
    if (kittyController != null && dataController != null) {
      final res = await kittyController!
          .shareKittyTrans(id: (dataController!.kitty.value.kitty?.iD ?? 0));
      if (res && mounted) {
        setState(() {
          shareMsg = kittyController!.textmessage.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Get.height * 0.8,
      width: double.maxFinite,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.symmetric(vertical: 12.h),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(24.w, 8.h, 16.w, 16.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "export_options".tr,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        _getSubtitle(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    borderRadius: BorderRadius.circular(20.r),
                    child: Container(
                      padding: EdgeInsets.all(8.w),
                      child: Icon(
                        Icons.close,
                        size: 20.w,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 32.h),
                child: Column(
                  children: [
                    // Generate Statement (only for multiple transactions)
                    if (!isSingleTransaction) ...[
                      _buildExportOption(
                        context: context,
                        icon: Icons.receipt_long_outlined,
                        title: "generate_statement".tr,
                        subtitle: "generate_detailed_statement".tr,
                        onTap: () => _generateStatement(context),
                      ),
                      SizedBox(height: 16.h),
                    ],
                    _buildExportOption(
                      context: context,
                      icon: Icons.chat_outlined,
                      title: "whatsapp_message".tr,
                      subtitle: "share_directly_via_whatsapp".tr,
                      onTap: () => _shareWhatsApp(context),
                    ),
                    SizedBox(height: 16.h),

                    // Export to PDF
                    _buildExportOption(
                      context: context,
                      icon: Icons.picture_as_pdf_outlined,
                      title: "export_to_pdf".tr,
                      subtitle: "download_as_pdf_document".tr,
                      onTap: () => _exportToPdf(context),
                    ),

                    // Export to Excel (only for multiple transactions)
                    if (!isSingleTransaction) ...[
                      SizedBox(height: 16.h),
                      _buildExportOption(
                        context: context,
                        icon: Icons.table_chart_outlined,
                        title: "export_to_excel".tr,
                        subtitle: "download_as_spreadsheet_file".tr,
                        onTap: () => _exportToExcel(context),
                      ),
                    ],

                    SizedBox(height: 16.h),

                    _buildExportOption(
                      context: context,
                      icon: Icons.text_snippet_outlined,
                      title: "export_to_text".tr,
                      subtitle: "share_as_plain_text_message".tr,
                      onTap: () => _exportToText(context),
                    ),

                    SizedBox(height: 16.h),

                    

                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExportOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  size: 20.w,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AutoSizeText(
                      title,
                      maxLines: 1,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.6),
                          ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.w,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _exportToPdf(BuildContext context) {
    if (isSingleTransaction) {
      if (isEvent) {
        Get.to(() => SingleStatementPage(
              eventId: widget.entityId,
              isContributions: true,
              kitty: widget.kitty,
              kittyTransaction: widget.singleTransaction,
            ));
      } else {
        Get.to(() => SingleStatementPage(
              isContributions: false,
              userTransactions: widget.singleTransaction,
            ));
      }
    } else {
      if (isEvent) {
        Get.to(() => StatementPage(
              eventId: widget.entityId,
              isContributions: true,
              kitty: widget.kitty,
              transactions: kittyController?.transactionsKitty ?? [],
            ));
      } else if (widget.transactionType == TransactionType.user) {
        Get.to(() => UserStatementPage(
              isContributions: true,
              userTransactions: userController?.alltransactions ?? [],
            ));
      } else {
        final controller =
            Get.find<TransactionController>(tag: widget.controllerTag);
        Get.to(() => Scaffold(
              appBar: AppBar(title: const Text('PDF Statement')),
              body: PdfPreview(
                shareActionExtraSubject:
                    "${widget.entityTitle ?? widget.transactionType.displayName} transactions statement",
                pdfFileName:
                    "${(widget.entityTitle ?? widget.transactionType.displayName).replaceAll(' ', '_')}_transactions_statement.pdf",
                build: (context) => generateTransactionsPdf(
                  widget.transactions ?? controller.transactions,
                  widget.transactionType,
                  entityTitle: widget.entityTitle,
                ),
              ),
            ));
      }
    }
  }

  void _exportToText(BuildContext context) async {
    if (isSingleTransaction) {
      if (isEvent) {
        final event = Get.find<ViewSingleEventController>().event.value;
        final DateFormat format = DateFormat.MMMEd().add_jms();
        DateTime createdAt =
            widget.singleTransaction?.createdAt ?? DateTime.now();
        String shareMsg =
            _buildSingleTransactionMessage(event, format, createdAt);
        await Share.share(shareMsg, subject: 'transaction_details'.tr);
      } else {
        final DateFormat format = DateFormat.MMMEd().add_jms();
        DateTime createdAt =
            widget.singleTransaction?.createdAt?.toLocal() ?? DateTime.now();
        String shareMsg = _buildTransactionMessage(format, createdAt);
        await Share.share(shareMsg, subject: 'transaction_details'.tr);
      }
    } else {
      if (shareMsg.isNotEmpty) {
        await Share.share(shareMsg);
      } else {
        final controller =
            Get.find<TransactionController>(tag: widget.controllerTag);
        final transactions = controller.transactions;

        if (transactions.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('no_transactions_to_export'.tr)),
          );
          return;
        }

        final title = widget.entityTitle ?? widget.transactionType.displayName;
        final text = await _buildTextReport(transactions, title);
        await Share.share(text, subject: '$title ${'transaction_report'.tr}');
      }
    }
    Navigator.pop(context);
  }

  void _generateStatement(
    BuildContext context,
  ) async {
    print('${widget}');
    final whatsappService = Get.put(WhatsAppStatementExportService());

    if (isEvent) {
      final kittyId = dataController?.kitty.value.kitty?.iD;
      final kittyTitle = dataController?.kitty.value.kitty?.title;

      if (kittyId != null) {
        await whatsappService.showWhatsAppStatementDialog(
          context: context,
          kittyId: kittyId,
          kittyTitle: kittyTitle,
        );
      }
    }  else if(widget.transactionType == TransactionType.chama){
        // Ensure we have a valid kittyId for chama transactions
        final kittyId = widget.kittyId ?? widget.entityId;
        if (kittyId == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('unable_to_generate_statement'.tr),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
        
        await whatsappService.showWhatsAppChamaStatementDialog(
            context: context, 
            kittyId: kittyId,
            chamaTitle:
                widget.entityTitle ?? widget.transactionType.displayName,
          );
    }
    
    else {
      try {

        final kittyId = widget.entityId ??
            widget.singleTransaction?.kittyId ??
            userController?.alltransactions.first.kittyId;
        final kittyTitle = widget.entityTitle ??
            widget.singleTransaction?.kittyTitle ??
            userController?.alltransactions.first.kittyTitle;

        if (kittyId != null) {
          await whatsappService.showWhatsAppStatementDialog(
            context: context,
            kittyId: kittyId,
            kittyTitle: kittyTitle,
          );
        } else {
          await whatsappService.showWhatsAppChamaStatementDialog(
            context: context,
            kittyId: widget.entityId,
            chamaTitle:
                widget.entityTitle ?? widget.transactionType.displayName,
          );
        }
      } catch (e) {
        await whatsappService.showWhatsAppChamaStatementDialog(
          context: context,
          kittyId: widget.entityId,
          chamaTitle: widget.entityTitle ?? widget.transactionType.displayName,
        );
      }
    }
  }

  void _exportToExcel(BuildContext context) async {
    _showLoadingDialog(context, 'generating_excel_file'.tr);

    try {
      String filePath;
      if (isEvent) {
        filePath = await createExcel(isKitty: true, eventId: widget.entityId);
      } else if (widget.transactionType == TransactionType.user) {
        filePath = await userExcel();
      } else {
        filePath = await createTransactionExcel(
          transactionType: widget.transactionType,
          controllerTag: widget.controllerTag,
          entityTitle: widget.entityTitle,
        );
      }
      Navigator.pop(context);

      _showFileOptionsBottomSheet(context, filePath);
    } catch (e) {
      Navigator.pop(context);
      _showErrorDialog(context, '${'failed_to_generate_excel_file'.tr}: $e');
    }
  }

  void _shareWhatsApp(BuildContext context) async {
    Navigator.pop(context);

    if (isSingleTransaction) {
      if (isEvent) {
        final event = Get.find<ViewSingleEventController>().event.value;
        final DateFormat format = DateFormat.MMMEd().add_jms();
        DateTime createdAt =
            widget.singleTransaction?.createdAt ?? DateTime.now();
        String shareMsg =
            _buildSingleTransactionWhatsAppMessage(event, format, createdAt);
        ShareWhatsapp.share(shareMsg);
      } else {
        final DateFormat format = DateFormat.MMMEd().add_jms();
        DateTime createdAt =
            widget.singleTransaction?.createdAt?.toLocal() ?? DateTime.now();
        String shareMsg = _buildTransactionMessage(format, createdAt);
        ShareWhatsapp.share(shareMsg);
      }
    } else {
      if (shareMsg.isNotEmpty) {
        ShareWhatsapp.share(shareMsg);
      } else {
        final controller =
            Get.find<TransactionController>(tag: widget.controllerTag);
        final transactions = controller.transactions;

        if (transactions.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('no_transactions_to_share'.tr)),
          );
          return;
        }

        final title = widget.entityTitle ?? widget.transactionType.displayName;
        final text = await _buildTextReport(transactions, title);
        ShareWhatsapp.share(text);
      }
    }
  }

  void _showFileOptionsBottomSheet(BuildContext context, String filePath) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40.w,
                height: 4.h,
                margin: EdgeInsets.only(bottom: 20.h),
                decoration: BoxDecoration(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              Text(
                "file_options".tr,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              SizedBox(height: 24.h),
              _buildFileOption(
                context: context,
                icon: Icons.file_open,
                title: "open_file".tr,
                subtitle: "open_with_default_app".tr,
                onTap: () async {
                  try {
                    final result = await OpenFile.open(filePath);
                    if (result.type != ResultType.done) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content:
                              Text('Error opening file: ${result.message}'),
                          backgroundColor: Theme.of(context).colorScheme.error,
                        ),
                      );
                    }
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error opening file: $e'),
                        backgroundColor: Theme.of(context).colorScheme.error,
                      ),
                    );
                  }
                  Navigator.pop(context);
                },
              ),
              SizedBox(height: 12.h),
              _buildFileOption(
                context: context,
                icon: Icons.share,
                title: "share_file".tr,
                subtitle: "share_with_other_apps".tr,
                onTap: () {
                  Navigator.pop(context);
                  Share.shareXFiles([XFile(filePath)]);
                },
              ),
              SizedBox(height: 16.h),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFileOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  size: 20.w,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    if (subtitle != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.6),
                            ),
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.w,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<String> _buildTextReport(
      List<TransactionModel> transactions, String title) async {
    final buffer = StringBuffer();
    buffer.writeln('$title ${'transaction_report'.tr}');
    buffer.writeln('${'generated'.tr}: ${DateTime.now().toString().substring(0, 19)}');
    buffer.writeln('=' * 50);
    buffer.writeln();

    for (final transaction in transactions) {
      buffer.writeln(
          '${'date'.tr}: ${transaction.createdAt?.toString().substring(0, 19) ?? 'n_a'.tr}');
      buffer.writeln(
          '${'reference'.tr}: ${transaction.transactionCode ?? transaction.transactionRef ?? 'n_a'.tr}');
      buffer.writeln(
          '${'amount_label'.tr}: ${FormattedCurrency.getFormattedCurrency(transaction.amount)}');
      if (transaction.firstName != null || transaction.secondName != null) {
        buffer.writeln(
            '${'name'.tr}: ${transaction.firstName ?? ''} ${transaction.secondName ?? ''}');
      }
      buffer.writeln('-' * 30);
    }

    final totalAmount =
        transactions.fold(0.0, (sum, t) => sum + (t.amount ?? 0));
    buffer.writeln();
    buffer.writeln(
        '${'total_amount'.tr}: ${FormattedCurrency.getFormattedCurrency(totalAmount)}');
    buffer.writeln('${'total_transactions'.tr}: ${transactions.length}');

    // Add event link if this is an event transaction
    final eventLink = await _getEventLink(transactions);
    if (eventLink != null) {
      buffer.writeln();
      buffer.writeln('${'event_link'.tr}: $eventLink');
    }

    return buffer.toString();
  }

  Future<String?> _getEventLink(List<TransactionModel> transactions) async {
    // Check if any transaction has an eventId
    final eventTransaction = transactions.firstWhere(
      (t) => t.eventId != null && t.eventId! > 0,
      orElse: () => transactions.first,
    );

    if (eventTransaction.eventId == null || eventTransaction.eventId! <= 0) {
      return null;
    }

    try {
      // Get event details to access username
      if (Get.isRegistered<Eventcontroller>()) {
        final eventsController = Get.find<Eventcontroller>();
        final event = eventsController.events.firstWhere(
          (e) => e.id == eventTransaction.eventId,
          orElse: () => throw Exception('Event not found'),
        );

        if (event.username.isNotEmpty) {
          return 'https://www.onekitty.co.ke/events/${event.username}';
        }
      }
    } catch (e) {
      // If we can't find the event, return null
      return null;
    }

    return null;
  }

  String _getSubtitle() {
    if (isEvent) {
      final event = Get.find<ViewSingleEventController>().event.value;
      return "Event: ${event.title}";
    } else if (dataController != null) {
      return "Kitty: ${dataController!.kitty.value.kitty?.title ?? ''}";
    }
    return widget.entityTitle ?? widget.transactionType.displayName;
  }

  // Message builders for single transactions
  String _buildTransactionMessage(DateFormat format, DateTime createdAt) {
    final transaction =
        widget.singleTransaction ?? userController?.alltransactions.first;
    if (transaction == null) return "";

    return "${'phone_number'.tr}: ${transaction.phoneNumber ?? "anonymous".tr}\n"
        "${'amount_label'.tr}: ${CountryConfig.getCurrencyCode} ${transaction.amount}\n"
        "${'transaction_code'.tr}: ${transaction.transactionCode}\n"
        "${'status_label'.tr}: ${transaction.status}\n"
        "${'date'.tr}: ${format.format(createdAt.toLocal())}";
  }

  String _buildSingleTransactionMessage(
      dynamic event, DateFormat format, DateTime createdAt) {
    return "Title: ${event?.title ?? dataController?.kitty.value.kitty?.title ?? ''}\n"
        "Phone Number: ${widget.singleTransaction?.phoneNumber}\n"
        "Amount: ${CountryConfig.getCurrencyCode} ${widget.singleTransaction?.amount}\n"
        "Name: ${widget.singleTransaction?.firstName ?? ""} ${widget.singleTransaction?.secondName ?? ""}\n"
        "Transaction Code: ${widget.singleTransaction?.transactionCode ?? ''}\n"
        "Date: ${format.format(createdAt.toLocal())}\n"
        "${event != null ? 'Event: https://onekitty.co.ke/event/${event.username}' : 'Kitty: https://onekitty.co.ke/kitty/${dataController?.kitty.value.kitty?.iD}'}";
  }

  String _buildSingleTransactionWhatsAppMessage(
      dynamic event, DateFormat format, DateTime createdAt) {
    return "${event != null ? 'Event' : 'Kitty Title'}: ${event?.title ?? dataController?.kitty.value.kitty?.title ?? ''}\n"
        "Phone Number: ${widget.singleTransaction?.phoneNumber}\n"
        "Amount: ${CountryConfig.getCurrencyCode} ${widget.singleTransaction?.amount}\n"
        "Name: ${widget.singleTransaction?.firstName ?? ""} ${widget.singleTransaction?.secondName ?? ""}\n"
        "Transaction Code: ${widget.singleTransaction?.transactionCode ?? ''}\n"
        "Date: ${format.format(createdAt.toLocal())}\n"
        "${event != null ? 'Event: https://onekitty.co.ke/events/${event.username}' : 'Kitty: https://onekitty.co.ke/kitty/${dataController?.kitty.value.kitty?.iD}'} ";
  }

 // Dialog helpers
  void _showLoadingDialog(BuildContext context, String message) {
    Get.dialog(Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 32.w,
              height: 32.w,
              child: CircularProgressIndicator(
                strokeWidth: 3.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    ));
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          title: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.error,
                size: 24.w,
              ),
              SizedBox(width: 8.w),
              Text(
                'error'.tr,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
              ),
            ],
          ),
          content: Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
              ),
              child: Text('okay'.tr),
            ),
          ],
        );
      },
    );
  }
}
