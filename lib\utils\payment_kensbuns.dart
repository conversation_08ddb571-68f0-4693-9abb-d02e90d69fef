import 'dart:async';
import 'package:flutter/material.dart';

class PaymentKensbuns extends StatefulWidget {
  const PaymentKensbuns({super.key});

  @override
  State<PaymentKensbuns> createState() => _PaymentKensburnsState();
}

class _PaymentKensburnsState extends State<PaymentKensbuns>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Timer _timer;
  int _currentIndex = 0;

  final List<String> _images = [
    'assets/images/icons/google-pay.png',
    'assets/images/icons/money.png',
    'assets/images/icons/visa.png',
    'assets/images/icons/apple-pay.png',
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
    
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      setState(() {
        _currentIndex = (_currentIndex + 1) % _images.length;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 45,
      width: 45,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: 1.0 + (_controller.value * 0.2),
            child: Image.asset(
              _images[_currentIndex],
              fit: BoxFit.contain,
            ),
          );
        },
      ),
    );
  }
}