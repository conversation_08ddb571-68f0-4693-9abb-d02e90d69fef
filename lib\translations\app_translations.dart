import 'package:get/get.dart';
import './english/index.dart' show english;
import './french/index.dart' show french;
import './german/index.dart' show german;
import './spanish/index.dart' show spanish;
import './kiswahili/index.dart' show Swahili;

class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    "fr": french,
    "en": english,
    "sw": Swahili,
    "es": spanish,
    "de": german,
  };
}
