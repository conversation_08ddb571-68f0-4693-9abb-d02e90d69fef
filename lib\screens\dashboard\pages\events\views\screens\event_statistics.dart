import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/event_statistics_controller.dart';

import 'package:onekitty/utils/formatted_currency.dart';

class EventStatistics extends StatefulWidget {
  final int? eventId;
  const EventStatistics({super.key, this.eventId});

  @override
  State<EventStatistics> createState() => _EventStatisticsState();
}

class _EventStatisticsState extends State<EventStatistics>
    with TickerProviderStateMixin {
  final EventStatisticsController controller = Get.find();
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    // controller = Get.put(EventStatisticsController());

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _loadStatistics();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    _fadeController.reset();
    _slideController.reset();

    await controller.fetchEventStatistics(eventId: widget.eventId);

    if (!controller.hasError.value) {
      _fadeController.forward();
      await Future.delayed(const Duration(milliseconds: 200));
      _slideController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(theme, isDark),
      body: Obx(() {
        if (controller.isLoadingStatistics.value) {
          return _buildLoadingState(theme);
        }

        if (controller.hasError.value) {
          return _buildErrorState(theme);
        }

        // Show content if we have any data to display, even if some sections failed
        if (controller.hasAnyDataToShow) {
          return _buildStatisticsContent(theme, isDark);
        }

        // Show no data state if no sections have data
        return _buildNoDataState(theme);
      }),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme, bool isDark) {
    return AppBar(
      title: Text(
        'event_statistics'.tr,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        ),
      ),
      backgroundColor: theme.scaffoldBackgroundColor,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      iconTheme: IconThemeData(color: theme.colorScheme.onSurface),
      actions: [
        AnimatedBuilder(
          animation: _fadeController,
          builder: (context, child) {
            return AnimatedScale(
              scale: _fadeController.value,
              duration: const Duration(milliseconds: 200),
              child: IconButton(
                onPressed: _loadStatistics,
                icon: const Icon(Icons.refresh_rounded),
                tooltip: 'refresh_statistics'.tr,
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                  foregroundColor: theme.colorScheme.primary,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 48,
            height: 48,
            child: CircularProgressIndicator(
              valueColor:
                  AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'loading_statistics'.tr,
            style: TextStyle(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 48,
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'unable_load_statistics'.tr,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              controller.errorMessage.value,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 32),
            FilledButton.icon(
              onPressed: _loadStatistics,
              icon: const Icon(Icons.refresh_rounded),
              label: Text('try_again'.tr),
              style: FilledButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurface.withOpacity(0.05),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.bar_chart_outlined,
                size: 48,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'no_statistics_available'.tr,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'no_statistics_message'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 32),
            FilledButton.icon(
              onPressed: _loadStatistics,
              icon: const Icon(Icons.refresh_rounded),
              label: Text('refresh'.tr),
              style: FilledButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsContent(ThemeData theme, bool isDark) {
    return RefreshIndicator(
      onRefresh: _loadStatistics,
      color: theme.colorScheme.primary,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Only show sections that have data and no parsing errors
                if (controller.shouldShowOverviewSection) ...[
                  _buildOverviewSection(theme),
                  const SizedBox(height: 20),
                ],
                
                if (controller.shouldShowRevenueSection) ...[
                  _buildRevenueSection(theme),
                  const SizedBox(height: 20),
                ],
                
                if (controller.shouldShowUserDemographicsSection) ...[
                  _buildUserDemographicsSection(theme),
                  const SizedBox(height: 20),
                ],

                if (kDebugMode) ...[
                  _buildInteractiveSections(theme),
                  const SizedBox(height: 20),
                ],
                
                const SizedBox(height: 100), // Bottom padding for better UX
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewSection(ThemeData theme) {
    return _buildAnimatedCard(
      theme: theme,
      delay: 0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            theme: theme,
            title: 'Overview',
            icon: Icons.dashboard_rounded,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 20),
          if (controller.hasOverviewData)
            _buildOverviewGrid(theme)
          else
            _buildNoDataMessage(theme, 'No overview data available'),
        ],
      ),
    );
  }

  Widget _buildOverviewGrid(ThemeData theme) {
    final metrics = [
      _MetricData(
        title: 'Total Events',
        value: controller.totalEvents.toString(),
        icon: Icons.event_rounded,
        color: theme.colorScheme.primary,
      ),
      _MetricData(
        title: 'Total Revenue',
        value: FormattedCurrency.getFormattedCurrency(controller.totalRevenue),
        icon: Icons.attach_money_rounded,
        color: Colors.green,
      ),
      _MetricData(
        title: 'Tickets Sold',
        value: controller.totalTicketsSold.toString(),
        icon: Icons.confirmation_number_rounded,
        color: Colors.blue,
      ),
      _MetricData(
        title: 'Total Attendees',
        value: controller.totalAttendees.toString(),
        icon: Icons.people_rounded,
        color: Colors.orange,
      ),
    ];

    return 
    Wrap(
      spacing: 8,
      runSpacing: 8, 
      alignment: WrapAlignment.center,
      children: metrics.map((e){
    return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 300),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.scale(
              scale: value,
              child: _buildMetricCard(theme, e),
            );
          },
        );}).toList()
        );
  }

  Widget _buildMetricCard(ThemeData theme, _MetricData data) {
    return GestureDetector(
      onTap: () {
        // Add haptic feedback
        HapticFeedback.lightImpact();
        // Could trigger detailed view or animation
      },
      child: AnimatedContainer(
        height: 150,
        width: 150,
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: data.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                data.icon,
                size: 24,
                color: data.color,
              ),
            ),
            const SizedBox(height: 12),
            FittedBox(
              child: Text(
                data.value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              data.title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueSection(ThemeData theme) {
    return _buildAnimatedCard(
      theme: theme,
      delay: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            theme: theme,
            title: 'Revenue Breakdown',
            icon: Icons.trending_up_rounded,
            color: Colors.green,
          ),
          const SizedBox(height: 20),
          if (controller.hasRevenueData)
            _buildRevenueContent(theme)
          else
            _buildNoDataMessage(theme, 'No revenue data available'),
        ],
      ),
    );
  }

  Widget _buildRevenueContent(ThemeData theme) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoTile(
                theme: theme,
                title: 'Total Revenue',
                value: FormattedCurrency.getFormattedCurrency(
                    controller.totalRevenue),
                icon: Icons.monetization_on_rounded,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoTile(
                theme: theme,
                title: 'Daily Average',
                value: FormattedCurrency.getFormattedCurrency(
                    controller.averageDailyRevenue),
                icon: Icons.calendar_today_rounded,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildInfoTile(
          theme: theme,
          title: 'Total Transactions',
          value: controller.totalTransactions.toString(),
          icon: Icons.receipt_rounded,
        ),
      ],
    );
  }

  Widget _buildUserDemographicsSection(ThemeData theme) {
    return _buildAnimatedCard(
      theme: theme,
      delay: 400,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            theme: theme,
            title: 'User Demographics',
            icon: Icons.people_outline_rounded,
            color: Colors.purple,
          ),
          const SizedBox(height: 20),
          if (controller.hasUserDemographicsData)
            _buildUserDemographicsContent(theme)
          else
            _buildNoDataMessage(theme, 'No user demographics data available'),
        ],
      ),
    );
  }

  Widget _buildUserDemographicsContent(ThemeData theme) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoTile(
                theme: theme,
                title: 'Unique Customers',
                value: controller.uniqueCustomers.toString(),
                icon: Icons.person_rounded,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoTile(
                theme: theme,
                title: 'Repeat Customers',
                value: controller.repeatCustomers.toString(),
                icon: Icons.repeat_rounded,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildInfoTile(
                theme: theme,
                title: 'Repeat Rate',
                value: '${controller.repeatCustomerRate.toStringAsFixed(1)}%',
                icon: Icons.trending_up_rounded,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoTile(
                theme: theme,
                title: 'Avg Tickets/User',
                value: controller.averageTicketsPerUser.toStringAsFixed(1),
                icon: Icons.confirmation_number_rounded,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInteractiveSections(ThemeData theme) {
    final sections = [
      _SectionData('Event Performance', Icons.analytics_rounded),
      _SectionData('Payment Methods', Icons.payment_rounded),
      _SectionData('Popular Tickets', Icons.star_rounded),
      _SectionData('Sales Timeline', Icons.timeline_rounded),
    ];

    return Column(
      children: sections.asMap().entries.map((entry) {
        final index = entry.key;
        final section = entry.value;

        return Padding(
          padding:
              EdgeInsets.only(bottom: index < sections.length - 1 ? 16 : 0),
          child: _buildInteractiveCard(
            theme: theme,
            title: section.title,
            icon: section.icon,
            delay: 600 + (index * 100),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildInteractiveCard({
    required ThemeData theme,
    required String title,
    required IconData icon,
    int delay = 0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                _showComingSoonDialog(theme, title);
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.1),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: theme.shadowColor.withOpacity(0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        icon,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      size: 16,
                      color: theme.colorScheme.onSurface.withOpacity(0.4),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedCard({
    required ThemeData theme,
    required Widget child,
    int delay = 0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, _) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.shadowColor.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(20),
              child: child,
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader({
    required ThemeData theme,
    required String title,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoTile({
    required ThemeData theme,
    required String title,
    required String value,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          FittedBox(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataMessage(ThemeData theme, String message) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.05),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              Icons.info_outline_rounded,
              size: 32,
              color: theme.colorScheme.onSurface.withOpacity(0.4),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showComingSoonDialog(ThemeData theme, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: theme.colorScheme.surface,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'Coming Soon',
          style: TextStyle(
            color: theme.colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          '$feature will be available in a future update.',
          style: TextStyle(
            color: theme.colorScheme.onSurface.withOpacity(0.8),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Got it',
              style: TextStyle(color: theme.colorScheme.primary),
            ),
          ),
        ],
      ),
    );
  }
}

class _MetricData {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  _MetricData({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });
}

class _SectionData {
  final String title;
  final IconData icon;

  _SectionData(this.title, this.icon);
}
