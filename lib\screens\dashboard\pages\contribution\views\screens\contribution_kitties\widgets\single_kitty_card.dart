import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:onekitty/translations/utils/time_range_translatable.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/formatted_currency.dart';

class ContributionKittyWidget extends StatelessWidget {
  final UserKitty kitty;

  const ContributionKittyWidget({
    super.key,
    required this.kitty,
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: () => _viewKittyDetails(context),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isLight.value
                  ? [Colors.white, Colors.grey.shade50]
                  : [Colors.grey.shade900, Colors.grey.shade900],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(7),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(kitty.kittyType?.toLowerCase().tr ?? "",
                  style: CustomTextStyles.titleSmallyellow),
              _buildHeader(),
              if (kitty.percentage != null) _buildProgressIndicator(),
              _buildCategoriesDisplay(),
              SizedBox(height: 16.h),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            kitty.kitty?.title ?? "unnamed_kitty".tr,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w800,
              color: isLight.value ? Colors.grey.shade800 : Colors.white,
              letterSpacing: -0.5,
            ),
          ),
        ),
        _buildBalanceChip(),
      ],
    );
  }

  Widget _buildBalanceChip() {
    return Text.rich(
        textAlign: TextAlign.end,
        TextSpan(children: [
          TextSpan(
              text: FormattedCurrency.getFormattedCurrency(
                  double.tryParse(kitty.kitty?.balance?.toString() ?? '') ?? 0),
              style: const TextStyle(
                  color: AppColors.primary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600)),
        ]));
    // ignore: dead_code
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: isLight.value
            ? Colors.green.shade50
            : Colors.green.shade900.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        FormattedCurrency.getFormattedCurrency(
            double.tryParse(kitty.kitty?.balance?.toString() ?? '') ?? 0),
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w700,
          color: isLight.value ? Colors.green.shade700 : Colors.greenAccent,
        ),
      ),
    );
  }

  // ignore: unused_element
  Widget _buildProgressIndicator() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: LinearProgressIndicator(
        value: double.tryParse(kitty.percentage.toString()??'')??0,
        minHeight: 3.h,
        backgroundColor: Colors.grey.shade300,
        valueColor:
            AlwaysStoppedAnimation(_getStatusColor(kitty.kittyStatus ?? "")),
      ),
    );
  }

  Widget _buildFooter() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: _buildInfoColumn(
            icon: Icons.calendar_today_rounded,
            title: 'created_label'.tr,
            value: DateFormat('MMM dd, yyyy')
                .format(kitty.kitty?.createdAt?.toLocal() ?? DateTime.now()),
          ),
        ),
        const SizedBox(width: 8),
        if(kitty.kitty?.endDate != null)
        Expanded(
          child: _buildInfoColumn(
            icon: Icons.access_time_rounded,
            title:  DateTimeFormat.relative(
              kitty.kitty?.endDate ?? DateTime.now(),
              levelOfPrecision: 1,
              prependIfBefore: '',
              ifNow: "now".tr,
              appendIfAfter: 'ago'.tr,
            ).contains('ago')
                ? 'ended'.tr
                : 'ends_in_label'.tr,
            value: TimeDurationTranslator.translateDuration(DateTimeFormat.relative(
              kitty.kitty!.endDate!,
              levelOfPrecision: 1,
              prependIfBefore: '',
              ifNow: "now".tr,
              appendIfAfter: 'ago'.tr,
            )),
          ),
        ),
        const SizedBox(width: 8),
        _buildStatusChip(),
      ],
    );
  }

  Widget _buildInfoColumn(
      {required IconData icon, required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18.w, color: Colors.grey.shade600),
            SizedBox(width: 4.w),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w700,
            color: isLight.value ? Colors.grey.shade800 : Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip() {
    final statusColor = _getStatusColor(kitty.kittyStatus ?? "");
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
          color: statusColor.withOpacity(0.15),
          borderRadius: BorderRadius.circular(30)),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            kitty.kittyStatus?.toLowerCase().tr ?? "unknown".tr,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w700,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return const Color(0xFF00C853);
      case "completed":
        return const Color(0xFF6200EA);
      case "settlement initiated":
        return const Color(0xFFFFD600);
      default:
        return const Color(0xFFE53935);
    }
  }

  Widget _buildCategoriesDisplay() {
    // Check if categories exist and are not empty
    if (kitty.kitty?.categories == null || kitty.kitty!.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: 8.h),
      child: SizedBox(
        height: 28.h,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: kitty.kitty!.categories!.length,
          separatorBuilder: (context, index) => SizedBox(width: 8.w),
          itemBuilder: (context, index) {
            final category = kitty.kitty!.categories![index];
            return _buildCategoryChip(category.name ?? '');
          },
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String categoryName) {
    if (categoryName.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2196F3).withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF2196F3).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6.w,
            height: 6.w,
            decoration: const BoxDecoration(
              color: Color(0xFF2196F3),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6.w),
          Text(
            categoryName,
            style: TextStyle(
              fontSize: 11.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2196F3),
            ),
          ),
        ],
      ),
    );
  }

  // ignore: unused_element
  double _calculateProgress() {
    final start = kitty.kitty?.createdAt ?? DateTime.now();
    final end =
        kitty.kitty?.endDate ?? DateTime.now().add(const Duration(days: 1));
    final total = end.difference(start).inSeconds;
    final passed = DateTime.now().difference(start).inSeconds;
    return (passed / total).clamp(0.0, 1.0);
  }

  void _viewKittyDetails(BuildContext context) {
    final dataController = Get.put(DataController());
    dataController.kitty.value = kitty;
    Get.toNamed(NavRoutes.viewingsinglekittyScreen);
  }
}

Color getKittyStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active":
    case "completed":
      return const Color(0xFF56AF57);
    case "settlement initiated":
      return const Color.fromARGB(255, 206, 104, 192);
    default:
      return const Color(0xFFEE5B60);
  }
}
