// Transaction Type Enum and Configuration
// Defines different types of transactions and their configurations

enum TransactionType {
  user,
  kitty,
  chama,
  event,
}

extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.user:
        return 'My Transactions';
      case TransactionType.kitty:
        return 'Kitty Transactions';
      case TransactionType.chama:
        return 'Chama Transactions';
      case TransactionType.event:
        return 'Event Transactions';
    }
  }

  String get apiEndpoint {
    switch (this) {
      case TransactionType.user:
        return 'user/transactions';
      case TransactionType.kitty:
        return 'kitty/transactions';
      case TransactionType.chama:
        return 'chama/transactions';
      case TransactionType.event:
        return 'event/transactions';
    }
  }

  List<String> get filterOptions {
    switch (this) {
      case TransactionType.user:
        return ["Code", "Date", "Kitty ID"];
      case TransactionType.kitty:
        return ["Code", "Date", "Account No"];
      case TransactionType.chama:
        return ["Code", "Date", "Account No"];
      case TransactionType.event:
        return ["Code", "Date", "Ticket ID"];
    }
  }

  bool get supportsExport => true;
  
  bool get supportsEdit {
    switch (this) {
      case TransactionType.kitty:
        return true;
      case TransactionType.user:
      case TransactionType.chama:
      case TransactionType.event:
        return false;
    }
  }
}

/// Configuration class for transaction page parameters
class TransactionPageConfig {
  final TransactionType transactionType;
  final int? entityId; // kittyId, chamaId, eventId
  final String? userId; // for user transactions
  final String? title; // custom title
  final String? accountNo; // for filtering
  final bool isFullPage;
  final bool showExportOptions;
  final bool showEditOptions;
  final int? kittyId;

  const TransactionPageConfig( {
    this.kittyId,
    required this.transactionType,
    this.entityId,
    this.userId,
    this.title,
    this.accountNo,
    this.isFullPage = true,
    this.showExportOptions = true,
    this.showEditOptions = true,
  });

  TransactionPageConfig copyWith({
    TransactionType? transactionType,
    int? entityId,
    String? userId,
    String? title,
    String? accountNo,
    bool? isFullPage,
    bool? showExportOptions,
    bool? showEditOptions,
  }) {
    return TransactionPageConfig(
      transactionType: transactionType ?? this.transactionType,
      entityId: entityId ?? this.entityId,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      accountNo: accountNo ?? this.accountNo,
      isFullPage: isFullPage ?? this.isFullPage,
      showExportOptions: showExportOptions ?? this.showExportOptions,
      showEditOptions: showEditOptions ?? this.showEditOptions,
    );
  }
}
