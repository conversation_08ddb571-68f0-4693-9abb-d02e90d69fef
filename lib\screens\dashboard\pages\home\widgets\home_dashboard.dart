import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/lottie/my_lottie.dart';  
class HomeDashboard extends StatelessWidget {
  final double containerHeight;
  const HomeDashboard({super.key, required this.containerHeight});

  @override
  Widget build(BuildContext context) {
    var phoneNumber = Get.find<Eventcontroller>()
        .getLocalUser()
        ?.phoneNumber
        ?.replaceRange(6, 9, "***");
    final _containerHeight = containerHeight < 1 ? 1.0 : containerHeight;

    return Stack(
      children: [
        Container(
          height: _containerHeight < 80.h ? 80.h : _containerHeight,
          margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
              colors: [AppColors.dark, AppColors.blueButtonColor],
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
            ),
          ),
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.all(_containerHeight > 100 ? 14 : 4),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AnimatedOpacity(
                      opacity: _containerHeight > 100 ? 1 : 0,
                      duration: const Duration(milliseconds: 200),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: ShaderMask(
                                  blendMode: BlendMode.srcIn,
                                  shaderCallback: (Rect bounds) {
                                    return LinearGradient(
                                      colors: [
                                        Colors.white,
                                        Colors.grey.shade600,
                                        Colors.white,
                                        Colors.grey.shade600,
                                        Colors.white,
                                      ],
                                      stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
                                      tileMode: TileMode.repeated,
                                    ).createShader(bounds);
                                  },
                                  child: Row(
                                    children: [
                                      Text(
                                        'do_contributions_with_freedom'.tr,
                                        style: TextStyle(
                                          fontFamily: 'Rustic',
                                          fontSize: 20.spMin,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: (_containerHeight * 20) / 150.h,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 8,
                left: 2,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: GetBuilder<UserKittyController>(
                    builder: (userController) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AnimatedDefaultTextStyle(
                                duration: const Duration(milliseconds: 200),
                                style: TextStyle(
                                  fontSize: _containerHeight > 100 ? 16 : 14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Sen',
                                ),
                                child: Text(
                                  "${userController.getLocalUser()?.firstName ?? ''} "
                                  "${userController.getLocalUser()?.secondName ?? ''}",
                                ),
                              ),
                              Text(
                                phoneNumber ?? '',
                                style: context.dividerTextSmall?.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          if (_containerHeight > 100)
                            AnimatedOpacity(
                              opacity: _containerHeight > 100 ? 1 : 0,
                              duration: const Duration(milliseconds: 200),
                              child: const SizedBox(width: 12), // Fixed Spacer replacement
                            ),
                        ],
                      );
                    },
                  ),
                ),
              ),
              Positioned(
                bottom: -12,
                right: -8,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    AnimatedScale(
                      scale: _containerHeight > 100 ? 1 : 0.6,
                      duration: const Duration(milliseconds: 200),
                      child: Container(
                        padding: const EdgeInsets.all(15),
                        height: 80,
                        decoration: const BoxDecoration(
                          color: AppColors.stackBlue,
                          shape: BoxShape.circle,
                        ),
                        child: Image.asset(
                          AssetUrl.launcher,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ),
                    // Remove any Expanded or Spacer widgets here
                  ],
                ),
              ),
            ],
          ),
        ),
        Positioned(left: -10.w, top: -10.h, child: const MyLottie3()),
      ],
    );
  }
}
