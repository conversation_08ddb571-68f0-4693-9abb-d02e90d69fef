# Event Validation Documentation

## Overview

This document provides comprehensive documentation for the event validation system implemented in the OneKitty mobile application. The validation system ensures data integrity, user experience, and security across all event-related operations.

## Table of Contents

1. [EventValidator Class](#eventvalidator-class)
2. [Validation Rules](#validation-rules)
3. [Usage Examples](#usage-examples)
4. [<PERSON>rro<PERSON> Handling](#error-handling)
5. [Test Cases](#test-cases)
6. [Best Practices](#best-practices)

## EventValidator Class

The `EventValidator` class is a comprehensive validation utility located at `lib/core/validators/event_validator.dart`. It provides static methods for validating various event-related data types.

### Key Features

- **Email Validation**: RFC-compliant email format validation
- **Phone Number Validation**: International and local phone number formats
- **Date Validation**: Event date constraints and logical validation
- **Coordinate Validation**: Geographic coordinate bounds checking
- **URL Validation**: Social media and website URL validation
- **Text Validation**: Length and content validation for various fields

## Validation Rules

### Email Validation

```dart
static bool isValidEmail(String email)
```

**Rules:**
- Must contain exactly one '@' symbol
- Local part (before @) must be 1-64 characters
- Domain part (after @) must be 1-255 characters
- Must contain at least one '.' in domain
- No consecutive dots allowed
- Valid characters: letters, numbers, dots, hyphens, underscores

**Valid Examples:**
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**Invalid Examples:**
- `invalid.email` (no @ symbol)
- `@domain.com` (empty local part)
- `user@` (empty domain)
- `<EMAIL>` (consecutive dots)

### Phone Number Validation

```dart
static bool isValidPhoneNumber(String phone)
```

**Rules:**
- Must be 10-15 digits long (excluding formatting)
- Can include country codes with '+' prefix
- Supports common formatting: spaces, hyphens, parentheses
- Removes all non-digit characters except '+' for validation

**Valid Examples:**
- `+1234567890`
- `(*************`
- `************`
- `+44 20 7946 0958`

**Invalid Examples:**
- `123` (too short)
- `12345678901234567890` (too long)
- `abc-def-ghij` (contains letters)

### Date Validation

```dart
static bool isValidEventDate(DateTime date)
static bool isValidDateRange(DateTime startDate, DateTime endDate)
```

**Rules:**
- Event dates must be in the future (after current date)
- End date must be after start date
- Maximum event duration: 365 days
- Minimum advance booking: 1 hour from current time

**Valid Examples:**
- Start: Tomorrow, End: Next week
- Start: Next month, End: Same day (single-day event)

**Invalid Examples:**
- Start: Yesterday (past date)
- Start: Next week, End: Tomorrow (end before start)
- Start: Today, End: Next year + 1 day (exceeds max duration)

### Coordinate Validation

```dart
static bool isValidLatitude(double latitude)
static bool isValidLongitude(double longitude)
static bool isValidCoordinates(double latitude, double longitude)
```

**Rules:**
- Latitude: -90.0 to 90.0 degrees
- Longitude: -180.0 to 180.0 degrees
- Precision: Up to 6 decimal places for accuracy

**Valid Examples:**
- Latitude: 40.7128, Longitude: -74.0060 (New York)
- Latitude: -33.8688, Longitude: 151.2093 (Sydney)

**Invalid Examples:**
- Latitude: 91.0 (exceeds maximum)
- Longitude: -181.0 (exceeds minimum)

## Usage Examples

### Basic Validation

```dart
// Email validation
String email = "<EMAIL>";
if (EventValidator.isValidEmail(email)) {
  print("Valid email");
} else {
  print("Invalid email format");
}

// Phone validation
String phone = "+****************";
if (EventValidator.isValidPhoneNumber(phone)) {
  print("Valid phone number");
} else {
  print("Invalid phone number format");
}
```

### Event Model Validation

```dart
// Using Event model's validate method
Event event = Event(
  title: "Sample Event",
  email: "<EMAIL>",
  phoneNumber: "+1234567890",
  startDate: DateTime.now().add(Duration(days: 7)),
  endDate: DateTime.now().add(Duration(days: 8)),
  latitude: 40.7128,
  longitude: -74.0060,
);

List<String> validationErrors = event.validate();
if (validationErrors.isEmpty) {
  print("Event is valid");
} else {
  print("Validation errors: ${validationErrors.join(', ')}");
}
```

### Controller Integration

```dart
// In CreateEventController
void validateAndCreateEvent() {
  // Validate individual fields
  if (!EventValidator.isValidEmail(emailController.text)) {
    showError("Please enter a valid email address");
    return;
  }
  
  if (!EventValidator.isValidPhoneNumber(phoneController.text)) {
    showError("Please enter a valid phone number");
    return;
  }
  
  // Validate date range
  if (!EventValidator.isValidDateRange(startDate.value, endDate.value)) {
    showError("End date must be after start date");
    return;
  }
  
  // Proceed with event creation
  createEvent();
}
```

## Error Handling

### Validation Error Types

1. **Format Errors**: Invalid format for email, phone, etc.
2. **Range Errors**: Values outside acceptable ranges
3. **Logic Errors**: Inconsistent data relationships
4. **Required Field Errors**: Missing mandatory information

### Error Messages

```dart
class ValidationMessages {
  static const String invalidEmail = "Please enter a valid email address";
  static const String invalidPhone = "Please enter a valid phone number";
  static const String pastDate = "Event date must be in the future";
  static const String invalidDateRange = "End date must be after start date";
  static const String invalidCoordinates = "Invalid location coordinates";
  static const String titleTooShort = "Event title must be at least 3 characters";
  static const String titleTooLong = "Event title cannot exceed 100 characters";
  static const String descriptionTooLong = "Description cannot exceed 1000 characters";
}
```

## Test Cases

### Email Validation Tests

```dart
void testEmailValidation() {
  // Valid emails
  assert(EventValidator.isValidEmail("<EMAIL>") == true);
  assert(EventValidator.isValidEmail("<EMAIL>") == true);
  assert(EventValidator.isValidEmail("<EMAIL>") == true);
  
  // Invalid emails
  assert(EventValidator.isValidEmail("invalid.email") == false);
  assert(EventValidator.isValidEmail("@domain.com") == false);
  assert(EventValidator.isValidEmail("user@") == false);
  assert(EventValidator.isValidEmail("<EMAIL>") == false);
}
```

### Phone Validation Tests

```dart
void testPhoneValidation() {
  // Valid phones
  assert(EventValidator.isValidPhoneNumber("+1234567890") == true);
  assert(EventValidator.isValidPhoneNumber("(*************") == true);
  assert(EventValidator.isValidPhoneNumber("************") == true);
  
  // Invalid phones
  assert(EventValidator.isValidPhoneNumber("123") == false);
  assert(EventValidator.isValidPhoneNumber("12345678901234567890") == false);
  assert(EventValidator.isValidPhoneNumber("abc-def-ghij") == false);
}
```

### Date Validation Tests

```dart
void testDateValidation() {
  DateTime now = DateTime.now();
  DateTime tomorrow = now.add(Duration(days: 1));
  DateTime nextWeek = now.add(Duration(days: 7));
  DateTime yesterday = now.subtract(Duration(days: 1));
  
  // Valid dates
  assert(EventValidator.isValidEventDate(tomorrow) == true);
  assert(EventValidator.isValidDateRange(tomorrow, nextWeek) == true);
  
  // Invalid dates
  assert(EventValidator.isValidEventDate(yesterday) == false);
  assert(EventValidator.isValidDateRange(nextWeek, tomorrow) == false);
}
```

### Coordinate Validation Tests

```dart
void testCoordinateValidation() {
  // Valid coordinates
  assert(EventValidator.isValidLatitude(40.7128) == true);
  assert(EventValidator.isValidLongitude(-74.0060) == true);
  assert(EventValidator.isValidCoordinates(40.7128, -74.0060) == true);
  
  // Invalid coordinates
  assert(EventValidator.isValidLatitude(91.0) == false);
  assert(EventValidator.isValidLongitude(-181.0) == false);
  assert(EventValidator.isValidCoordinates(91.0, -181.0) == false);
}
```

## Best Practices

### 1. Early Validation

- Validate input as soon as possible (on field blur or form submission)
- Provide immediate feedback to users
- Use real-time validation for better UX

### 2. Comprehensive Error Messages

- Provide clear, actionable error messages
- Explain what the user needs to fix
- Use consistent messaging across the application

### 3. Client-Side and Server-Side Validation

- Always validate on both client and server
- Client-side for UX, server-side for security
- Never trust client-side validation alone

### 4. Performance Considerations

- Cache validation results when appropriate
- Use debouncing for real-time validation
- Avoid excessive validation calls

### 5. Accessibility

- Ensure error messages are accessible to screen readers
- Use proper ARIA labels and roles
- Provide visual and textual error indicators

### 6. Testing

- Write comprehensive unit tests for all validation logic
- Test edge cases and boundary conditions
- Include integration tests for form validation flows

### 7. Internationalization

- Support different date formats for different locales
- Validate phone numbers according to country-specific rules
- Provide localized error messages

## Integration with Flutter Forms

### Using with TextFormField

```dart
TextFormField(
  controller: emailController,
  validator: (value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!EventValidator.isValidEmail(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  },
  decoration: InputDecoration(
    labelText: 'Email Address',
    errorStyle: TextStyle(color: Colors.red),
  ),
)
```

### Custom Validation Widget

```dart
class ValidatedTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool Function(String) validator;
  final String errorMessage;
  
  const ValidatedTextField({
    Key? key,
    required this.controller,
    required this.label,
    required this.validator,
    required this.errorMessage,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '$label is required';
        }
        if (!validator(value)) {
          return errorMessage;
        }
        return null;
      },
      decoration: InputDecoration(
        labelText: label,
        errorStyle: TextStyle(color: Colors.red),
      ),
    );
  }
}
```

## Conclusion

The event validation system provides a robust foundation for ensuring data quality and user experience in the OneKitty application. By following the patterns and practices outlined in this documentation, developers can maintain consistency and reliability across all event-related features.

For questions or improvements to this validation system, please refer to the development team or create an issue in the project repository.