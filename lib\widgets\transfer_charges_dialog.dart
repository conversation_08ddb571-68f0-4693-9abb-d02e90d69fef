import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/formatted_currency.dart';

/// Enhanced transfer charges transparency dialog
/// Shows detailed breakdown of all charges before transfer confirmation
class TransferChargesDialog extends StatelessWidget {
  final double amount;
  final double platformFee;
  final double transactionCharge;
  final double thirdPartyFee;
  final double totalCharges;
  final double finalAmount;
  final double remainingBalance;
  final String recipientName;
  final String recipientAccount;
  final String? recipientAccountRef;
  final String transferMode;
  final String reason;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;
  final bool isLoading;

  const TransferChargesDialog({
    super.key,
    required this.amount,
    required this.platformFee,
    required this.transactionCharge,
    required this.thirdPartyFee,
    required this.totalCharges,
    required this.finalAmount,
    required this.remainingBalance,
    required this.recipientName,
    required this.recipientAccount,
    this.recipientAccountRef,
    required this.transferMode,
    required this.reason,
    required this.onConfirm,
    required this.onCancel,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'transfer_confirmation'.tr,
                    style: CustomTextStyles.titleMediumBlack900,
                  ),
                ),
                IconButton(
                  onPressed: onCancel,
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 20.h),

            // Transfer Summary
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'transfer_summary'.tr,
                    style: CustomTextStyles.titleSmallGray900.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildSummaryRow('${'to'.tr}:', recipientName),
                  _buildSummaryRow('${'account'.tr}:', recipientAccount),
                  if (recipientAccountRef != null)
                    _buildSummaryRow('${'reference'.tr}:', recipientAccountRef!),
                  _buildSummaryRow('${'mode'.tr}:', transferMode),
                  _buildSummaryRow('${'reason'.tr}:', reason),
                ],
              ),
            ),
            SizedBox(height: 16.h),

            // Amount Breakdown
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'amount_breakdown'.tr,
                    style: CustomTextStyles.titleSmallGray900.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildAmountRow('${'transfer_amount'.tr}:', amount, isMain: true),
                  const Divider(),
                  Text(
                    'charges'.tr,
                    style: CustomTextStyles.bodyMediumGray600.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  _buildChargeRow('platform_fee'.tr, platformFee),
                  _buildChargeRow('transaction_charge'.tr, transactionCharge),
                  _buildChargeRow('third_party_fee'.tr, thirdPartyFee),
                  const Divider(),
                  _buildAmountRow('${'total_charges'.tr}:', totalCharges, 
                      color: Colors.red.shade600),
                  _buildAmountRow('${'amount_to_recipient'.tr}:', finalAmount, 
                      color: Colors.green.shade600, isMain: true),
                ],
              ),
            ),
            SizedBox(height: 16.h),

            // Balance Information
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    color: Colors.orange.shade600,
                    size: 20,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'remaining_balance'.tr,
                          style: CustomTextStyles.bodyMediumGray600,
                        ),
                        Text(
                          FormattedCurrency.getFormattedCurrency(remainingBalance),
                          style: CustomTextStyles.titleSmallGray900.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 24.h),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: isLoading ? null : onCancel,
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('cancel'.tr),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: MyButton(
                    onClick: isLoading ? null : onConfirm,
                    label: isLoading ? 'processing'.tr : 'confirm_transfer'.tr,
                    showLoading: isLoading,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: CustomTextStyles.bodySmallGray600,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: CustomTextStyles.bodySmallGray900.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(String label, double amount, {
    Color? color,
    bool isMain = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isMain
                ? CustomTextStyles.titleSmallGray900.copyWith(
                    fontWeight: FontWeight.w600,
                  )
                : CustomTextStyles.bodyMediumGray600,
          ),
          Text(
            FormattedCurrency.getFormattedCurrency(amount),
            style: (isMain
                ? CustomTextStyles.titleSmallGray900
                : CustomTextStyles.bodyMediumGray600).copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChargeRow(String label, double amount) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: Text(
              label,
              style: CustomTextStyles.bodySmallGray600,
            ),
          ),
          Text(
            FormattedCurrency.getFormattedCurrency(amount),
            style: CustomTextStyles.bodySmallGray900.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Helper class for parsing charge data from API responses
class TransferChargeData {
  final double amount;
  final double platformFee;
  final double transactionCharge;
  final double thirdPartyFee;
  final double totalCharges;
  final double finalAmount;
  final double remainingBalance;
  final String recipientName;
  final String recipientAccount;
  final String? recipientAccountRef;

  TransferChargeData({
    required this.amount,
    required this.platformFee,
    required this.transactionCharge,
    required this.thirdPartyFee,
    required this.totalCharges,
    required this.finalAmount,
    required this.remainingBalance,
    required this.recipientName,
    required this.recipientAccount,
    this.recipientAccountRef,
  });

  factory TransferChargeData.fromApiResponse(Map<String, dynamic> data) {
    final charges = data['charges'] ?? 0.0;
    final amount = double.tryParse(data['amount_received']?.toString() ?? '0') ?? 0.0;
    
    // Parse individual charge components if available
    final chargesBreakdown = data['charges_breakdown'] as Map<String, dynamic>?;
    final platformFee = double.tryParse(chargesBreakdown?['platform_fee']?.toString() ?? '0') ?? 0.0;
    final transactionCharge = double.tryParse(chargesBreakdown?['transaction_charge']?.toString() ?? '0') ?? 0.0;
    final thirdPartyFee = double.tryParse(chargesBreakdown?['third_party_fee']?.toString() ?? '0') ?? 0.0;
    
    // If breakdown not available, estimate based on total charges
    final totalCharges = double.tryParse(charges.toString()) ?? 0.0;
    final estimatedPlatformFee = platformFee > 0 ? platformFee : totalCharges * 0.3;
    final estimatedTransactionCharge = transactionCharge > 0 ? transactionCharge : totalCharges * 0.5;
    final estimatedThirdPartyFee = thirdPartyFee > 0 ? thirdPartyFee : totalCharges * 0.2;

    return TransferChargeData(
      amount: amount,
      platformFee: estimatedPlatformFee,
      transactionCharge: estimatedTransactionCharge,
      thirdPartyFee: estimatedThirdPartyFee,
      totalCharges: totalCharges,
      finalAmount: amount - totalCharges,
      remainingBalance: double.tryParse(data['kitty_balance']?.toString() ?? '0') ?? 0.0,
      recipientName: data['beneficiary_name'] ?? 'unknown'.tr,
      recipientAccount: data['beneficiary_account'] ?? '',
      recipientAccountRef: data['beneficiary_account_ref'],
    );
  }
}
