import 'package:onekitty/configs/country_specifics.dart';

const extraThrees = {
  
  'incomplete_submission': 'Incomplete Submission',
  'please_complete_all_requirements': 'Please complete all requirements:',
  'front_id_requirement': '• Front ID',
  'back_id_requirement': '• Back ID',
  'selfie_requirement': '• Selfie',
  'valid_id_number_requirement': '• Valid ID Number',
  'confirm_submission': 'Confirm Submission',
  'confirm_submit_kyc': 'Are you sure you want to submit your KYC information?',
  'confirm_delete_item': 'Are you sure you want to delete this {item}?',
  'file_picker_not_implemented':
      'File picker not implemented - would open file picker here',
  'media_url_example': 'https://example.com/image.jpg',
  'error_loading_media': 'Error loading media',
  'invalid_media_index': 'Invalid media index',
  'add_atleast_one_ticket': 'Add at least one ticket',
  'error_creating_event': 'Error creating event',
  'could_not_create_event': 'Could not create event',
  'error_adding_social_media': 'Error adding social media',
  'enter_valid_website_url': 'Please enter a valid website URL',
  'document_title_length_validation':
      'Document title must be between 5 and 300',
  'error_downloading_file': 'Error downloading file',
  'go_to_page': 'Go to {page}',
  'invalid_kitty_id_format': 'Invalid Kitty ID : {kittyId}',
  'maximum_5_attempts': 'Maximum 5 attempts',
  'success_status': 'SUCCESS',
  'failed_status': 'FAILED',
  'some_error_occurred': 'Some error occurred',
  'customer_message': 'Customer message',
  'e_g_55': 'e.g 55',
  'select_categories_placeholder': 'Select Categories',
  'loading_categories_text': 'Loading categories...',
  'search_categories': 'Search categories...',
  'filter_kitties_tooltip': 'Filter Kitties',
  'prefetching': 'Prefetching...',
  'acc_no_colon': 'AccNo:',
  'created_label': 'Created',
  'ends_in_label': 'Ends In',
  'n_a_label': 'N/A',
  'payment_ref_label': 'Payment Ref',
  'enter_your_payment_ref_format': 'Enter your {paymentRef}',
  'payment_ref_required_format': '"{paymentRef}" is required',
  'please_provide_payment_ref_format': 'Please provide your {paymentRef}',
  'every_shilling_counts_message':
      'Every shilling counts; make a positive impact on',
  'transaction_id_colon': 'Transaction ID:',
  'share_msg_transaction_format':
      'Phone Number: {phoneNo}\nAmount: ${CountryConfig.getCurrencyCode} {amount}\nTransaction Code: {transactionCode}\nKitty: https://onekitty.co.ke/kitty/{kittyId}',
  'back_button': 'Back',
  'next_button': 'Next',
  'submit_button': 'Submit',
  'done_button': 'Done',
  'error_label': 'Error',
  'five_thousand': '5000',
  'mpesa_label': 'M-Pesa',
  'visa_label': 'Card',
  'check_your_values_error': 'Check Your Values',
  'success_toast_label': 'success',
  'error_toast_label': 'Error',
  'fill_in_all_values': 'Fill in all values',
  'select_categories_text': 'Select Categories ',
  'all_categories_text': 'All Categories',
  'unknown_category_text': 'Unknown Category',
  'filter_kitties_text': 'Filter Kitties',
  'refine_search_results_text': 'Refine your search results',
  'search_by_name_or_id_text': 'Search by Name or ID',
  'enter_kitty_name_or_id_text': 'Enter kitty name or ID',
  'date_range_text': 'Date Range',
  'start_date_text': 'Start Date',
  'end_date_text': 'End Date',
  'select_date_text': 'Select date',
  'status_text': 'Status',
  'select_status_text': 'Select status',
  'all_statuses_text': 'All Statuses',
  'category_text': 'Category',
  'select_category_text': 'Select category',
  'clear_all_text': 'Clear All',
  'apply_filters_text': 'Apply Filters',
  'draft_text': 'Draft',
  'active_text': 'Active',
  'complete_text': 'Complete',
  'primary_text': 'Primary',
  'expired_text': 'Expired',
  'percentage_text': 'Percentage',
  'amount_text': 'Amount',
  'beneficiary_phone_number': 'Beneficiary Phone Number',
  'who_should_receive_contribution':
      'Who should receive the contribution at the end',
  'mpesa_paybill_label': 'Mpesa PayBill',
  'paybill_number_label': 'PayBill number',
  'paybill_hint': 'paybill',
  'enter_paybill_number_validation': 'Enter Paybill Number',
  'paybill_letters_numbers_only':
      'Paybill number can only contain letters and numbers',
  'account_number_label': 'Account number',
  'account_number_hint': 'account number',
  'enter_account_number_validation': 'Enter Account Number',
  'mpesa_till_number_label': 'Mpesa Till number',
  'till_number_label': 'Till number',
  'till_number_hint': 'till number',
  'enter_till_number_validation': 'Enter Till Number',
  'till_no_alphabets': 'Till number can not contain Alphabets',
  'select_bank_label': 'Select Bank',
  'bank_account_required_validation': 'Please enter your bank account number',
  'bank_account_label': 'Bank Account',
  'edit_kitty_end_date': 'Edit Kitty End Date',
  'back_label': 'Back',
  'update_label': 'Update',
};