import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/get_invited_users_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

class InviteUsersController extends GetxController implements GetxService {
  final isLoadingInvitees = false.obs;
  final sendInvites = false.obs;
  final invitedUsers = <GetInvitedUsersModel>[].obs;
  final HttpService apiProvider = Get.find();
  final newInvites = <InviteUsersModel>[].obs;
  final logger = Get.find<Logger>();
  Future<void> fetchInvites(int eventId) async {
    isLoadingInvitees(true);
    try {
      var response = await apiProvider.request(
        method: Method.GET,
        url: "${ApiUrls.FETCHINVITEDUSERS}?page=0&size=20&event_id=$eventId",
      );
      if (response.data['status'] ?? false) {
        final returnedData = response.data['data']['items'] as List;
        invitedUsers.value = returnedData
            .map((item) => GetInvitedUsersModel.fromJson(item))
            .toList();
      } else {
        throw Exception('Failed to load invitees');
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed to fetch invitees',
          backgroundColor: Colors.red);
          logger.e('$e');
    } finally {
      isLoadingInvitees(false);
    }
  }

  Future inviteMember(
      int eventId, int ticketId, List<InviteUsersModel> users) async {
    sendInvites(true);
    try {
      var response = await apiProvider.request(
          method: Method.POST,
          url: ApiUrls.INVITEUSERS,
          params: {
            "ticket_id": ticketId,
            "users": users.map((e) => e.toJson()).toList()
          });
      if (response.data['status'] ?? false) {
        Get.snackbar('success', 'invites sent');
        newInvites.clear();
        fetchInvites(eventId);
      } else {
        Get.snackbar('Error', response.data['message'] ?? 'failed to invite',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e('$e');
    } finally {
      sendInvites(false);
    }
  }
}

class InviteUsersModel {
  final String phoneNumber;
  final String firstName;
  final String secondName;
  final String email;

  InviteUsersModel({
    required this.phoneNumber,
    required this.firstName,
    required this.secondName,
    required this.email,
  });

  factory InviteUsersModel.fromJson(Map<String, dynamic> json) {
    return InviteUsersModel(
      phoneNumber: json['phone_number'],
      firstName: json['first_name'],
      secondName: json['second_name'],
      email: json['email'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone_number': phoneNumber,
      'first_name': firstName,
      'second_name': secondName,
      'email': email,
    };
  }
}
