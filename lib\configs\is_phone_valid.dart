class PhoneNumberValidator {
  // Comprehensive country code database with their phone number patterns
  static final Map<String, PhoneCountryInfo> _countryData = {
    // North America
    '1': const PhoneCountryInfo('US/CA', 10, 10, r'^[2-9]\d{2}[2-9]\d{6}$'),

    // Europe
    '44': const PhoneCountryInfo('GB', 10, 10, r'^[1-9]\d{9}$'),
    '33': const PhoneCountryInfo('FR', 9, 9, r'^[1-9]\d{8}$'),
    '49': const PhoneCountryInfo('DE', 10, 12, r'^[1-9]\d{9,11}$'),
    '39': const PhoneCountryInfo('IT', 9, 11, r'^[3]\d{8,10}$'),
    '34': const PhoneCountryInfo('ES', 9, 9, r'^[6-9]\d{8}$'),
    '31': const PhoneCountryInfo('NL', 9, 9, r'^[6]\d{8}$'),
    '32': const PhoneCountryInfo('BE', 9, 9, r'^[4-9]\d{8}$'),
    '41': const PhoneCountryInfo('CH', 9, 9, r'^[7]\d{8}$'),
    '43': const PhoneCountryInfo('AT', 10, 11, r'^[6-9]\d{9,10}$'),
    '45': const PhoneCountryInfo('DK', 8, 8, r'^[2-9]\d{7}$'),
    '46': const PhoneCountryInfo('SE', 9, 9, r'^[7]\d{8}$'),
    '47': const PhoneCountryInfo('NO', 8, 8, r'^[4-9]\d{7}$'),
    '48': const PhoneCountryInfo('PL', 9, 9, r'^[5-9]\d{8}$'),
    '351': const PhoneCountryInfo('PT', 9, 9, r'^[9]\d{8}$'),
    '353': const PhoneCountryInfo('IE', 9, 9, r'^[8-9]\d{8}$'),
    '358': const PhoneCountryInfo('FI', 9, 10, r'^[4-5]\d{8,9}$'),
    '372': const PhoneCountryInfo('EE', 7, 8, r'^[5-8]\d{6,7}$'),
    '371': const PhoneCountryInfo('LV', 8, 8, r'^[2]\d{7}$'),
    '370': const PhoneCountryInfo('LT', 8, 8, r'^[6]\d{7}$'),

    // Asia-Pacific
    '86': const PhoneCountryInfo('CN', 11, 11, r'^[1]\d{10}$'),
    '81': const PhoneCountryInfo('JP', 10, 11, r'^[7-9]\d{9,10}$'),
    '82': const PhoneCountryInfo('KR', 10, 11, r'^[1]\d{9,10}$'),
    '91': const PhoneCountryInfo('IN', 10, 10, r'^[6-9]\d{9}$'),
    '65': const PhoneCountryInfo('SG', 8, 8, r'^[8-9]\d{7}$'),
    '60': const PhoneCountryInfo('MY', 9, 10, r'^[1]\d{8,9}$'),
    '62': const PhoneCountryInfo('ID', 9, 12, r'^[8]\d{8,11}$'),
    '63': const PhoneCountryInfo('PH', 10, 10, r'^[9]\d{9}$'),
    '66': const PhoneCountryInfo('TH', 9, 9, r'^[6-9]\d{8}$'),
    '84': const PhoneCountryInfo('VN', 9, 10, r'^[3-9]\d{8,9}$'),
    '852': const PhoneCountryInfo('HK', 8, 8, r'^[5-9]\d{7}$'),
    '853': const PhoneCountryInfo('MO', 8, 8, r'^[6]\d{7}$'),
    '886': const PhoneCountryInfo('TW', 9, 9, r'^[9]\d{8}$'),
    '61': const PhoneCountryInfo('AU', 9, 9, r'^[4]\d{8}$'),
    '64': const PhoneCountryInfo('NZ', 8, 9, r'^[2]\d{7,8}$'),

    // Africa
    '27': const PhoneCountryInfo('ZA', 9, 9, r'^[6-8]\d{8}$'),
    '234': const PhoneCountryInfo('NG', 10, 10, r'^[7-9]\d{9}$'),
    '254': const PhoneCountryInfo('KE', 9, 9, r'^[1-9]\d{8}$'),
    '255': const PhoneCountryInfo('TZ', 9, 9, r'^[6-7]\d{8}$'),
    '256': const PhoneCountryInfo('UG', 9, 9, r'^[7]\d{8}$'),
    '250': const PhoneCountryInfo('RW', 9, 9, r'^[7]\d{8}$'),
    '251': const PhoneCountryInfo('ET', 9, 9, r'^[9]\d{8}$'),
    '212': const PhoneCountryInfo('MA', 9, 9, r'^[6-7]\d{8}$'),
    '213': const PhoneCountryInfo('DZ', 9, 9, r'^[5-7]\d{8}$'),
    '216': const PhoneCountryInfo('TN', 8, 8, r'^[2-9]\d{7}$'),
    '218': const PhoneCountryInfo('LY', 9, 9, r'^[9]\d{8}$'),
    '220': const PhoneCountryInfo('GM', 7, 7, r'^[2-9]\d{6}$'),
    '221': const PhoneCountryInfo('SN', 9, 9, r'^[7]\d{8}$'),
    '223': const PhoneCountryInfo('ML', 8, 8, r'^[6-9]\d{7}$'),
    '224': const PhoneCountryInfo('GN', 9, 9, r'^[6]\d{8}$'),
    '225': const PhoneCountryInfo('CI', 10, 10, r'^[0-9]\d{9}$'),
    '226': const PhoneCountryInfo('BF', 8, 8, r'^[6-7]\d{7}$'),
    '227': const PhoneCountryInfo('NE', 8, 8, r'^[9]\d{7}$'),
    '228': const PhoneCountryInfo('TG', 8, 8, r'^[9]\d{7}$'),
    '229': const PhoneCountryInfo('BJ', 8, 8, r'^[6-9]\d{7}$'),
    '230': const PhoneCountryInfo('MU', 8, 8, r'^[5]\d{7}$'),
    '232': const PhoneCountryInfo('SL', 8, 8, r'^[2-9]\d{7}$'),
    '233': const PhoneCountryInfo('GH', 9, 9, r'^[2-5]\d{8}$'),
    '235': const PhoneCountryInfo('TD', 8, 8, r'^[6-9]\d{7}$'),
    '236': const PhoneCountryInfo('CF', 8, 8, r'^[7]\d{7}$'),
    '237': const PhoneCountryInfo('CM', 9, 9, r'^[6-7]\d{8}$'),
    '238': const PhoneCountryInfo('CV', 7, 7, r'^[5-9]\d{6}$'),
    '239': const PhoneCountryInfo('ST', 7, 7, r'^[9]\d{6}$'),
    '240': const PhoneCountryInfo('GQ', 9, 9, r'^[2-9]\d{8}$'),
    '241': const PhoneCountryInfo('GA', 8, 8, r'^[0-9]\d{7}$'),
    '242': const PhoneCountryInfo('CG', 9, 9, r'^[0-9]\d{8}$'),
    '243': const PhoneCountryInfo('CD', 9, 9, r'^[8-9]\d{8}$'),
    '244': const PhoneCountryInfo('AO', 9, 9, r'^[9]\d{8}$'),
    '245': const PhoneCountryInfo('GW', 7, 7, r'^[5-9]\d{6}$'),
    '246': const PhoneCountryInfo('IO', 7, 7, r'^[3]\d{6}$'),
    '248': const PhoneCountryInfo('SC', 7, 7, r'^[2]\d{6}$'),
    '249': const PhoneCountryInfo('SD', 9, 9, r'^[9]\d{8}$'),
    '252': const PhoneCountryInfo('SO', 8, 9, r'^[6-9]\d{7,8}$'),
    '253': const PhoneCountryInfo('DJ', 8, 8, r'^[7]\d{7}$'),
    '257': const PhoneCountryInfo('BI', 8, 8, r'^[1-9]\d{7}$'),
    '258': const PhoneCountryInfo('MZ', 9, 9, r'^[8]\d{8}$'),
    '260': const PhoneCountryInfo('ZM', 9, 9, r'^[9-7]\d{8}$'),
    '261': const PhoneCountryInfo('MG', 9, 9, r'^[3]\d{8}$'),
    '262': const PhoneCountryInfo('RE', 9, 9, r'^[6-7]\d{8}$'),
    '263': const PhoneCountryInfo('ZW', 9, 9, r'^[7]\d{8}$'),
    '264': const PhoneCountryInfo('NA', 9, 9, r'^[6-8]\d{8}$'),
    '265': const PhoneCountryInfo('MW', 9, 9, r'^[8-9]\d{8}$'),
    '266': const PhoneCountryInfo('LS', 8, 8, r'^[5-6]\d{7}$'),
    '267': const PhoneCountryInfo('BW', 8, 8, r'^[7]\d{7}$'),
    '268': const PhoneCountryInfo('SZ', 8, 8, r'^[7-8]\d{7}$'),
    '269': const PhoneCountryInfo('KM', 7, 7, r'^[3]\d{6}$'),

    // Middle East
    '971': const PhoneCountryInfo('AE', 9, 9, r'^[5]\d{8}$'),
    '966': const PhoneCountryInfo('SA', 9, 9, r'^[5]\d{8}$'),
    '974': const PhoneCountryInfo('QA', 8, 8, r'^[3-7]\d{7}$'),
    '965': const PhoneCountryInfo('KW', 8, 8, r'^[5-9]\d{7}$'),
    '968': const PhoneCountryInfo('OM', 8, 8, r'^[9]\d{7}$'),
    '973': const PhoneCountryInfo('BH', 8, 8, r'^[3-9]\d{7}$'),
    '964': const PhoneCountryInfo('IQ', 10, 10, r'^[7]\d{9}$'),
    '963': const PhoneCountryInfo('SY', 9, 9, r'^[9]\d{8}$'),
    '962': const PhoneCountryInfo('JO', 9, 9, r'^[7]\d{8}$'),
    '961': const PhoneCountryInfo('LB', 8, 8, r'^[3-9]\d{7}$'),
    '972': const PhoneCountryInfo('IL', 9, 9, r'^[5]\d{8}$'),
    '970': const PhoneCountryInfo('PS', 9, 9, r'^[5]\d{8}$'),
    '98': const PhoneCountryInfo('IR', 10, 10, r'^[9]\d{9}$'),
    '90': const PhoneCountryInfo('TR', 10, 10, r'^[5]\d{9}$'),

    // South America
    '55': const PhoneCountryInfo('BR', 10, 11, r'^[1-9]\d{9,10}$'),
    '54': const PhoneCountryInfo('AR', 10, 10, r'^[9]\d{9}$'),
    '56': const PhoneCountryInfo('CL', 8, 9, r'^[2-9]\d{7,8}$'),
    '57': const PhoneCountryInfo('CO', 10, 10, r'^[3]\d{9}$'),
    '58': const PhoneCountryInfo('VE', 10, 10, r'^[4]\d{9}$'),
    '51': const PhoneCountryInfo('PE', 9, 9, r'^[9]\d{8}$'),
    '593': const PhoneCountryInfo('EC', 9, 9, r'^[9]\d{8}$'),
    '595': const PhoneCountryInfo('PY', 9, 9, r'^[9]\d{8}$'),
    '598': const PhoneCountryInfo('UY', 8, 8, r'^[9]\d{7}$'),
    '591': const PhoneCountryInfo('BO', 8, 8, r'^[6-7]\d{7}$'),
    '597': const PhoneCountryInfo('SR', 7, 7, r'^[6-8]\d{6}$'),
    '592': const PhoneCountryInfo('GY', 7, 7, r'^[6]\d{6}$'),
    '594': const PhoneCountryInfo('GF', 9, 9, r'^[6]\d{8}$'),

    // Caribbean & Central America
    '52': const PhoneCountryInfo('MX', 10, 10, r'^[1-9]\d{9}$'),
    '53': const PhoneCountryInfo('CU', 8, 8, r'^[5]\d{7}$'),
    '507': const PhoneCountryInfo('PA', 8, 8, r'^[6]\d{7}$'),
    '506': const PhoneCountryInfo('CR', 8, 8, r'^[6-8]\d{7}$'),
    '505': const PhoneCountryInfo('NI', 8, 8, r'^[5-8]\d{7}$'),
    '504': const PhoneCountryInfo('HN', 8, 8, r'^[3-9]\d{7}$'),
    '503': const PhoneCountryInfo('SV', 8, 8, r'^[6-7]\d{7}$'),
    '502': const PhoneCountryInfo('GT', 8, 8, r'^[4-5]\d{7}$'),
    '501': const PhoneCountryInfo('BZ', 7, 7, r'^[6]\d{6}$'),

    // Additional regions
    '7': const PhoneCountryInfo('RU/KZ', 10, 10, r'^[9]\d{9}$'),
    '380': const PhoneCountryInfo('UA', 9, 9, r'^[5-9]\d{8}$'),
    '375': const PhoneCountryInfo('BY', 9, 9, r'^[2-4]\d{8}$'),
    '374': const PhoneCountryInfo('AM', 8, 8, r'^[4-9]\d{7}$'),
    '995': const PhoneCountryInfo('GE', 9, 9, r'^[5]\d{8}$'),
    '994': const PhoneCountryInfo('AZ', 9, 9, r'^[5]\d{8}$'),
    '993': const PhoneCountryInfo('TM', 8, 8, r'^[6]\d{7}$'),
    '992': const PhoneCountryInfo('TJ', 9, 9, r'^[9]\d{8}$'),
    '996': const PhoneCountryInfo('KG', 9, 9, r'^[5-7]\d{8}$'),
    '998': const PhoneCountryInfo('UZ', 9, 9, r'^[9]\d{8}$'),
  };

  /// Main validation function that handles phone numbers with or without '+'
  static ValidationResult isValidPhone(String phoneNumber) {
    try {
      if (phoneNumber.isEmpty) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'Phone number cannot be empty',
          originalNumber: phoneNumber,
        );
      }

      // Clean the input
      String cleanedNumber = _cleanPhoneNumber(phoneNumber);

      // Ensure it starts with '+'
      String formattedNumber = _ensurePlusPrefix(cleanedNumber);

      // Extract country code and local number
      CountryCodeInfo countryInfo = _extractCountryCode(formattedNumber);

      if (countryInfo.countryCode.isEmpty) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'Invalid or unsupported country code',
          originalNumber: phoneNumber,
          formattedNumber: formattedNumber,
        );
      }

      // Validate the local number
      PhoneCountryInfo? phoneInfo = _countryData[countryInfo.countryCode];

      if (phoneInfo == null) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'Country code ${countryInfo.countryCode} not supported',
          originalNumber: phoneNumber,
          formattedNumber: formattedNumber,
          countryCode: countryInfo.countryCode,
        );
      }

      // Check local number length
      if (countryInfo.localNumber.length < phoneInfo.minLength ||
          countryInfo.localNumber.length > phoneInfo.maxLength) {
        return ValidationResult(
          isValid: false,
          errorMessage:
              'Invalid length for ${phoneInfo.countryName}. Expected ${phoneInfo.minLength}-${phoneInfo.maxLength} digits after country code',
          originalNumber: phoneNumber,
          formattedNumber: formattedNumber,
          countryCode: countryInfo.countryCode,
          countryName: phoneInfo.countryName,
        );
      }

      // Check pattern matching
      RegExp pattern = RegExp(phoneInfo.pattern);
      if (!pattern.hasMatch(countryInfo.localNumber)) {
        return ValidationResult(
          isValid: false,
          errorMessage:
              'Invalid format for ${phoneInfo.countryName} mobile number',
          originalNumber: phoneNumber,
          formattedNumber: formattedNumber,
          countryCode: countryInfo.countryCode,
          countryName: phoneInfo.countryName,
        );
      }

      // All validations passed
      return ValidationResult(
        isValid: true,
        originalNumber: phoneNumber,
        formattedNumber: formattedNumber,
        countryCode: countryInfo.countryCode,
        countryName: phoneInfo.countryName,
        localNumber: countryInfo.localNumber,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Unexpected error during validation: ${e.toString()}',
        originalNumber: phoneNumber,
      );
    }
  }

  /// Clean phone number by removing spaces, dashes, brackets, and other formatting
  static String _cleanPhoneNumber(String phoneNumber) {
    return phoneNumber
        .replaceAll(
          RegExp(r'[\s\-\(\)\[\]\.]+'),
          '',
        ) // Remove spaces, dashes, brackets, dots
        .replaceAll(RegExp(r'[^\d\+]'), ''); // Keep only digits and plus sign
  }

  /// Ensure phone number starts with '+'
  static String _ensurePlusPrefix(String phoneNumber) {
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }
    return '+$phoneNumber';
  }

  /// Extract country code from phone number
  static CountryCodeInfo _extractCountryCode(String phoneNumber) {
    if (!phoneNumber.startsWith('+')) {
      return const CountryCodeInfo('', '');
    }

    String numberWithoutPlus = phoneNumber.substring(1);

    // Try to match country codes (from longest to shortest to avoid conflicts)
    List<String> sortedCountryCodes =
        _countryData.keys.toList()
          ..sort((a, b) => b.length.compareTo(a.length));

    for (String countryCode in sortedCountryCodes) {
      if (numberWithoutPlus.startsWith(countryCode)) {
        String localNumber = numberWithoutPlus.substring(countryCode.length);
        if (localNumber.isNotEmpty) {
          return CountryCodeInfo(countryCode, localNumber);
        }
      }
    }

    return const CountryCodeInfo('', '');
  }

  /// Get list of supported countries
  static List<CountryInfo> getSupportedCountries() {
    return _countryData.entries
        .map(
          (entry) => CountryInfo(
            countryCode: entry.key,
            countryName: entry.value.countryName,
            minLength: entry.value.minLength,
            maxLength: entry.value.maxLength,
          ),
        )
        .toList()
      ..sort((a, b) => a.countryName.compareTo(b.countryName));
  }

  /// Format phone number for display
  static String formatPhoneNumber(String phoneNumber) {
    ValidationResult result = isValidPhone(phoneNumber);
    if (result.isValid) {
      return result.formattedNumber ?? phoneNumber;
    }
    return phoneNumber;
  }

  /// Check if a country code is supported
  static bool isCountryCodeSupported(String countryCode) {
    return _countryData.containsKey(countryCode);
  }

  /// Get country info by country code
  static PhoneCountryInfo? getCountryInfo(String countryCode) {
    return _countryData[countryCode];
  }
}

/// Data class for phone country information
class PhoneCountryInfo {
  final String countryName;
  final int minLength;
  final int maxLength;
  final String pattern;

  const PhoneCountryInfo(
    this.countryName,
    this.minLength,
    this.maxLength,
    this.pattern,
  );
}

/// Data class for validation results
class ValidationResult {
  final bool isValid;
  final String? errorMessage;
  final String originalNumber;
  final String? formattedNumber;
  final String? countryCode;
  final String? countryName;
  final String? localNumber;

  const ValidationResult({
    required this.isValid,
    this.errorMessage,
    required this.originalNumber,
    this.formattedNumber,
    this.countryCode,
    this.countryName,
    this.localNumber,
  });

  @override
  String toString() {
    if (isValid) {
      return 'Valid: $formattedNumber ($countryName)';
    } else {
      return 'Invalid: $errorMessage';
    }
  }
}

/// Data class for country code extraction
class CountryCodeInfo {
  final String countryCode;
  final String localNumber;

  const CountryCodeInfo(this.countryCode, this.localNumber);
}

/// Data class for country information
class CountryInfo {
  final String countryCode;
  final String countryName;
  final int minLength;
  final int maxLength;

  const CountryInfo({
    required this.countryCode,
    required this.countryName,
    required this.minLength,
    required this.maxLength,
  });
}

// Usage Examples and Testing
class PhoneValidationTester {
  static void runTests() {
    List<String> testNumbers = [
      '+************', // Kenya
      '************', // Kenya without +
      '+1234567890', // US
      '1234567890', // US without +
      '+************', // UK
      '+33123456789', // France
      '+86138000000', // China (invalid pattern)
      '+8613800000000', // China (valid)
      '+************', // UAE
      '+************', // Saudi Arabia
      '+91987654321', // India
      '+12345', // Too short
      '+123456789012345678', // Too long
      '+************', // Invalid country code
      '', // Empty
      'invalid', // Non-numeric
      '+254 757 015 999', // With spaces
      '+254-757-015-999', // With dashes
      '(254) 757-015-999', // With brackets
    ];

    for (String testNumber in testNumbers) {
      ValidationResult result = PhoneNumberValidator.isValidPhone(testNumber);
      if (result.isValid) {}
    }

    List<CountryInfo> countries = PhoneNumberValidator.getSupportedCountries();
    countries.take(10).forEach((country) {});
  }
}

// Helper extension for easy validation
extension PhoneNumberExtension on String {
  ValidationResult get isValidPhoneNumber =>
      PhoneNumberValidator.isValidPhone(this);

  bool get isValidPhone => PhoneNumberValidator.isValidPhone(this).isValid;

  String get formatPhone => PhoneNumberValidator.formatPhoneNumber(this);
}
