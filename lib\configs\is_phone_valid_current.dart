// import 'package:dlibphonenumber/dlibphonenumber.dart';

// class PhoneNumberValidator {
//   static final PhoneNumberUtil _phoneUtil = PhoneNumberUtil.instance;

//   /// Main validation function that handles phone numbers with or without '+'
//   static ValidationResult isValidPhone(String phoneNumber) {
//     try {
//       if (phoneNumber.isEmpty) {
//         return ValidationResult(
//           isValid: false,
//           errorMessage: 'Phone number cannot be empty',
//           originalNumber: phoneNumber,
//         );
//       }

//       // Clean the input
//       String cleanedNumber = _cleanPhoneNumber(phoneNumber);

//       // Try to parse the number
//       PhoneNumber? parsedNumber;
//       String? detectedRegion;

//       try {
//         // First attempt: Parse with detected region from country code
//         if (cleanedNumber.startsWith('+')) {
//           parsedNumber = _phoneUtil.parse(cleanedNumber, '');
//           detectedRegion = _phoneUtil.getRegionCodeForNumber(parsedNumber);
//         } else {
//           // If no '+', try to parse assuming it has country code
//           parsedNumber = _phoneUtil.parse('+$cleanedNumber', '');
//           detectedRegion = _phoneUtil.getRegionCodeForNumber(parsedNumber);
//         }
//       } catch (e) {
//         return ValidationResult(
//           isValid: false,
//           errorMessage: 'Invalid phone number format: ${e.toString()}',
//           originalNumber: phoneNumber,
//         );
//       }

//       if (parsedNumber == null || detectedRegion == null) {
//         return ValidationResult(
//           isValid: false,
//           errorMessage: 'Could not parse phone number',
//           originalNumber: phoneNumber,
//         );
//       }

//       // Validate the parsed number
//       bool isValid = _phoneUtil.isValidNumber(parsedNumber);

//       if (!isValid) {
//         return ValidationResult(
//           isValid: false,
//           errorMessage: 'Invalid phone number for the detected region',
//           originalNumber: phoneNumber,
//           formattedNumber: _phoneUtil.format(
//             parsedNumber,
//             PhoneNumberFormat.e164,
//           ),
//           countryCode: parsedNumber.countryCode.toString(),
//           countryName: _getCountryName(detectedRegion),
//         );
//       }

//       // Get additional information
//       String formattedNumber = _phoneUtil.format(
//         parsedNumber,
//         PhoneNumberFormat.e164,
//       );
//       String nationalNumber = _phoneUtil.format(
//         parsedNumber,
//         PhoneNumberFormat.national,
//       );
//       PhoneNumberType numberType = _phoneUtil.getNumberType(parsedNumber);

//       // Get geographical and carrier info (optional - may not always be available)
//       String? territory;
//       String? carrier;
//       List<String>? timezones;

//       try {
//         territory = PhoneNumberOfflineGeocoder.instance
//             .getDescriptionForNumber(parsedNumber, Locale.english);
//         if (territory.isEmpty) territory = null;
//       } catch (e) {
//         territory = null;
//       }

//       try {
//         carrier = PhoneNumberToCarrierMapper.instance
//             .getNameForNumber(parsedNumber, Locale.english);
//         if (carrier.isEmpty) carrier = null;
//       } catch (e) {
//         carrier = null;
//       }

//       try {
//         timezones = PhoneNumberToTimeZonesMapper.instance
//             .getTimeZonesForNumber(parsedNumber);
//         if (timezones.isEmpty) timezones = null;
//       } catch (e) {
//         timezones = null;
//       }

//       return ValidationResult(
//         isValid: true,
//         originalNumber: phoneNumber,
//         formattedNumber: formattedNumber,
//         countryCode: parsedNumber.countryCode.toString(),
//         countryName: _getCountryName(detectedRegion),
//         localNumber: nationalNumber,
//         regionCode: detectedRegion,
//         numberType: numberType,
//         territory: territory,
//         carrier: carrier,
//         timezones: timezones,
//       );
//     } catch (e) {
//       return ValidationResult(
//         isValid: false,
//         errorMessage: 'Unexpected error during validation: ${e.toString()}',
//         originalNumber: phoneNumber,
//       );
//     }
//   }

//   /// Clean phone number by removing spaces, dashes, brackets, and other formatting
//   static String _cleanPhoneNumber(String phoneNumber) {
//     return phoneNumber
//         .replaceAll(RegExp(r'[\s\-\(\)\[\]\.]+'), '')
//         .replaceAll(RegExp(r'[^\d\+]'), '');
//   }

//   /// Get country name from region code
//   static String _getCountryName(String regionCode) {
//     // Map of common region codes to country names
//     const Map<String, String> regionNames = {
//       'US': 'United States',
//       'CA': 'Canada',
//       'GB': 'United Kingdom',
//       'FR': 'France',
//       'DE': 'Germany',
//       'IT': 'Italy',
//       'ES': 'Spain',
//       'NL': 'Netherlands',
//       'BE': 'Belgium',
//       'CH': 'Switzerland',
//       'AT': 'Austria',
//       'DK': 'Denmark',
//       'SE': 'Sweden',
//       'NO': 'Norway',
//       'PL': 'Poland',
//       'PT': 'Portugal',
//       'IE': 'Ireland',
//       'FI': 'Finland',
//       'EE': 'Estonia',
//       'LV': 'Latvia',
//       'LT': 'Lithuania',
//       'CN': 'China',
//       'JP': 'Japan',
//       'KR': 'South Korea',
//       'IN': 'India',
//       'SG': 'Singapore',
//       'MY': 'Malaysia',
//       'ID': 'Indonesia',
//       'PH': 'Philippines',
//       'TH': 'Thailand',
//       'VN': 'Vietnam',
//       'HK': 'Hong Kong',
//       'MO': 'Macau',
//       'TW': 'Taiwan',
//       'AU': 'Australia',
//       'NZ': 'New Zealand',
//       'ZA': 'South Africa',
//       'NG': 'Nigeria',
//       'KE': 'Kenya',
//       'BI': 'Burundi',
//       'TZ': 'Tanzania',
//       'UG': 'Uganda',
//       'RW': 'Rwanda',
//       'ET': 'Ethiopia',
//       'MA': 'Morocco',
//       'DZ': 'Algeria',
//       'TN': 'Tunisia',
//       'LY': 'Libya',
//       'GM': 'Gambia',
//       'SN': 'Senegal',
//       'ML': 'Mali',
//       'GN': 'Guinea',
//       'CI': 'Côte d\'Ivoire',
//       'BF': 'Burkina Faso',
//       'NE': 'Niger',
//       'TG': 'Togo',
//       'BJ': 'Benin',
//       'MU': 'Mauritius',
//       'SL': 'Sierra Leone',
//       'GH': 'Ghana',
//       'TD': 'Chad',
//       'CF': 'Central African Republic',
//       'CM': 'Cameroon',
//       'CV': 'Cape Verde',
//       'ST': 'São Tomé and Príncipe',
//       'GQ': 'Equatorial Guinea',
//       'GA': 'Gabon',
//       'CG': 'Republic of the Congo',
//       'CD': 'Democratic Republic of the Congo',
//       'AO': 'Angola',
//       'GW': 'Guinea-Bissau',
//       'IO': 'British Indian Ocean Territory',
//       'SC': 'Seychelles',
//       'SD': 'Sudan',
//       'SO': 'Somalia',
//       'DJ': 'Djibouti',
//       'MZ': 'Mozambique',
//       'ZM': 'Zambia',
//       'MG': 'Madagascar',
//       'RE': 'Réunion',
//       'ZW': 'Zimbabwe',
//       'NA': 'Namibia',
//       'MW': 'Malawi',
//       'LS': 'Lesotho',
//       'BW': 'Botswana',
//       'SZ': 'Eswatini',
//       'KM': 'Comoros',
//       'AE': 'United Arab Emirates',
//       'SA': 'Saudi Arabia',
//       'QA': 'Qatar',
//       'KW': 'Kuwait',
//       'OM': 'Oman',
//       'BH': 'Bahrain',
//       'IQ': 'Iraq',
//       'SY': 'Syria',
//       'JO': 'Jordan',
//       'LB': 'Lebanon',
//       'IL': 'Israel',
//       'PS': 'Palestine',
//       'IR': 'Iran',
//       'TR': 'Turkey',
//       'BR': 'Brazil',
//       'AR': 'Argentina',
//       'CL': 'Chile',
//       'CO': 'Colombia',
//       'VE': 'Venezuela',
//       'PE': 'Peru',
//       'EC': 'Ecuador',
//       'PY': 'Paraguay',
//       'UY': 'Uruguay',
//       'BO': 'Bolivia',
//       'SR': 'Suriname',
//       'GY': 'Guyana',
//       'GF': 'French Guiana',
//       'MX': 'Mexico',
//       'CU': 'Cuba',
//       'PA': 'Panama',
//       'CR': 'Costa Rica',
//       'NI': 'Nicaragua',
//       'HN': 'Honduras',
//       'SV': 'El Salvador',
//       'GT': 'Guatemala',
//       'BZ': 'Belize',
//       'RU': 'Russia',
//       'KZ': 'Kazakhstan',
//       'UA': 'Ukraine',
//       'BY': 'Belarus',
//       'AM': 'Armenia',
//       'GE': 'Georgia',
//       'AZ': 'Azerbaijan',
//       'TM': 'Turkmenistan',
//       'TJ': 'Tajikistan',
//       'KG': 'Kyrgyzstan',
//       'UZ': 'Uzbekistan',
//     };

//     return regionNames[regionCode] ?? regionCode;
//   }

//   /// Get list of supported countries
//   static List<CountryInfo> getSupportedCountries() {
//     final List<CountryInfo> countries = [];
//     final List<String> supportedRegions = _phoneUtil.supportedRegions;

//     for (String region in supportedRegions) {
//       try {
//         PhoneNumber? example = _phoneUtil.getExampleNumber(region);
//         if (example != null) {
//           countries.add(CountryInfo(
//             countryCode: example.countryCode.toString(),
//             countryName: _getCountryName(region),
//             regionCode: region,
//           ));
//         }
//       } catch (e) {
//         // Skip regions that don't have examples
//       }
//     }

//     return countries..sort((a, b) => a.countryName.compareTo(b.countryName));
//   }

//   /// Format phone number for display (E164 format)
//   static String formatPhoneNumber(String phoneNumber) {
//     ValidationResult result = isValidPhone(phoneNumber);
//     if (result.isValid) {
//       return result.formattedNumber ?? phoneNumber;
//     }
//     return phoneNumber;
//   }

//   /// Format phone number in international format
//   static String? formatInternational(String phoneNumber) {
//     try {
//       String cleaned = _cleanPhoneNumber(phoneNumber);
//       if (!cleaned.startsWith('+')) cleaned = '+$cleaned';
//       PhoneNumber parsed = _phoneUtil.parse(cleaned, '');
//       return _phoneUtil.format(parsed, PhoneNumberFormat.international);
//     } catch (e) {
//       return null;
//     }
//   }

//   /// Format phone number in national format
//   static String? formatNational(String phoneNumber) {
//     try {
//       String cleaned = _cleanPhoneNumber(phoneNumber);
//       if (!cleaned.startsWith('+')) cleaned = '+$cleaned';
//       PhoneNumber parsed = _phoneUtil.parse(cleaned, '');
//       return _phoneUtil.format(parsed, PhoneNumberFormat.national);
//     } catch (e) {
//       return null;
//     }
//   }

//   /// Check if a country code is supported
//   static bool isCountryCodeSupported(String countryCode) {
//     try {
//       // Try to get an example number for validation
//       for (String region in _phoneUtil.supportedRegions) {
//         PhoneNumber? example = _phoneUtil.getExampleNumber(region);
//         if (example?.countryCode.toString() == countryCode) {
//           return true;
//         }
//       }
//       return false;
//     } catch (e) {
//       return false;
//     }
//   }

//   /// Extract phone numbers from text
//   static List<ValidationResult> extractPhoneNumbers(
//     String text,
//     String defaultRegion,
//   ) {
//     try {
//       Iterable<PhoneNumberMatch> matches =
//           _phoneUtil.findNumbers(text, defaultRegion);
//       return matches.map((match) {
//         String extracted = _phoneUtil.format(
//           match.number,
//           PhoneNumberFormat.e164,
//         );
//         return isValidPhone(extracted);
//       }).toList();
//     } catch (e) {
//       return [];
//     }
//   }

//   /// Get example number for a region
//   static String? getExampleNumber(String regionCode) {
//     try {
//       PhoneNumber? example = _phoneUtil.getExampleNumber(regionCode);
//       if (example != null) {
//         return _phoneUtil.format(example, PhoneNumberFormat.e164);
//       }
//       return null;
//     } catch (e) {
//       return null;
//     }
//   }
// }

// /// Data class for validation results
// class ValidationResult {
//   final bool isValid;
//   final String? errorMessage;
//   final String originalNumber;
//   final String? formattedNumber;
//   final String? countryCode;
//   final String? countryName;
//   final String? localNumber;
//   final String? regionCode;
//   final PhoneNumberType? numberType;
//   final String? territory;
//   final String? carrier;
//   final List<String>? timezones;

//   const ValidationResult({
//     required this.isValid,
//     this.errorMessage,
//     required this.originalNumber,
//     this.formattedNumber,
//     this.countryCode,
//     this.countryName,
//     this.localNumber,
//     this.regionCode,
//     this.numberType,
//     this.territory,
//     this.carrier,
//     this.timezones,
//   });

//   @override
//   String toString() {
//     if (isValid) {
//       String result = 'Valid: $formattedNumber ($countryName)';
//       if (numberType != null) result += ' - Type: ${_getNumberTypeName()}';
//       if (territory != null) result += ' - Territory: $territory';
//       if (carrier != null) result += ' - Carrier: $carrier';
//       if (timezones != null && timezones!.isNotEmpty) {
//         result += ' - Timezone: ${timezones!.first}';
//       }
//       return result;
//     } else {
//       return 'Invalid: $errorMessage';
//     }
//   }

//   String _getNumberTypeName() {
//     switch (numberType) {
//       case PhoneNumberType.fixedLine:
//         return 'Fixed Line';
//       case PhoneNumberType.mobile:
//         return 'Mobile';
//       case PhoneNumberType.fixedLineOrMobile:
//         return 'Fixed Line or Mobile';
//       case PhoneNumberType.tollFree:
//         return 'Toll Free';
//       case PhoneNumberType.premiumRate:
//         return 'Premium Rate';
//       case PhoneNumberType.sharedCost:
//         return 'Shared Cost';
//       case PhoneNumberType.voip:
//         return 'VoIP';
//       case PhoneNumberType.personalNumber:
//         return 'Personal Number';
//       case PhoneNumberType.pager:
//         return 'Pager';
//       case PhoneNumberType.uan:
//         return 'UAN';
//       case PhoneNumberType.voicemail:
//         return 'Voicemail';
//       default:
//         return 'Unknown';
//     }
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'isValid': isValid,
//       'errorMessage': errorMessage,
//       'originalNumber': originalNumber,
//       'formattedNumber': formattedNumber,
//       'countryCode': countryCode,
//       'countryName': countryName,
//       'localNumber': localNumber,
//       'regionCode': regionCode,
//       'numberType': _getNumberTypeName(),
//       'territory': territory,
//       'carrier': carrier,
//       'timezones': timezones,
//     };
//   }
// }

// /// Data class for country information
// class CountryInfo {
//   final String countryCode;
//   final String countryName;
//   final String regionCode;

//   const CountryInfo({
//     required this.countryCode,
//     required this.countryName,
//     required this.regionCode,
//   });

//   @override
//   String toString() => '$countryName (+$countryCode)';
// }

// // Usage Examples and Testing
// class PhoneValidationTester {
//   static void runTests() {
//     print('=== Phone Number Validation Tests ===\n');

//     List<String> testNumbers = [
//       '+************', // Kenya
//       '************', // Kenya without +
//       '+12345678901', // US
//       '12345678901', // US without +
//       '+************', // UK
//       '+33123456789', // France
//       '+8613800000000', // China
//       '+************', // UAE
//       '+************', // Saudi Arabia
//       '+************', // India
//       '+12345', // Too short
//       '+123456789012345678', // Too long
//       '+************', // Invalid country code
//       '', // Empty
//       'invalid', // Non-numeric
//       '+254 757 015 999', // With spaces
//       '+254-757-015-999', // With dashes
//       '(254) 757-015-999', // With brackets
//     ];

//     for (String testNumber in testNumbers) {
//       ValidationResult result = PhoneNumberValidator.isValidPhone(testNumber);
//       print('Testing: "$testNumber"');
//       print('Result: ${result.toString()}');
//       if (result.isValid) {
//         print('E164: ${result.formattedNumber}');
//         print('National: ${result.localNumber}');
//         String? intl = PhoneNumberValidator.formatInternational(testNumber);
//         if (intl != null) print('International: $intl');
//       }
//       print('---');
//     }

//     // Test extracting phone numbers from text
//     print('\n=== Extract Phone Numbers from Text ===\n');
//     String text = 'Call me on 0757015999 or +254712345678';
//     List<ValidationResult> extracted =
//         PhoneNumberValidator.extractPhoneNumbers(text, 'KE');
//     print('Text: "$text"');
//     print('Found ${extracted.length} phone numbers:');
//     for (var result in extracted) {
//       print('  ${result.formattedNumber}');
//     }

//     // Test getting supported countries
//     print('\n=== Sample Supported Countries ===\n');
//     List<CountryInfo> countries =
//         PhoneNumberValidator.getSupportedCountries();
//     print('Total supported countries: ${countries.length}');
//     print('First 10 countries:');
//     countries.take(10).forEach((country) {
//       print('  ${country.toString()}');
//       String? example =
//           PhoneNumberValidator.getExampleNumber(country.regionCode);
//       if (example != null) {
//         print('    Example: $example');
//       }
//     });
//   }
// }

// // Helper extension for easy validation
// extension PhoneNumberExtension on String {
//   ValidationResult get isValidPhoneNumber =>
//       PhoneNumberValidator.isValidPhone(this);

//   bool get isValidPhone => PhoneNumberValidator.isValidPhone(this).isValid;

//   String get formatPhone => PhoneNumberValidator.formatPhoneNumber(this);

//   String? get formatInternational =>
//       PhoneNumberValidator.formatInternational(this);

//   String? get formatNational => PhoneNumberValidator.formatNational(this);
// }