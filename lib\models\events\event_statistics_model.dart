// Event Statistics Models
// Models for the event statistics API response structure

class EventStatisticsResponse {
  final bool status;
  final String message;
  final EventStatisticsData? data;

  const EventStatisticsResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory EventStatisticsResponse.fromJson(Map<String, dynamic> json) {
    return EventStatisticsResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null 
          ? EventStatisticsData.fromJson(json['data']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.toJson(),
    };
  }

  EventStatisticsResponse copyWith({
    bool? status,
    String? message,
    EventStatisticsData? data,
  }) {
    return EventStatisticsResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EventStatisticsResponse &&
          status == other.status &&
          message == other.message &&
          data == other.data;

  @override
  int get hashCode => status.hashCode ^ message.hashCode ^ data.hashCode;

  bool get isValid => status && data != null;
}

class EventStatisticsData {
  final StatisticsFilters? filters;
  final Statistics? statistics;

  const EventStatisticsData({
    this.filters,
    this.statistics,
  });

  factory EventStatisticsData.fromJson(Map<String, dynamic> json) {
    return EventStatisticsData(
      filters: json['filters'] != null 
          ? StatisticsFilters.fromJson(json['filters']) 
          : null,
      statistics: json['statistics'] != null 
          ? Statistics.fromJson(json['statistics']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'filters': filters?.toJson(),
      'statistics': statistics?.toJson(),
    };
  }

  EventStatisticsData copyWith({
    StatisticsFilters? filters,
    Statistics? statistics,
  }) {
    return EventStatisticsData(
      filters: filters ?? this.filters,
      statistics: statistics ?? this.statistics,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EventStatisticsData &&
          filters == other.filters &&
          statistics == other.statistics;

  @override
  int get hashCode => filters.hashCode ^ statistics.hashCode;
}

class StatisticsFilters {
  final String? endDate;
  final int? eventId;
  final String? startDate;

  const StatisticsFilters({
    this.endDate,
    this.eventId,
    this.startDate,
  });

  factory StatisticsFilters.fromJson(Map<String, dynamic> json) {
    return StatisticsFilters(
      endDate: json['end_date'],
      eventId: json['event_id'],
      startDate: json['start_date'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'end_date': endDate,
      'event_id': eventId,
      'start_date': startDate,
    };
  }

  StatisticsFilters copyWith({
    String? endDate,
    int? eventId,
    String? startDate,
  }) {
    return StatisticsFilters(
      endDate: endDate ?? this.endDate,
      eventId: eventId ?? this.eventId,
      startDate: startDate ?? this.startDate,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StatisticsFilters &&
          endDate == other.endDate &&
          eventId == other.eventId &&
          startDate == other.startDate;

  @override
  int get hashCode => endDate.hashCode ^ eventId.hashCode ^ startDate.hashCode;
}

class Statistics {
  final dynamic eventPerformance;
  final Overview? overview;
  final dynamic paymentMethods;
  final dynamic popularTickets;
  final RevenueBreakdown? revenueBreakdown;
  final dynamic salesTimeline;
  final dynamic ticketSales;
  final UserDemographics? userDemographics;

  const Statistics({
    this.eventPerformance,
    this.overview,
    this.paymentMethods,
    this.popularTickets,
    this.revenueBreakdown,
    this.salesTimeline,
    this.ticketSales,
    this.userDemographics,
  });

  factory Statistics.fromJson(Map<String, dynamic> json) {
    return Statistics(
      eventPerformance: json['event_performance'],
      overview: json['overview'] != null 
          ? Overview.fromJson(json['overview']) 
          : null,
      paymentMethods: json['payment_methods'],
      popularTickets: json['popular_tickets'],
      revenueBreakdown: json['revenue_breakdown'] != null 
          ? RevenueBreakdown.fromJson(json['revenue_breakdown']) 
          : null,
      salesTimeline: json['sales_timeline'],
      ticketSales: json['ticket_sales'],
      userDemographics: json['user_demographics'] != null 
          ? UserDemographics.fromJson(json['user_demographics']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'event_performance': eventPerformance,
      'overview': overview?.toJson(),
      'payment_methods': paymentMethods,
      'popular_tickets': popularTickets,
      'revenue_breakdown': revenueBreakdown?.toJson(),
      'sales_timeline': salesTimeline,
      'ticket_sales': ticketSales,
      'user_demographics': userDemographics?.toJson(),
    };
  }

  Statistics copyWith({
    dynamic eventPerformance,
    Overview? overview,
    dynamic paymentMethods,
    dynamic popularTickets,
    RevenueBreakdown? revenueBreakdown,
    dynamic salesTimeline,
    dynamic ticketSales,
    UserDemographics? userDemographics,
  }) {
    return Statistics(
      eventPerformance: eventPerformance ?? this.eventPerformance,
      overview: overview ?? this.overview,
      paymentMethods: paymentMethods ?? this.paymentMethods,
      popularTickets: popularTickets ?? this.popularTickets,
      revenueBreakdown: revenueBreakdown ?? this.revenueBreakdown,
      salesTimeline: salesTimeline ?? this.salesTimeline,
      ticketSales: ticketSales ?? this.ticketSales,
      userDemographics: userDemographics ?? this.userDemographics,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Statistics &&
          eventPerformance == other.eventPerformance &&
          overview == other.overview &&
          paymentMethods == other.paymentMethods &&
          popularTickets == other.popularTickets &&
          revenueBreakdown == other.revenueBreakdown &&
          salesTimeline == other.salesTimeline &&
          ticketSales == other.ticketSales &&
          userDemographics == other.userDemographics;

  @override
  int get hashCode =>
      eventPerformance.hashCode ^
      overview.hashCode ^
      paymentMethods.hashCode ^
      popularTickets.hashCode ^
      revenueBreakdown.hashCode ^
      salesTimeline.hashCode ^
      ticketSales.hashCode ^
      userDemographics.hashCode;
}

class Overview {
  final double averageTicketPrice;
  final int totalAttendees;
  final int totalEvents;
  final double totalRevenue;
  final int totalTicketsSold;

  const Overview({
    required this.averageTicketPrice,
    required this.totalAttendees,
    required this.totalEvents,
    required this.totalRevenue,
    required this.totalTicketsSold,
  });

  factory Overview.fromJson(Map<String, dynamic> json) {
    try {
      return Overview(
        averageTicketPrice: double.tryParse(json['average_ticket_price']?.toString() ?? '0') ?? 0.0,
        totalAttendees: int.tryParse(json['total_attendees']?.toString() ?? '0') ?? 0,
        totalEvents: int.tryParse(json['total_events']?.toString() ?? '0') ?? 0,
        totalRevenue: double.tryParse(json['total_revenue']?.toString() ?? '0') ?? 0.0,
        totalTicketsSold: int.tryParse(json['total_tickets_sold']?.toString() ?? '0') ?? 0,
      );
    } catch (e) {
      // Return default values if parsing fails
      return const Overview(
        averageTicketPrice: 0.0,
        totalAttendees: 0,
        totalEvents: 0,
        totalRevenue: 0.0,
        totalTicketsSold: 0,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'average_ticket_price': averageTicketPrice,
      'total_attendees': totalAttendees,
      'total_events': totalEvents,
      'total_revenue': totalRevenue,
      'total_tickets_sold': totalTicketsSold,
    };
  }

  Overview copyWith({
    double? averageTicketPrice,
    int? totalAttendees,
    int? totalEvents,
    double? totalRevenue,
    int? totalTicketsSold,
  }) {
    return Overview(
      averageTicketPrice: averageTicketPrice ?? this.averageTicketPrice,
      totalAttendees: totalAttendees ?? this.totalAttendees,
      totalEvents: totalEvents ?? this.totalEvents,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      totalTicketsSold: totalTicketsSold ?? this.totalTicketsSold,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Overview &&
          averageTicketPrice == other.averageTicketPrice &&
          totalAttendees == other.totalAttendees &&
          totalEvents == other.totalEvents &&
          totalRevenue == other.totalRevenue &&
          totalTicketsSold == other.totalTicketsSold;

  @override
  int get hashCode =>
      averageTicketPrice.hashCode ^
      totalAttendees.hashCode ^
      totalEvents.hashCode ^
      totalRevenue.hashCode ^
      totalTicketsSold.hashCode;

  bool get hasData => totalEvents > 0 || totalRevenue > 0 || totalTicketsSold > 0;
}

class RevenueBreakdown {
  final double averageDailyRevenue;
  final dynamic dailyBreakdown;
  final double totalRevenue;
  final int totalTransactions;

  const RevenueBreakdown({
    required this.averageDailyRevenue,
    this.dailyBreakdown,
    required this.totalRevenue,
    required this.totalTransactions,
  });

  factory RevenueBreakdown.fromJson(Map<String, dynamic> json) {
    try {
      return RevenueBreakdown(
        averageDailyRevenue: double.tryParse(json['average_daily_revenue']?.toString() ?? '0') ?? 0.0,
        dailyBreakdown: json['daily_breakdown'],
        totalRevenue: double.tryParse(json['total_revenue']?.toString() ?? '0') ?? 0.0,
        totalTransactions: int.tryParse(json['total_transactions']?.toString() ?? '0') ?? 0,
      );
    } catch (e) {
      // Return default values if parsing fails
      return const RevenueBreakdown(
        averageDailyRevenue: 0.0,
        dailyBreakdown: null,
        totalRevenue: 0.0,
        totalTransactions: 0,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'average_daily_revenue': averageDailyRevenue,
      'daily_breakdown': dailyBreakdown,
      'total_revenue': totalRevenue,
      'total_transactions': totalTransactions,
    };
  }

  RevenueBreakdown copyWith({
    double? averageDailyRevenue,
    dynamic dailyBreakdown,
    double? totalRevenue,
    int? totalTransactions,
  }) {
    return RevenueBreakdown(
      averageDailyRevenue: averageDailyRevenue ?? this.averageDailyRevenue,
      dailyBreakdown: dailyBreakdown ?? this.dailyBreakdown,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      totalTransactions: totalTransactions ?? this.totalTransactions,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RevenueBreakdown &&
          averageDailyRevenue == other.averageDailyRevenue &&
          dailyBreakdown == other.dailyBreakdown &&
          totalRevenue == other.totalRevenue &&
          totalTransactions == other.totalTransactions;

  @override
  int get hashCode =>
      averageDailyRevenue.hashCode ^
      dailyBreakdown.hashCode ^
      totalRevenue.hashCode ^
      totalTransactions.hashCode;

  bool get hasData => totalRevenue > 0 || totalTransactions > 0;
}

class UserDemographics {
  final double averageTicketsPerUser;
  final double repeatCustomerRate;
  final int repeatCustomers;
  final int uniqueCustomers;

  const UserDemographics({
    required this.averageTicketsPerUser,
    required this.repeatCustomerRate,
    required this.repeatCustomers,
    required this.uniqueCustomers,
  });

  factory UserDemographics.fromJson(Map<String, dynamic> json) {
    try {
      return UserDemographics(
        averageTicketsPerUser: double.tryParse(json['average_tickets_per_user']?.toString() ?? '0') ?? 0.0,
        repeatCustomerRate: double.tryParse(json['repeat_customer_rate']?.toString() ?? '0') ?? 0.0,
        repeatCustomers: int.tryParse(json['repeat_customers']?.toString() ?? '0') ?? 0,
        uniqueCustomers: int.tryParse(json['unique_customers']?.toString() ?? '0') ?? 0,
      );
    } catch (e) {
      // Return default values if parsing fails
      return const UserDemographics(
        averageTicketsPerUser: 0.0,
        repeatCustomerRate: 0.0,
        repeatCustomers: 0,
        uniqueCustomers: 0,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'average_tickets_per_user': averageTicketsPerUser,
      'repeat_customer_rate': repeatCustomerRate,
      'repeat_customers': repeatCustomers,
      'unique_customers': uniqueCustomers,
    };
  }

  UserDemographics copyWith({
    double? averageTicketsPerUser,
    double? repeatCustomerRate,
    int? repeatCustomers,
    int? uniqueCustomers,
  }) {
    return UserDemographics(
      averageTicketsPerUser: averageTicketsPerUser ?? this.averageTicketsPerUser,
      repeatCustomerRate: repeatCustomerRate ?? this.repeatCustomerRate,
      repeatCustomers: repeatCustomers ?? this.repeatCustomers,
      uniqueCustomers: uniqueCustomers ?? this.uniqueCustomers,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserDemographics &&
          averageTicketsPerUser == other.averageTicketsPerUser &&
          repeatCustomerRate == other.repeatCustomerRate &&
          repeatCustomers == other.repeatCustomers &&
          uniqueCustomers == other.uniqueCustomers;

  @override
  int get hashCode =>
      averageTicketsPerUser.hashCode ^
      repeatCustomerRate.hashCode ^
      repeatCustomers.hashCode ^
      uniqueCustomers.hashCode;

  bool get hasData => uniqueCustomers > 0 || repeatCustomers > 0;
}
