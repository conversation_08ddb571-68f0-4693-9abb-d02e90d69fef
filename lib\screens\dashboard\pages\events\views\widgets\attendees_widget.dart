
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/main.dart';

class AttendeesWidget extends StatelessWidget {
  final double? padding, size, textSize;
  final int count;
  const AttendeesWidget(
      {super.key, this.padding, this.size, this.textSize, required this.count});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(padding ?? 8.0),
      child: Row(
        children: [
          SizedBox(
            width: size != null ? size! * 4 + 8 : 105,
            child: Stack(
              children: [
                Positioned(
                  child: CircleAvatar(
                    radius: size ?? 25,
                    backgroundColor: Colors.purple[300],
                    child: const Icon(Icons.person),
                  ),
                ),
                Positioned(
                  left: size ?? 25,
                  child: CircleAvatar(
                    radius: size ?? 25,
                    child: const Icon(Icons.person),
                  ),
                ),
                Positioned(
                  left: size != null ? size! * 2 : 50,
                  child: CircleAvatar(
                    radius: size ?? 25,
                    backgroundColor: const Color(0xff4355b6),
                    child: Padding(
                      padding: const EdgeInsets.all(6.0),
                      child: FittedBox(
                        child: Text(
                          '+$count',
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          Text(
            'attendees'.tr,
            style: TextStyle(
                fontSize: textSize ?? 18,
                fontWeight: FontWeight.w500,
                color: const Color(0xff4355b6)),
          ),
        ],
      ),
    );
  }
}

class TabButtonWidget extends StatelessWidget {
  final bool? active;
  final String label;
  final Function()? onTap;
  const TabButtonWidget(
      {super.key, this.active, required this.label, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        decoration: BoxDecoration(
          color: active ?? false
              ? primaryColor.withOpacity(0.15)
              : isLight.value
                  ? const Color(0xfff9f9f9)
                  : null,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: active ?? false ? primaryColor : Colors.grey,
            width: 1.0,
          ),
        ),
        child: active ?? false
            ? Row(
                children: [
                  const Icon(Icons.circle, size: 12, color: Color(0xff4355b6)),
                  const SizedBox(width: 6),
                  Text(label,
                      style: TextStyle(
                        color: const Color(0xff4355b6),
                        fontSize: 16.spMin,
                        fontWeight: FontWeight.w600,
                      )),
                ],
              )
            : Text(label,
                style: TextStyle(
                  fontSize: 16.spMin,
                  fontWeight: FontWeight.w600,
                )),
      ),
    );
  }
}

