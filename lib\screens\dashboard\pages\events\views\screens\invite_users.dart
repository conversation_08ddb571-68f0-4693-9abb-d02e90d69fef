import 'package:flutter_contacts/contact.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/invite_users_controller.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:shimmer/shimmer.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';

class InviteUsersPage extends StatefulWidget {
  final int ticketId;
  final int eventId;
  const InviteUsersPage({
    super.key,
    required this.ticketId,
    required this.eventId,
  });

  @override
  State<InviteUsersPage> createState() => _InvitePageState();
}

class _InvitePageState extends State<InviteUsersPage> {
  final TextEditingController _phoneNumber = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalControllers _controller = Get.put(GlobalControllers());
  final controller = Get.put(InviteUsersController());
  @override
  void initState() {
    super.initState();
    controller.fetchInvites(widget.eventId);
    _controller.onInit();
  }

  @override
  void dispose() {
    _phoneNumber.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 20.h),
                Form(
                  key: _formKey,
                  child: CustomInternationalPhoneInput(
                    onInputChanged: (PhoneNumber number) {
                      print("${number.phoneNumber}");
                      _phoneNumber.text =
                          number.phoneNumber.toString().replaceAll("+", '');
                    },
                    controller: _phoneNumber,
                     
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'phone_number_is_required'.tr;
                      }
                      return null;
                    },  ),
                ),
                SizedBox(height: 20.h),
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      showDialog(
                          context: context,
                          builder: (BuildContext context) => InviteDetails(
                                phone: _phoneNumber.text,
                                // name: results['names'],
                                eventId: widget.eventId,
                                ticketId: widget.ticketId,
                              )).whenComplete(() {
                        _phoneNumber.clear();
                      });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.add, color: Colors.white),
                      SizedBox(width: 8.w),
                      Text(
                        'invite_user'.tr,
                        style: TextStyle(fontSize: 16.sp, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                Obx(
                  () => controller.newInvites.isEmpty
                      ? const SizedBox()
                      : Column(
                          children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                'new_event_invitees'.tr,
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            ListView.builder(
                                shrinkWrap: true,
                                itemCount: controller.newInvites.length,
                                itemBuilder: (context, index) {
                                  final user = controller.newInvites[index];
                                  return ListTile(
                                    leading: Text('${index + 1}'),
                                    title: Text(
                                        "${user.firstName} ${user.secondName}"),
                                    subtitle: Text(user.phoneNumber),
                                    trailing: IconButton(
                                        onPressed: () => controller.newInvites
                                            .removeAt(index),
                                        icon: const Icon(Icons.close)),
                                  );
                                }),
                            Obx(
                              () => MyButton(
                                showLoading: controller.sendInvites.value,
                                onClick: () {
                                  controller.inviteMember(widget.eventId,
                                      widget.ticketId, controller.newInvites);
                                },
                                label: 'send_invites'.tr,
                              ),
                            )
                          ],
                        ),
                ),
                SizedBox(height: 20.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'event_invitees'.tr,
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                  ),
                ),
                SizedBox(height: 8.h),
                Obx(() => controller.isLoadingInvitees.value
                    ? ListView.builder(
                        itemCount: 3,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              width: double.infinity,
                              height: 100.h,
                              color: Colors.white,
                            ),
                          );
                        })
                    : controller.invitedUsers.isEmpty
                        ? Center(child: Text('no_invitees_found'.tr))
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: controller.invitedUsers.length,
                            itemBuilder: (context, index) {
                              if (controller.isLoadingInvitees.value) {
                                return ListView.builder(
                                    itemCount: 3,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      return Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        child: Container(
                                          margin: const EdgeInsets.all(4),
                                          width: double.infinity,
                                          height: 100.h,
                                          color: Colors.white,
                                        ),
                                      );
                                    });
                              }
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    onTap: () {},
                                    child: Row(children: [
                                      Text('${index + 1}'),
                                      SizedBox(width: 8.w),
                                      const CircleAvatar(
                                        child: Icon(Icons.person),
                                      ),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Text(
                                                      "${controller.invitedUsers[index].firstName} ${controller.invitedUsers[index].secondName}",
                                                      style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w600)),
                                                  const Spacer(),
                                                  GestureDetector(
                                                    onTap: () {
                                                      final inviteCode =
                                                          controller
                                                              .invitedUsers[
                                                                  index]
                                                              .inviteCode;
                                                      Clipboard.setData(
                                                          ClipboardData(
                                                              text:
                                                                  inviteCode));
                                                      Get.snackbar(
                                                        'copied'.tr,
                                                        'invite_code_copied'.tr,
                                                      );
                                                    },
                                                    child: Chip(
                                                      avatar: const Icon(
                                                          Icons.copy),
                                                      label: Text(controller
                                                          .invitedUsers[index]
                                                          .inviteCode),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: 2.h),
                                              Text(
                                                  controller.invitedUsers[index]
                                                      .phoneNumber,
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w600)),
                                            ]),
                                      ),
                                    ]),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: Divider(color: Colors.grey[300]),
                                  ),
                                ],
                              );
                            })),
              ],
            ),
          ),
        ));
  }
}

class DisplayContacts extends StatelessWidget {
  final GlobalControllers controller;
  const DisplayContacts({super.key, required this.controller});
  @override
  Widget build(BuildContext context) {
    final searchController = TextEditingController();
    final filteredContacts =
        <Contact>[].obs; // Observable list for filtered contacts

    // Initialize filteredContacts with all contacts
    filteredContacts.assignAll(controller.contacts);

    return Container(
        padding: const EdgeInsets.all(8),
        height: MediaQuery.sizeOf(context).height * 3 / 4,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24), border: Border.all()),
        child: Obx(() {
          return Column(
            children: [
              Text('add_contact'.tr,
                  style:  const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 24,
                  )),
              const SizedBox(height: 8),
              CupertinoSearchTextField(
                controller: searchController,
                onChanged: (value) {
                  // Filter contacts based on search input
                  if (value.isEmpty) {
                    filteredContacts.assignAll(controller.contacts);
                  } else {
                    filteredContacts
                        .assignAll(controller.contacts.where((contact) {
                      return contact.displayName
                              .toLowerCase()
                              .contains(value.toLowerCase());
                    }).toList());
                  }
                },
              ),
              Expanded(
                child: ListView.builder(
                    itemCount: filteredContacts.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final contact = filteredContacts[index];
                      return ListTile(
                        onTap: () {
                          final results = {
                            'names': contact.displayName,
                            'phone':
                                '${contact.phones.isNotEmpty ? contact.phones[0] : ''}'
                                    .removeAllWhitespace,
                            'role': 'viewer',
                            'date_joined': DateTime.now(),
                            'avatar': contact.photo != null &&
                                    contact.photo!.isNotEmpty
                                ? contact.photo
                                : null,
                          };
                          Navigator.of(context).pop(results);
                        },
                        title: Text(contact.displayName),
                        leading:
                            contact.photo != null && contact.photo!.isNotEmpty
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(360),
                                    child: Image.memory(
                                      contact.photo!,
                                      height: 36,
                                      width: 36,
                                    ),
                                  )
                                : const CircleAvatar(
                                    radius: 18, child: Icon(Icons.person)),
                        subtitle: Text(
                            '${contact.phones.isNotEmpty ? contact.phones[0] : ''}'),
                      );
                    }),
              )
            ],
          );
        }));
  }
}

class InviteDetails extends StatelessWidget {
  final int eventId, ticketId;
  final String? phone, name;
  const InviteDetails(
      {super.key,
      required this.eventId,
      required this.ticketId,
      this.phone,
      this.name});

  @override
  Widget build(BuildContext context) {
    List<String> nameParts = name?.split(' ') ?? [];
    String firstname = nameParts.isNotEmpty ? nameParts.first : '';
    String secondname =
        nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    TextEditingController firstName = TextEditingController(text: firstname),
        email = TextEditingController(),
        secondName = TextEditingController(text: secondname),
        _phoneNumber = TextEditingController(text: phone ?? '');
    final GlobalKey<FormState> _formKey1 = GlobalKey<FormState>();
    final controller = Get.put(InviteUsersController());
    return AlertDialog(
      title: Text(phone != null ? 'user_details_for_phone'.tr.replaceAll('{phone}', phone!) : 'user_details'.tr),
      content: Form(
        key: _formKey1,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    MyTextFieldwValidator(
                      controller: firstName,
                      title: 'first_name'.tr,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'first_name_is_required'.tr;
                        }
                        return null;
                      },
                    ),
                    MyTextFieldwValidator(
                      controller: secondName,
                      title: 'second_name'.tr,
                    ),
                    if (phone == null)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CustomInternationalPhoneInput(
                          onInputChanged: (PhoneNumber number) {
                            print("${number.phoneNumber}");
                            _phoneNumber.text = number.phoneNumber
                                .toString()
                                .replaceAll("+", '');
                          },
                          controller: _phoneNumber,
                           
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'phone_number_is_required'.tr;
                            }
                            return null;
                          },
                        ),
                      ),
                    MyTextFieldwValidator(
                      controller: email,
                      keyboardType: TextInputType.emailAddress,
                      title: 'email'.tr,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'email_is_required'.tr;
                        } else if (!RegExp(r'^[^@]+@[^@]+\.[^@]+')
                            .hasMatch(value)) {
                          return 'please_enter_valid_email'.tr;
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Get.back();
          },
          child: Text('cancel'.tr),
        ),
        MyButton(
          width: 155.w,
          label: 'invite'.tr,
          onClick: () async {
            if (_formKey1.currentState!.validate()) {
              controller.newInvites.add(InviteUsersModel(
                  phoneNumber: phone ?? _phoneNumber.text,
                  firstName: firstName.text,
                  secondName: secondName.text,
                  email: email.text));
              Get.back();
            }
          },
        ),
      ],
    );
  }
}
