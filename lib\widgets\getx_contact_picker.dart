import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contact_picker_controller.dart';

enum ContactPickerMode { single, multiple }
enum ContactPickerDisplay { fullScreen, dialog }

class GetXContactPicker extends StatefulWidget {
  final ContactPickerMode mode;
  final ContactPickerDisplay display;
  final String title;
  final Function(Contact)? onSingleContactSelected;
  final Function(List<Contact>)? onMultipleContactsSelected;
  final List<Contact>? preSelectedContacts;
  final bool showSelectedContactsHeader;
  final Widget? customAppBarActions;

  const GetXContactPicker({
    super.key,
    required this.mode,
    this.display = ContactPickerDisplay.dialog,
    this.title = '',
    this.onSingleContactSelected,
    this.onMultipleContactsSelected,
    this.preSelectedContacts,
    this.showSelectedContactsHeader = true,
    this.customAppBarActions,
  });

  @override
  State<GetXContactPicker> createState() => _GetXContactPickerState();
}

class _GetXContactPickerState extends State<GetXContactPicker> {
  late ContactPickerController controller;
  late String controllerTag;

  @override
  void initState() {
    super.initState();
    controllerTag = '${widget.mode.name}_${widget.display.name}_${DateTime.now().millisecondsSinceEpoch}';
    controller = Get.put(ContactPickerController(), tag: controllerTag);
  }

  @override
  void dispose() {
    Get.delete<ContactPickerController>(tag: controllerTag);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mode = widget.mode;
    final display = widget.display;
    final title = widget.title;
    final preSelectedContacts = widget.preSelectedContacts;
    final showSelectedContactsHeader = widget.showSelectedContactsHeader;
    final customAppBarActions = widget.customAppBarActions;
    final onSingleContactSelected = widget.onSingleContactSelected;
    final onMultipleContactsSelected = widget.onMultipleContactsSelected;
    final isFullScreen = display == ContactPickerDisplay.fullScreen;
    final isDialog = display == ContactPickerDisplay.dialog;
    final isSingleMode = mode == ContactPickerMode.single;
    final controllerTag = '${mode.name}_${display.name}_${DateTime.now().millisecondsSinceEpoch}';
    final controller = Get.put(ContactPickerController(), tag: controllerTag);
    final searchController = TextEditingController();

    // Initialize pre-selected contacts if provided
    if (preSelectedContacts != null && preSelectedContacts.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.clearSelection();
        for (var contact in preSelectedContacts) {
          controller.selectContact(contact);
        }
      });
    } else {
      // Clear selection for fresh start
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.clearSelection();
      });
    }

    // Load contacts when widget is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.loadingState.value == ContactsLoadingState.initial) {
        controller.loadContacts(context);
      }
    });

    Widget content = Column(
      children: [
        if (display == ContactPickerDisplay.dialog)
          Text(
            title.isNotEmpty ? title : 'select_contact'.tr,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 24,
            ),
          ),
        if (display == ContactPickerDisplay.dialog) const SizedBox(height: 8),
        Obx(() => controller.loadingState.value == ContactsLoadingState.loaded
            ? CupertinoSearchTextField(
                controller: searchController,
                placeholder: 'search_contacts'.tr,
                onChanged: controller.filterContacts,
              )
            : const SizedBox.shrink()),
        const SizedBox(height: 8),
        if (showSelectedContactsHeader) _buildSelectedContactsHeader(controller),
        Expanded(child: _buildContactsContent(controller)),
        if (mode == ContactPickerMode.multiple)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: () {
                onMultipleContactsSelected?.call(controller.selectedContacts);
                if (display == ContactPickerDisplay.dialog) {
                  Navigator.of(context).pop(controller.selectedContacts);
                } else {
                  Get.back(result: controller.selectedContacts);
                }
              },
              child: Obx(() => Text(
                  '${'select'.tr} (${controller.selectedContacts.length})')),
            ),
          ),
      ],
    );

    if (display == ContactPickerDisplay.dialog) {
      return Container(
        padding: const EdgeInsets.all(8),
        height: MediaQuery.of(context).size.height * 3 / 4,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          border: Border.all(),
        ),
        child: content,
      );
    } else {
      return Scaffold(
        appBar: AppBar(
          title: Text(title.isNotEmpty ? title : 'select_contact'.tr),
          actions: customAppBarActions != null
              ? [customAppBarActions]
              : [
                  IconButton(
                    onPressed: () {
                      showSearch(
                        context: context,
                        delegate: ContactSearchDelegate(
                          controller.allContacts,
                          (contact) => _selectContact(contact, controller, context),
                        ),
                      );
                    },
                    icon: const Icon(Icons.search),
                  ),
                ],
        ),
        body: content,
        floatingActionButton: mode == ContactPickerMode.multiple
            ? FloatingActionButton(
                onPressed: () {
                  onMultipleContactsSelected?.call(controller.selectedContacts);
                  if (display == ContactPickerDisplay.dialog) {
                    Navigator.of(context).pop(controller.selectedContacts);
                  } else {
                    Get.back(result: controller.selectedContacts);
                  }
                },
                child: const Icon(Icons.check),
              )
            : null,
      );
    }
  }

  Widget _buildSelectedContactsHeader(ContactPickerController controller) {
    if (widget.mode == ContactPickerMode.single) {
      return const SizedBox.shrink();
    }

    return Obx(() => controller.selectedContacts.isNotEmpty
        ? Column(
            children: [
              SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.selectedContacts.length,
                  itemBuilder: (context, index) {
                    final contact = controller.selectedContacts[index];
                    return Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Column(
                        children: [
                          Stack(
                            children: [
                              CircleAvatar(
                                backgroundImage: contact.photo != null
                                    ? MemoryImage(contact.photo!)
                                    : null,
                                radius: 20,
                                child: contact.photo == null
                                    ? Text(contact.displayName.isNotEmpty
                                        ? contact.displayName[0]
                                        : '')
                                    : null,
                              ),
                              Positioned(
                                right: -5,
                                top: -5,
                                child: InkWell(
                                  onTap: () => controller.removeContact(contact),
                                  child: const Icon(
                                    Icons.cancel,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            width: 50,
                            child: Text(
                              contact.displayName,
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 10),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              const Divider(color: Colors.grey, height: 1),
            ],
          )
        : const SizedBox.shrink());
  }

  Widget _buildContactsContent(ContactPickerController controller) {
    return Obx(() {
      switch (controller.loadingState.value) {
        case ContactsLoadingState.initial:
        case ContactsLoadingState.loading:
          return _buildLoadingState();
        case ContactsLoadingState.loaded:
          return _buildContactsList(controller);
        case ContactsLoadingState.permissionDenied:
          return _buildPermissionDeniedState(controller);
        case ContactsLoadingState.error:
          return _buildErrorState(controller);
      }
    });
  }

  Widget _buildLoadingState() {
    return   Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'loading_contacts'.tr,
            style: const TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionDeniedState(ContactPickerController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.contacts_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'contacts_access_required'.tr,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'contacts_access_description'.tr,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => controller.retryLoadContacts(Get.context!),
              icon: const Icon(Icons.refresh),
              label: Text('grant_access'.tr),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ContactPickerController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'error_loading_contacts'.tr,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Obx(() => Text(
              controller.errorMessage.value.isNotEmpty
                  ? controller.errorMessage.value
                  : 'something_went_wrong_loading_contacts'.tr,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            )),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => controller.retryLoadContacts(Get.context!),
              icon: const Icon(Icons.refresh),
              label: Text('try_again'.tr),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactsList(ContactPickerController controller) {
    return ListView.builder(
      itemCount: controller.filteredContacts.length,
      itemBuilder: (context, index) {
        final contact = controller.filteredContacts[index];
        final isSelected = controller.isSelected(contact);

        return ListTile(
          leading: contact.photo != null && contact.photo!.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(360),
                  child: Image.memory(
                    contact.photo!,
                    height: 36,
                    width: 36,
                    fit: BoxFit.cover,
                  ),
                )
              : const CircleAvatar(
                  radius: 18,
                  child: Icon(Icons.person),
                ),
          title: Text(contact.displayName),
          subtitle: Text(
            contact.phones.isNotEmpty
                ? contact.phones.first.number
                : 'no_number'.tr,
          ),
          trailing: isSelected
              ? const Icon(Icons.check, color: Colors.green)
              : null,
          onTap: () => _selectContact(contact, controller, context),
        );
      },
    );
  }

  void _selectContact(Contact contact, ContactPickerController controller, BuildContext context) {
    final mode = widget.mode;
    final display = widget.display;
    final onSingleContactSelected = widget.onSingleContactSelected;
    if (mode == ContactPickerMode.single) {
      // For single mode, briefly show selection then return
      controller.clearSelection();
      controller.selectContact(contact);
      onSingleContactSelected?.call(contact);

      // Add a small delay to show the selection feedback
      Future.delayed(const Duration(milliseconds: 200), () {
        if (display == ContactPickerDisplay.dialog) {
          Navigator.of(context).pop(contact);
        } else {
          Get.back(result: contact);
        }
      });
    } else {
      if (controller.isSelected(contact)) {
        controller.removeContact(contact);
      } else {
        controller.selectContact(contact);
      }
    }
  }
}

class ContactSearchDelegate extends SearchDelegate<Contact> {
  final List<Contact> contacts;
  final Function(Contact) onContactSelected;

  ContactSearchDelegate(this.contacts, this.onContactSelected);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, Contact()),
    );
  }

  @override
  Widget buildResults(BuildContext context) => buildSearchResults(context);

  @override
  Widget buildSuggestions(BuildContext context) => buildSearchResults(context);

  Widget buildSearchResults(BuildContext context) {
    final filteredContacts = contacts
        .where((contact) =>
            contact.displayName.toLowerCase().contains(query.toLowerCase()))
        .toList();

    return ListView.builder(
      itemCount: filteredContacts.length,
      itemBuilder: (context, index) {
        final contact = filteredContacts[index];
        return ListTile(
          title: Text(contact.displayName),
          subtitle: Text(
            contact.phones.isNotEmpty ? contact.phones.first.number : 'no_number'.tr,
          ),
          onTap: () {
            close(context, contact);
            onContactSelected(contact);
          },
        );
      },
    );
  }
}