import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:onekitty/services/http_service.dart';

class TestController extends GetxController {
  final _isLoading = false.obs;
  final _data = Rxn<dynamic>();
  final _url = "kitty/get_kitty_all/7621".obs;
  final _searchQuery = ''.obs;
  final _expandedNodes = <String>{}.obs;
  final _filteredData = Rxn<dynamic>();
  final _searchHistory = <String>[].obs;
  final _isDarkMode = false.obs;
  
  bool get isLoading => _isLoading.value;
  dynamic get data => _data.value;
  dynamic get filteredData => _filteredData.value ?? _data.value;
  String get url => _url.value;
  String get searchQuery => _searchQuery.value;
  Set<String> get expandedNodes => _expandedNodes.value;
  List<String> get searchHistory => _searchHistory;
  bool get isDarkMode => _isDarkMode.value;
  
  @override
  void onInit() {
    super.onInit();
    // Auto-fetch data on initialization
    ever(_searchQuery, _performSearch);
  }
  
  void toggleDarkMode() => _isDarkMode.toggle();
  
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    if (query.isNotEmpty && !_searchHistory.contains(query)) {
      _searchHistory.insert(0, query);
      if (_searchHistory.length > 10) {
        _searchHistory.removeRange(10, _searchHistory.length);
      }
    }
  }
  
  void _performSearch(String query) {
    if (query.isEmpty) {
      _filteredData.value = null;
      return;
    }
    
    if (_data.value != null) {
      _filteredData.value = _filterData(_data.value, query.toLowerCase());
    }
  }
  
  dynamic _filterData(dynamic data, String query) {
    if (data is Map) {
      Map<String, dynamic> filteredMap = {};
      for (var entry in data.entries) {
        if (entry.key.toLowerCase().contains(query) ||
            entry.value.toString().toLowerCase().contains(query) ||
            _containsSearchTerm(entry.value, query)) {
          filteredMap[entry.key] = entry.value;
        }
      }
      return filteredMap.isNotEmpty ? filteredMap : null;
    } else if (data is List) {
      List filteredList = [];
      for (var item in data) {
        if (item.toString().toLowerCase().contains(query) ||
            _containsSearchTerm(item, query)) {
          filteredList.add(item);
        }
      }
      return filteredList.isNotEmpty ? filteredList : null;
    }
    return data.toString().toLowerCase().contains(query) ? data : null;
  }
  
  bool _containsSearchTerm(dynamic data, String query) {
    if (data is Map) {
      return data.entries.any((entry) =>
          entry.key.toLowerCase().contains(query) ||
          _containsSearchTerm(entry.value, query));
    } else if (data is List) {
      return data.any((item) => _containsSearchTerm(item, query));
    }
    return data.toString().toLowerCase().contains(query);
  }
  
  void toggleNodeExpansion(String nodeKey) {
    if (_expandedNodes.contains(nodeKey)) {
      _expandedNodes.remove(nodeKey);
    } else {
      _expandedNodes.add(nodeKey);
    }
  }
  
  void expandAll() {
    _expandAllNodes(_data.value, '');
  }
  
  void collapseAll() {
    _expandedNodes.clear();
  }
  
  void _expandAllNodes(dynamic data, String path) {
    if (data is Map) {
      for (var entry in data.entries) {
        String nodePath = path.isEmpty ? entry.key : '$path.${entry.key}';
        if (entry.value is Map || entry.value is List) {
          _expandedNodes.add(nodePath);
          _expandAllNodes(entry.value, nodePath);
        }
      }
    } else if (data is List) {
      for (int i = 0; i < data.length; i++) {
        String nodePath = path.isEmpty ? '[$i]' : '$path[$i]';
        if (data[i] is Map || data[i] is List) {
          _expandedNodes.add(nodePath);
          _expandAllNodes(data[i], nodePath);
        }
      }
    }
  }
  
  void setUrl(String newUrl) => _url.value = newUrl;
  
  Future<void> fetchData() async {
    _isLoading.value = true;
    try {
      final httpService = Get.find<HttpService>();
      final resp = await httpService.request(url: _url.value, method: Method.GET);
      _data.value = resp.data;
      _filteredData.value = null;
      Get.snackbar(
        'Success', 
        'Data fetched successfully',
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'Error', 
        'Failed to fetch data: ${e.toString()}',
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  void copyToClipboard() {
    if (_data.value != null) {
      final jsonString = const JsonEncoder.withIndent('  ').convert(_data.value);
      Clipboard.setData(ClipboardData(text: jsonString));
      Get.snackbar(
        'Copied', 
        'JSON response copied to clipboard',
        backgroundColor: Colors.blue.withOpacity(0.8),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }
  
  void clearData() {
    _data.value = null;
    _filteredData.value = null;
    _searchQuery.value = '';
    _expandedNodes.clear();
  }
}

class JsonViewer extends StatelessWidget {
  final dynamic data;
  final String searchQuery;
  final TestController controller;
  final String path;
  
  const JsonViewer({
    super.key, 
    required this.data, 
    required this.controller,
    this.searchQuery = '',
    this.path = '',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: controller.isDarkMode
              ? [Colors.grey[900]!, Colors.grey[800]!]
              : [Colors.grey[50]!, Colors.white],
        ),
      ),
      child: InteractiveViewer(
        panEnabled: true,
        scaleEnabled: true,
        minScale: 0.5,
        maxScale: 3.0,
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          physics: const BouncingScrollPhysics(),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            child: IntrinsicWidth(
              child: Container(
                padding: const EdgeInsets.all(16),
                constraints: const BoxConstraints(minWidth: 800),
                child: _buildJsonNode(data, 0, path),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildJsonNode(dynamic value, int depth, String nodePath) {
    if (value is Map) {
      return _buildMapNode(value, depth, nodePath);
    } else if (value is List) {
      return _buildListNode(value, depth, nodePath);
    } else {
      return _buildValueNode(value, depth);
    }
  }

  Widget _buildMapNode(Map map, int depth, String nodePath) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: controller.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: map.entries.map((entry) {
          final currentPath = nodePath.isEmpty ? entry.key : '$nodePath.${entry.key}';
          final isPrimitive = entry.value is! Map && entry.value is! List;
          final isExpanded = controller.expandedNodes.contains(currentPath);
          
          return Container(
            margin: EdgeInsets.only(left: depth * 20.0, bottom: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: _getNodeBackgroundColor(depth, isPrimitive),
            ),
            child: isPrimitive
                ? _buildKeyValuePair(entry.key, entry.value, depth)
                : AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    child: Theme(
                      data: Theme.of(Get.context!).copyWith(
                        dividerColor: Colors.transparent,
                      ),
                      child: ExpansionTile(
                        key: ValueKey(currentPath),
                        initiallyExpanded: isExpanded,
                        onExpansionChanged: (expanded) => controller.toggleNodeExpansion(currentPath),
                        backgroundColor: _getExpandedBackgroundColor(entry.value),
                        collapsedBackgroundColor: _getCollapsedBackgroundColor(entry.value),
                        iconColor: _getIconColor(entry.value),
                        collapsedIconColor: _getIconColor(entry.value),
                        tilePadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        childrenPadding: const EdgeInsets.only(left: 16, bottom: 8),
                        leading: _buildTypeIcon(entry.value),
                        title: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '"${entry.key}"',
                              style: TextStyle(
                                fontFamily: 'Fira Code',
                                color: controller.isDarkMode ? Colors.lightBlue[300] : Colors.blue[700],
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 8),
                            _buildTypeChip(entry.value),
                          ],
                        ),
                        children: [
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: _buildJsonNode(entry.value, depth + 1, currentPath),
                          ),
                        ],
                      ),
                    ),
                  ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildListNode(List list, int depth, String nodePath) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: controller.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Column(
        children: list.asMap().entries.map((entry) {
          final currentPath = nodePath.isEmpty ? '[${entry.key}]' : '$nodePath[${entry.key}]';
          final isPrimitive = entry.value is! Map && entry.value is! List;
          final isExpanded = controller.expandedNodes.contains(currentPath);
          
          return Container(
            margin: EdgeInsets.only(left: depth * 20.0, bottom: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: _getNodeBackgroundColor(depth, isPrimitive),
            ),
            child: isPrimitive
                ? _buildIndexValuePair(entry.key, entry.value, depth)
                : AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    child: Theme(
                      data: Theme.of(Get.context!).copyWith(
                        dividerColor: Colors.transparent,
                      ),
                      child: ExpansionTile(
                        key: ValueKey(currentPath),
                        initiallyExpanded: isExpanded,
                        onExpansionChanged: (expanded) => controller.toggleNodeExpansion(currentPath),
                        backgroundColor: _getExpandedBackgroundColor(entry.value),
                        collapsedBackgroundColor: _getCollapsedBackgroundColor(entry.value),
                        iconColor: _getIconColor(entry.value),
                        collapsedIconColor: _getIconColor(entry.value),
                        tilePadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        childrenPadding: const EdgeInsets.only(left: 16, bottom: 8),
                        leading: _buildTypeIcon(entry.value),
                        title: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '[${entry.key}]',
                              style: TextStyle(
                                fontFamily: 'Fira Code',
                                color: controller.isDarkMode ? Colors.green[300] : Colors.green[700],
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 8),
                            _buildTypeChip(entry.value),
                          ],
                        ),
                        children: [
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: _buildJsonNode(entry.value, depth + 1, currentPath),
                          ),
                        ],
                      ),
                    ),
                  ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTypeIcon(dynamic value) {
    IconData icon;
    Color color;
    
    if (value is Map) {
      icon = Icons.data_object_rounded;
      color = controller.isDarkMode ? Colors.blue[300]! : Colors.blue[600]!;
    } else if (value is List) {
      icon = Icons.data_array_rounded;
      color = controller.isDarkMode ? Colors.green[300]! : Colors.green[600]!;
    } else {
      icon = Icons.code;
      color = controller.isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
    }
    
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(icon, size: 16, color: color),
    );
  }

  Widget _buildTypeChip(dynamic value) {
    String label;
    Color color;
    
    if (value is Map) {
      label = '${value.length} keys';
      color = controller.isDarkMode ? Colors.blue[300]! : Colors.blue[600]!;
    } else if (value is List) {
      label = '${value.length} items';
      color = controller.isDarkMode ? Colors.green[300]! : Colors.green[600]!;
    } else {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getNodeBackgroundColor(int depth, bool isPrimitive) {
    if (!isPrimitive) return Colors.transparent;
    
    final colors = controller.isDarkMode
        ? [Colors.grey[850], Colors.grey[800], Colors.grey[750]]
        : [Colors.grey[100], Colors.grey[50], Colors.white];
    
    return colors[depth % colors.length]!;
  }

  Color _getExpandedBackgroundColor(dynamic value) {
    if (value is Map) {
      return controller.isDarkMode 
          ? Colors.blue[900]!.withOpacity(0.3) 
          : Colors.blue[50]!;
    } else {
      return controller.isDarkMode 
          ? Colors.green[900]!.withOpacity(0.3) 
          : Colors.green[50]!;
    }
  }

  Color _getCollapsedBackgroundColor(dynamic value) {
    return controller.isDarkMode 
        ? Colors.grey[800]!.withOpacity(0.5) 
        : Colors.grey[100]!;
  }

  Color _getIconColor(dynamic value) {
    if (value is Map) {
      return controller.isDarkMode ? Colors.blue[300]! : Colors.blue[600]!;
    } else {
      return controller.isDarkMode ? Colors.green[300]! : Colors.green[600]!;
    }
  }

  Widget _buildValueNode(dynamic value, int depth) {
    Color color;
    String displayValue;
    IconData icon;
    
    if (value is String) {
      color = controller.isDarkMode ? Colors.orange[300]! : Colors.orange[600]!;
      displayValue = '"$value"';
      icon = Icons.text_fields;
    } else if (value is num) {
      color = controller.isDarkMode ? Colors.purple[300]! : Colors.purple[600]!;
      displayValue = value.toString();
      icon = Icons.numbers;
    } else if (value is bool) {
      color = controller.isDarkMode ? Colors.red[300]! : Colors.red[600]!;
      displayValue = value.toString();
      icon = value ? Icons.check_circle : Icons.cancel;
    } else if (value == null) {
      color = controller.isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
      displayValue = 'null';
      icon = Icons.block;
    } else {
      color = controller.isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
      displayValue = value.toString();
      icon = Icons.help_outline;
    }

    final isMatch = searchQuery.isNotEmpty && 
        displayValue.toLowerCase().contains(searchQuery.toLowerCase());

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: EdgeInsets.only(left: depth * 20.0, bottom: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isMatch
            ? controller.isDarkMode 
                ? Colors.yellow[800]!.withOpacity(0.3)
                : Colors.yellow[200]!.withOpacity(0.7)
            : color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isMatch
              ? controller.isDarkMode ? Colors.yellow[600]! : Colors.yellow[600]!
              : color.withOpacity(0.3),
          width: isMatch ? 2 : 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Flexible(
            child: SelectableText(
              displayValue,
              style: TextStyle(
                fontFamily: 'Fira Code',
                color: color,
                fontSize: 13,
                fontWeight: isMatch ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyValuePair(String key, dynamic value, int depth) {
    final isMatch = searchQuery.isNotEmpty && 
        (key.toLowerCase().contains(searchQuery.toLowerCase()) ||
         value.toString().toLowerCase().contains(searchQuery.toLowerCase()));
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: EdgeInsets.only(left: depth * 20.0, bottom: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isMatch 
            ? controller.isDarkMode 
                ? Colors.yellow[800]!.withOpacity(0.3)
                : Colors.yellow[200]!.withOpacity(0.7)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isMatch ? Border.all(
          color: controller.isDarkMode ? Colors.yellow[600]! : Colors.yellow[600]!,
          width: 2,
        ) : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '"$key": ',
            style: TextStyle(
              fontFamily: 'Fira Code',
              color: controller.isDarkMode ? Colors.lightBlue[300] : Colors.blue[700],
              fontWeight: FontWeight.w600,
              fontSize: 13,
            ),
          ),
          const SizedBox(width: 4),
          Flexible(child: _buildInlineValue(value)),
        ],
      ),
    );
  }

  Widget _buildIndexValuePair(int index, dynamic value, int depth) {
    final isMatch = searchQuery.isNotEmpty && 
        value.toString().toLowerCase().contains(searchQuery.toLowerCase());
        
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: EdgeInsets.only(left: depth * 20.0, bottom: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isMatch 
            ? controller.isDarkMode 
                ? Colors.yellow[800]!.withOpacity(0.3)
                : Colors.yellow[200]!.withOpacity(0.7)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isMatch ? Border.all(
          color: controller.isDarkMode ? Colors.yellow[600]! : Colors.yellow[600]!,
          width: 2,
        ) : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '[$index]: ',
            style: TextStyle(
              fontFamily: 'Fira Code',
              color: controller.isDarkMode ? Colors.green[300] : Colors.green[700],
              fontWeight: FontWeight.w600,
              fontSize: 13,
            ),
          ),
          const SizedBox(width: 4),
          Flexible(child: _buildInlineValue(value)),
        ],
      ),
    );
  }

  Widget _buildInlineValue(dynamic value) {
    Color color;
    String displayValue;
    IconData icon;
    
    if (value is String) {
      color = controller.isDarkMode ? Colors.orange[300]! : Colors.orange[600]!;
      displayValue = '"$value"';
      icon = Icons.text_fields;
    } else if (value is num) {
      color = controller.isDarkMode ? Colors.purple[300]! : Colors.purple[600]!;
      displayValue = value.toString();
      icon = Icons.numbers;
    } else if (value is bool) {
      color = controller.isDarkMode ? Colors.red[300]! : Colors.red[600]!;
      displayValue = value.toString();
      icon = value ? Icons.check_circle : Icons.cancel;
    } else if (value == null) {
      color = controller.isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
      displayValue = 'null';
      icon = Icons.block;
    } else {
      color = controller.isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
      displayValue = value.toString();
      icon = Icons.help_outline;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 12, color: color.withOpacity(0.7)),
        const SizedBox(width: 4),
        Flexible(
          child: SelectableText(
            displayValue,
            style: TextStyle(
              fontFamily: 'Fira Code',
              color: color,
              fontSize: 13,
            ),
          ),
        ),
      ],
    );
  }
}

class TestPage extends StatelessWidget {
  const TestPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TestController());
    
    return Obx(() => Theme(
      data: controller.isDarkMode 
          ? ThemeData.dark().copyWith(
              // primarySwatch: Colors.blue,
              scaffoldBackgroundColor: Colors.grey[900],
              appBarTheme: AppBarTheme(
                backgroundColor: Colors.grey[850],
                elevation: 2,
              ),
            )
          : ThemeData.light().copyWith(
              // primarySwatch: Colors.blue,
              scaffoldBackgroundColor: Colors.grey[50],
            ),
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            "🚀 Advanced API Tester",
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          elevation: 0,
          actions: [
            // Search Button
            IconButton(
              onPressed: () => _showAdvancedSearchDialog(context, controller),
              icon: const Icon(Icons.search_rounded),
              tooltip: 'Advanced Search',
            ),
            // Theme Toggle
            IconButton(
              onPressed: controller.toggleDarkMode,
              icon: Icon(
                controller.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              ),
              tooltip: 'Toggle Theme',
            ),
            // Expand/Collapse All
            PopupMenuButton(
              icon: const Icon(Icons.unfold_more_rounded),
              tooltip: 'Tree Actions',
              itemBuilder: (context) => [
                PopupMenuItem(
                  onTap: controller.expandAll,
                  child: const Row(
                    children: [
                      Icon(Icons.unfold_more, size: 18),
                      SizedBox(width: 8),
                      Text('Expand All'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  onTap: controller.collapseAll,
                  child: const Row(
                    children: [
                      Icon(Icons.unfold_less, size: 18),
                      SizedBox(width: 8),
                      Text('Collapse All'),
                    ],
                  ),
                ),
              ],
            ),
            // More Options
            IconButton(
              onPressed: () => _showOptionsDialog(context, controller),
              icon: const Icon(Icons.more_vert_rounded),
              tooltip: 'More Options',
            ),
            // Refresh Button
            Obx(() => Container(
              margin: const EdgeInsets.only(right: 8),
              child: IconButton(
                onPressed: controller.isLoading ? null : controller.fetchData,
                icon: controller.isLoading 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.refresh_rounded),
                tooltip: 'Refresh Data',
              ),
            )),
          ],
        ),
        body: Column(
          children: [
            // Search Bar
            Container(
              margin: const EdgeInsets.all(16),
              child: _buildSearchBar(controller),
            ),
            // Main Content
            Expanded(
              child: Obx(() {
                if (controller.isLoading) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          'Fetching data...',
                          style: TextStyle(
                            color: controller.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  );
                }
                
                if (controller.data == null) {
                  return _buildEmptyState(controller);
                }
                
                final displayData = controller.searchQuery.isNotEmpty 
                    ? controller.filteredData 
                    : controller.data;
                    
                if (displayData == null && controller.searchQuery.isNotEmpty) {
                  return _buildNoSearchResults(controller);
                }
                
                return JsonViewer(
                  data: displayData,
                  controller: controller,
                  searchQuery: controller.searchQuery,
                );
              }),
            ),
          ],
        ),
        floatingActionButton: Obx(() => controller.data != null
            ? FloatingActionButton.extended(
                onPressed: controller.copyToClipboard,
                icon: const Icon(Icons.copy_rounded),
                label: const Text('Copy JSON'),
                backgroundColor: controller.isDarkMode ? Colors.blue[700] : Colors.blue[600],
              )
            : const SizedBox.shrink(),
        ),
      ),
    ));
  }

  Widget _buildSearchBar(TestController controller) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: controller.isDarkMode ? Colors.grey[800] : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: controller.setSearchQuery,
        decoration: InputDecoration(
          hintText: 'Search JSON keys and values...',
          prefixIcon: const Icon(Icons.search_rounded),
          suffixIcon: Obx(() => controller.searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () => controller.setSearchQuery(''),
                  icon: const Icon(Icons.clear_rounded),
                )
              : const SizedBox.shrink()),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: controller.isDarkMode ? Colors.grey[800] : Colors.white,
        ),
      ),
    );
  }

  Widget _buildEmptyState(TestController controller) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: controller.isDarkMode 
                  ? Colors.grey[800]?.withOpacity(0.5) 
                  : Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.api_rounded,
              size: 64,
              color: controller.isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Data Available',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: controller.isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the refresh button to fetch data from your API',
            style: TextStyle(
              color: controller.isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: controller.fetchData,
            icon: const Icon(Icons.refresh_rounded),
            label: const Text('Fetch Data'),
            style: ElevatedButton.styleFrom(
              backgroundColor: controller.isDarkMode ? Colors.blue[700] : Colors.blue[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoSearchResults(TestController controller) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: controller.isDarkMode 
                  ? Colors.orange[800]?.withOpacity(0.2) 
                  : Colors.orange[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 64,
              color: controller.isDarkMode ? Colors.orange[400] : Colors.orange[600],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Results Found',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: controller.isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Obx(() => Text(
            'No matches found for "${controller.searchQuery}"',
            style: TextStyle(
              color: controller.isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          )),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => controller.setSearchQuery(''),
            icon: const Icon(Icons.clear_rounded),
            label: const Text('Clear Search'),
            style: ElevatedButton.styleFrom(
              backgroundColor: controller.isDarkMode ? Colors.orange[700] : Colors.orange[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAdvancedSearchDialog(BuildContext context, TestController controller) {
    final textController = TextEditingController(text: controller.searchQuery);
    
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: controller.isDarkMode ? Colors.grey[800] : Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: controller.isDarkMode ? Colors.blue[800] : Colors.blue[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.search_rounded,
                      color: controller.isDarkMode ? Colors.blue[300] : Colors.blue[600],
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Advanced Search',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              TextField(
                controller: textController,
                decoration: InputDecoration(
                  hintText: 'Enter search term...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: const Icon(Icons.search_rounded),
                  filled: true,
                  fillColor: controller.isDarkMode ? Colors.grey[700] : Colors.grey[50],
                ),
                onChanged: (value) => controller.setSearchQuery(value),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              if (controller.searchHistory.isNotEmpty) ...[
                const Text(
                  'Recent Searches',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    itemCount: controller.searchHistory.length,
                    itemBuilder: (context, index) {
                      final query = controller.searchHistory[index];
                      return ListTile(
                        dense: true,
                        leading: const Icon(Icons.history_rounded, size: 18),
                        title: Text(
                          query,
                          style: const TextStyle(fontSize: 14),
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.north_west_rounded, size: 16),
                          onPressed: () {
                            textController.text = query;
                            controller.setSearchQuery(query);
                          },
                        ),
                        onTap: () {
                          controller.setSearchQuery(query);
                          Navigator.pop(context);
                        },
                      );
                    },
                  ),
                ),
              ],
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      controller.setSearchQuery('');
                      Navigator.pop(context);
                    },
                    child: const Text('Clear'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: controller.isDarkMode ? Colors.blue[700] : Colors.blue[600],
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Done'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showOptionsDialog(BuildContext context, TestController controller) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: controller.isDarkMode ? Colors.grey[800] : Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: controller.isDarkMode ? Colors.purple[800] : Colors.purple[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.settings_rounded,
                      color: controller.isDarkMode ? Colors.purple[300] : Colors.purple[600],
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'API Tester Options',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildOptionTile(
                icon: Icons.link_rounded,
                title: 'Change URL',
                subtitle: 'Modify the API endpoint',
                onTap: () {
                  Navigator.pop(context);
                  _showUrlDialog(context, controller);
                },
                color: controller.isDarkMode ? Colors.blue[300]! : Colors.blue[600]!,
              ),
              _buildOptionTile(
                icon: Icons.copy_rounded,
                title: 'Copy Response',
                subtitle: 'Copy JSON to clipboard',
                onTap: () {
                  Navigator.pop(context);
                  controller.copyToClipboard();
                },
                color: controller.isDarkMode ? Colors.green[300]! : Colors.green[600]!,
              ),
              _buildOptionTile(
                icon: Icons.clear_rounded,
                title: 'Clear Data',
                subtitle: 'Remove all loaded data',
                onTap: () {
                  Navigator.pop(context);
                  controller.clearData();
                },
                color: controller.isDarkMode ? Colors.red[300]! : Colors.red[600]!,
              ),
              _buildOptionTile(
                icon: Icons.info_rounded,
                title: 'Current URL',
                // subtitle: null,
                subtitle: 
                    controller.url,
                onTap: null,
                color: controller.isDarkMode ? Colors.orange[300]! : Colors.orange[600]!,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: subtitle != null ? Text(subtitle) : null,
        trailing: trailing ?? (onTap != null ? const Icon(Icons.chevron_right_rounded) : null),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showUrlDialog(BuildContext context, TestController controller) {
    final textController = TextEditingController(text: controller.url);
    
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: 500,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: controller.isDarkMode ? Colors.grey[800] : Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: controller.isDarkMode ? Colors.blue[800] : Colors.blue[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.link_rounded,
                      color: controller.isDarkMode ? Colors.blue[300] : Colors.blue[600],
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'API Endpoint',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              TextField(
                controller: textController,
                decoration: InputDecoration(
                  hintText: 'https://api.example.com/endpoint',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: controller.isDarkMode ? Colors.grey[700] : Colors.grey[50],
                  prefixIcon: const Icon(Icons.http_rounded),
                ),
                maxLines: 3,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () {
                      final newUrl = textController.text.trim();
                      if (newUrl.isNotEmpty) {
                        controller.setUrl(newUrl);
                        Navigator.pop(context);
                        Get.snackbar(
                          'URL Updated', 
                          'API endpoint has been changed',
                          backgroundColor: Colors.green.withOpacity(0.8),
                          colorText: Colors.white,
                          duration: const Duration(seconds: 2),
                        );
                      }
                    },
                    icon: const Icon(Icons.update_rounded),
                    label: const Text('Update'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: controller.isDarkMode ? Colors.blue[700] : Colors.blue[600],
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}