import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/configs/payment_channels.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/cardPayment.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/payments_page_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/topUp_otp.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/payment_kensbuns.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/main.dart' show isLight;

class PaymentsPage extends StatefulWidget {
  final List<Map<String, dynamic>>? tickets;
  final int eventId;
  final int price;
  const PaymentsPage(
      {super.key, this.tickets, required this.eventId, required this.price});

  @override
  State<PaymentsPage> createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> {
  final ValueNotifier<PaymentChannels?> _selectedValue =
      ValueNotifier(null);
 final  paymentChannel = Get.isRegistered<PaymentChannel>() ? Get.find<PaymentChannel>() :  Get.put(PaymentChannel());
 
  final GetStorage box = Get.find();
  final TextEditingController _fullNames = TextEditingController();
  final TextEditingController _emailAddress = TextEditingController();
  final TextEditingController _phone = TextEditingController();
  String? number;
  final _controller = Get.find<PaymentsController>();
  @override
  void initState() {
    _phone.text =
        '0${Get.find<Eventcontroller>().getLocalUser()?.phoneNumber?.substring(3) ?? ''}';
    number = _phone.text;
    _emailAddress.text =
        Get.find<Eventcontroller>().getLocalUser()?.email ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final paymentMethods = paymentChannel.paymentChannels.value;
    return ValueListenableBuilder(
        valueListenable: _selectedValue,
        builder: (context, selectedValue, child) {
          return Container(
              padding: EdgeInsets.only(
                top: 16.0.h,
                left: 8.0.w,
                right: 8.0.w,
                bottom: MediaQuery.of(context).viewInsets.bottom + 16.0.h,
              ),
              margin: EdgeInsets.only(
                left: 8.0.w,
                right: 8.0.w,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'select_payment_method'.tr,
                            style: TextStyle(
                                fontSize: 18.spMin,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: Text('cancel'.tr,
                              style: const TextStyle(color: primaryColor)),
                        ),
                      ],
                    ),
                    const Divider(),
                    SizedBox(
                      height: 136.h,
                      child: ListView.builder(
                          itemCount: paymentMethods.length,
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () =>
                                  _selectedValue.value = paymentMethods[index],
                              child: Container(
                                  height: 120.h,
                                  width: 120.w,
                                  alignment: Alignment.center,
                                  margin: EdgeInsets.all(8.spMin),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8.w, vertical: 0),
                                  decoration: BoxDecoration(
                                      color: selectedValue ==
                                              paymentMethods[index]
                                          ? AppColors.primary.withOpacity(0.05)
                                          : isLight.value
                                              ? Colors.white
                                              : Colors.transparent,
                                      border: Border.all(
                                          width: selectedValue ==
                                                  paymentMethods[index]
                                              ? 2
                                              : 1,
                                          color: selectedValue ==
                                                  paymentMethods[index]
                                              ? AppColors.primary
                                              : Colors.grey),
                                      borderRadius:
                                          BorderRadius.circular(15.r)),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        paymentMethods[index].name,
                                        style: const TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w600),
                                      ), paymentMethods[index].name == 'Card' ? const PaymentKensbuns() :
                                      Image.asset(
                                          height: paymentMethods[index]
                                                      .name ==
                                                  "Airtel Money"
                                              ? 66.h
                                              : 80.h,
                                          width: paymentMethods[index]
                                                      .name ==
                                                  "Airtel Money"
                                              ? 66.w
                                              : 80.w,
                                          color: paymentMethods[index]
                                                      .name ==
                                                  "Card"
                                              ? AppColors.primary
                                              : null,
                                          fit: BoxFit.contain,
                                          paymentMethods[index].imageUrl)
                                    ],
                                  )),
                            );
                          }),
                    ),
                    selectedValue == null
                        ? const SizedBox()
                        : Column(
                            children: [
                              SizedBox(height: 20.h),
                              // Show all input fields regardless of payment method
                              MyTextField(
                                  controller: _fullNames,
                                  keyboardType: TextInputType.name,
                                  title: selectedValue.name == 'Card' 
                                      ? 'full_names'.tr 
                                      : 'full_names_optional'.tr),
                              MyTextField(
                                  controller: _emailAddress,
                                  keyboardType: TextInputType.emailAddress,
                                  title: selectedValue.name == 'Card'
                                      ? 'email_address'.tr
                                      : 'email_address_optional'.tr),
                              CustomInternationalPhoneInput(
                                controller: _phone,
                                onInputChanged: (PhoneNumber numbers) {
                                  number = numbers.phoneNumber
                                      .toString()
                                      .replaceAll("+", '');
                                },
                                  validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'phone_number_is_required'.tr;
                                  }
                                  return null;
                                }, 
                              ),
                              SizedBox(height: 20.h),
                              Obx(() {
                                Map<String, dynamic> data = {
                                  'channel_code': selectedValue.channelCode,
                                  'phone_number': number,
                                  // 'affiliate_code': "344",
                                  'longitude': double.tryParse(
                                      box.read(CacheKeys.long)?.toString() ??
                                          ''),
                                  'latitude': double.tryParse(
                                      box.read(CacheKeys.lat)?.toString() ??
                                          ''),
                                  'first_name': _fullNames.text,
                                  'second_name': "",
                                  'email': _emailAddress.text,
                                  'tickets': widget.tickets,
                                  'event_id': widget.eventId.toString(),
                                };
                                return MyButton(
                                    showLoading: _controller.isLoading.value,
                                    label: 'pay'.tr,
                                    onClick: () async {
                                      if (!_controller.isLoading.value) {
                                        if (selectedValue.name == "Card") {
                                          if (await _controller
                                              .cardPaymentCheckout(
                                                  phoneNumber:
                                                      Get.put(Eventcontroller())
                                                              .getLocalUser()
                                                              ?.phoneNumber ??
                                                          "",
                                                  email: _emailAddress.text,
                                                  amount: widget.price,
                                                  channel: 55,
                                                  userId:
                                                      Get.put(Eventcontroller())
                                                              .getLocalUser()
                                                              ?.id ??
                                                          0)) {
                                            Get.off(() => const CardPayment(
                                                isPurchasingTicket: true,
                                                isChamaContribute: false));
                                          }
                                        } else if (selectedValue.name ==
                                            'Sasapay') {
                                          if (await _controller.completePayment(
                                              context: context, data: data)) {
                                            Get.off(() => TopUpOtp(
                                                transId: _controller
                                                    .transId.string));
                                          }
                                        } else {
                                          if (await _controller.completePayment(
                                              context: context, data: data)) {
                                            Snack.show(true,
                                                'stk_sent_enter_pin'.tr.replaceAll('{method}', selectedValue.name));
                                          }
                                        }
                                      }
                                    });
                              })
                            ],
                          )
                  ],
                ),
              ));
        });
  }
}
