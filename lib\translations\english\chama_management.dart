const Map<String, String> chamaManagementEnglish = {
  'my_chama': 'My Chama',
  'create_a_chama': 'Create a chama',
  'chama_name': 'Chama name',
  
  'ready_to_grow_together': 'Ready To Grow Together?',
  'start_your_chama_build_wealth': 'Start your chama, build wealth, and support each other.',
  'create_a_chama_title': 'Create a Chama',
  'chama_details': 'Chama\n Details',
  'members': 'Members',
  'chama_name_label': 'Chama Name',
  'chama_name_hint': 'e.g Was<PERSON>i Chama',
  'chama_name_required': 'Chama Name cannot be empty',
  'chama_name_length': 'Chama Name must be between 5 and 300',
  'chama_description': 'Chama Description',
  'chama_description_hint': 'e.g purpose of chama',
  'whatsapp_group_link': 'Whatsapp group link(optional)',
  'group_link': 'Group link',
  'enter_referer_code': 'Enter Referer code(Optional)',
  'referral_code': 'Referral code',
  'referral_code_hint': 'e.g 12',
  'enter_chama_email': 'Enter Chama Email',
  'email_hint': 'e.g <EMAIL>',
  'how_much_contribute': 'How Much Does each Member Contribute',
  'amount_hint': '1000',
  'invite_chama_members': 'Invite Chama Members',
  'update_member_details': 'Update Member Details',
  'enter_first_name': 'Enter First Name',
  'enter_last_name': 'Enter Last Name',
  'payer_phone_number': 'Payer phone number',
  'chama_member_phone_number': 'Chama member phone number',
  'pay_penalty': 'Pay Penalty',
  'select_penalty_to_pay': 'Select Penalty to Pay',
  'it_looks_like_has_penalties': 'It looks like',
  'has_penalties': 'has',
  'penalties': 'penalties.',
  'meetings': 'Meetings',
  'meetings_description': 'Here are meetings set by chama admin',
  'add_meeting': 'Add Meeting',
  'calender': 'Calender',
  'view_all_penalties': 'View all',
  'view_all_transactions': 'View all',
  'transactions': 'transactions',
  'issue_penalty_to': 'Issue penalty to',
  'update_member_info': 'Update member info',
  'remove_as_signatory': 'remove as \n signatory',
  'view_all_member_transactions': 'View all',
  'member_transactions': 'Transactions',
  'issue_penalty_to_member': 'Issue a Penalty to',
  'view_all_member_penalties': 'View all',
  'member_penalties': 'penalties',
  'remove_member': 'Remove',
  'member_phone_required': 'Please enter chama member phone number',
  'penalties_uppercase': 'PENALTIES',
  'chama_title': 'CHAMA TITLE:',
  'member_label': 'MEMBER:',
  'penalty_label': 'PENALTY:',
  'meeting': 'meeting',
  'penalty': 'penalty',
  'failed_to_load_chamas': 'Failed to load chamas. Please try again.',
  'members_count': 'Members',
  'add_chama_members': 'Add Chama Members',
  'add_members': 'Add Members',
  'an_error_occurred_adding_member': 'An error occurred while adding member.',
  'add_chama_member': 'Add Chama Member',
  'set_receiving_order': 'Set Receiving Order',
  'issue_penalties': 'Issue penalty(ies)',
  'update_penalty': 'Update Penalty',
  'set_penalties': 'Set Penalties',
  'create_penalties_description': 'Create penalties which can be issed to members later',
  'select_common_penalties': 'Select from common penalties',
  'click_to_select_penalty': 'Click to select penalty',
  'enter_penalty_name': 'Enter penalty name',
  'penalty_name_hint': 'e.g Missing meeting',
  'enter_penalty_description': 'Enter penalty description(Optional)',
  'penalty_description_hint': 'e.g description',
  'enter_penalty_amount': 'Enter penalty amount',
  'penalty_amount_hint': 'e.g 100',
  'common_penalties': 'COMMON PENALTIES',
  'chama_settings': 'Chama Settings',
  'settings_description': 'These are the settings set by the chama Admin',
  'beneficiary_per_cycle': 'Beneficiary per cycle',
  'beneficiary_percentage': 'Beneficiary Percentage',
  'signatories': 'Signatories',
  'signatory_threshold': 'Signatory Threshold',
  'get_to_know_beneficiaries': 'Get to know more of what beneficiaries are.',
  'beneficiaries_per_cycle': 'Beneficiaries per cycle:',
  'get_to_know_signatories': 'Get to know more about signatories',
  'update_button': 'Update',
  'save_button': 'Save',
  'documents': 'Documents',
  'share_documents_description': 'Share any documents with your chama members',
  'error_getting_documents': 'Error while getting documents',
  'add_document': 'Add Document',
  'signatories_title': 'Signatories',
  'signatories_description': 'Here are signatories set by the chama admin',
  'add_signatory': 'Add Signatory',
  'first_name': 'First Name',
  'last_name': 'Last Name',
  'whatsapp_number': 'Whatsapp Number',
  'chama': 'CHAMA',
  'created_by': 'Created By: ',
  'chama_email': 'Chama Email',
  'enter_your_chama_email': 'Enter Your Chama Email',
  'transfer_from_chama_account': 'Transfer funds from chama account',
  'transfer_from_penalty_kitty': 'Transfer from penalty kitty',
  'transfer_from_penalty': 'Transfer from Penalty',
  'transfer_from_chama': 'Transfer from Chama',
  'enter_beneficiary_phone_number': 'Enter beneficiary phone number',
  'enter_reason_for_transaction': 'Enter reason for transaction',
  'make_a_transfer': 'Make a Transfer',
  'make_transactions_with_ease': 'Make transactions with ease',
  'group_name': 'Group Name',
  'please_enter_group_name': 'Please enter group name',
  'group_members': 'Group Members',
  'add_members_to_group': 'Add members to the group',
  'delete_selected_members': 'Delete Selected Members',
  'edit_member': 'Edit Member',
  'phone_number_format_hint': 'Format: 0722XXX or 722XXX',
  'delete_member': 'Delete Member',
  'please_add_one_member_group': 'Please add at least one member to the group',
  'transfer_funds_from_chama_account': 'Transfer funds from chama account',
};