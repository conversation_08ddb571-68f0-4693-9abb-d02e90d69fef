import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'dart:ui' as ui;

class LanguageService extends GetxController {
  static LanguageService get instance => Get.find<LanguageService>();
  
  final box = GetStorage();
  final RxString currentLanguage = 'en'.obs;
  
  final List<String> supportedLanguages = ['en', 'sw', 'fr', 'es', 'de'];
  
  @override
  void onInit() {
    super.onInit();
    currentLanguage.value = _getInitialLanguage();
    _setIntlLocale(currentLanguage.value);
  }
  
  String _getInitialLanguage() {
    String? savedLanguage = box.read('language');
    if (savedLanguage != null) return savedLanguage;
    
    String deviceLocale = ui.PlatformDispatcher.instance.locale.languageCode;
    return supportedLanguages.contains(deviceLocale) ? deviceLocale : 'en';
  }
  
  void _setIntlLocale(String lang) {
    Intl.defaultLocale = lang;
  }
  
  Future<void> changeLanguage(String lang) async {
    if (!supportedLanguages.contains(lang)) return;
    
    await box.write('language', lang);
    currentLanguage.value = lang;
    _setIntlLocale(lang);
    Get.updateLocale(ui.Locale(lang));
  }
}