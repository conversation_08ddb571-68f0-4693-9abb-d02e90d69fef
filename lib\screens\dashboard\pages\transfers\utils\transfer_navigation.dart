// Transfer Navigation Utilities
// Helper functions for navigating to different transfer types

import 'package:get/get.dart';
import '../views/screens/transfer_page.dart';
import '../models/transfer_type.dart';

class TransferNavigation {
  /// Navigate to event transfer
  static void toEventTransfer({
    required int kittyId,
    String? title,
  }) {
    Get.to(() => TransferPage(
      config: TransferPageConfig(
        transferType: TransferType.event,
        entityId: kittyId,
        title: title ?? 'Event Transfer',
        showApprovalFlow: false,
      ),
    ));
  }

  /// Navigate to chama transfer
  static void toChamaTransfer({
    required int chamaId,
    String? title,
  }) {
    Get.to(() => TransferPage(
      config: TransferPageConfig(
        transferType: TransferType.chama,
        entityId: chamaId,
        title: title ?? 'Chama Transfer',
        showApprovalFlow: true,
      ),
    ));
  }

  /// Navigate to penalty transfer
  static void toPenaltyTransfer({
    required int chamaId,
    String? title,
  }) {
    Get.to(() => TransferPage(
      config: TransferPageConfig(
        transferType: TransferType.penalty,
        entityId: chamaId,
        title: title ?? 'Penalty Transfer',
        isPenaltyTransfer: true,
        showApprovalFlow: true,
      ),
    ));
  }

  /// Navigate to transfer with custom configuration
  static void toTransferWithConfig(TransferPageConfig config) {
    Get.to(() => TransferPage(config: config));
  }
}

/// Extension methods for easier navigation
extension TransferNavigationExtension on GetInterface {
  /// Navigate to event transfer
  void toEventTransfer({
    required int kittyId,
    String? title,
  }) {
    TransferNavigation.toEventTransfer(
      kittyId: kittyId,
      title: title,
    );
  }

  /// Navigate to chama transfer
  void toChamaTransfer({
    required int chamaId,
    String? title,
  }) {
    TransferNavigation.toChamaTransfer(
      chamaId: chamaId,
      title: title,
    );
  }

  /// Navigate to penalty transfer
  void toPenaltyTransfer({
    required int chamaId,
    String? title,
  }) {
    TransferNavigation.toPenaltyTransfer(
      chamaId: chamaId,
      title: title,
    );
  }
}