import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show MethodChannel;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class DeepLinkHelper extends StatefulWidget {
  const DeepLinkHelper({super.key});

  @override
  State<DeepLinkHelper> createState() => _DeepLinkHelperState();
}

class _DeepLinkHelperState extends State<DeepLinkHelper> {
  // Deeplink Permission request
  final _storage = GetStorage();
  bool _neverShowAgain = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      persistentFooterButtons: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: SizedBox(
            height: 50.h,
            width: double.infinity,
            child: FilledButton(
                onPressed: () async {
                  _storage.write('showDeepLinkDialog', !_neverShowAgain);

                  await _openLinkSettings();

                  _storage.write('showDeepLinkDialog', true);

                  if (mounted) Navigator.of(context).pop();
                },
                child: Text(
                  'continue_button'.tr,
                  style: TextStyle(
                      fontSize: 16.spMin, fontWeight: FontWeight.w600),
                )),
          ),
        ),
      ],
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        actions: [
          TextButton(
              onPressed: () async {
                _storage.write('showDeepLinkDialog', !_neverShowAgain);

                await _openLinkSettings();

                _storage.write('showDeepLinkDialog', true);

                if (mounted) Navigator.of(context).pop();
              },
              child: Text(
                'continue_button'.tr,
                style:
                    TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w600),
              ))
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'to_enable_deep_linking_follow_steps'.tr,
                  style: TextStyle(fontSize: 18.spMin, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20.h),
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(maxHeight: 200.h),
                  child: Image.asset(
                    'assets/images/add_links_arrow.jpg',
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  'click_add_links_button'.tr,
                  style: TextStyle(fontSize: 16.spMin),
                ),
                SizedBox(height: 24.h),
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(maxHeight: 200.h),
                  child: Image.asset(
                    'assets/images/all_links_dialog.jpg',
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  'select_all_links_onekitty'.tr,
                  style: TextStyle(fontSize: 16.spMin),
                ),
                SizedBox(height: 16.h),
                Row(
                  children: [
                    Checkbox(
                      value: _neverShowAgain,
                      onChanged: (value) {
                        setState(() {
                          _neverShowAgain = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Text(
                        'dont_show_this_again'.tr,
                        style: TextStyle(fontSize: 14.spMin),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 100.h)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _openLinkSettings() async {
    try {
      const platform = MethodChannel('ke.co.onekitty/deep_link_settings');
      final bool result = await platform.invokeMethod('openLinkSettings');
      if (!result) {
        debugPrint('Failed to open link settings');
      }
    } catch (e) {
      debugPrint('Error opening link settings: $e');
    }
  }
}
