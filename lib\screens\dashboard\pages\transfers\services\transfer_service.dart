// Unified Transfer Service
// Handles all transfer-related API calls across different transfer types

import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/chama/transfer_req.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/services/api_urls.dart';
import '../models/transfer_type.dart';

class TransferResult {
  final bool success;
  final String message;
  final Map<String, dynamic> data;

  TransferResult({
    required this.success,
    required this.message,
    this.data = const {},
  });
}

class TransferService extends GetxService {
  final HttpService _apiProvider = Get.find<HttpService>();
  final Logger _logger = Get.find<Logger>();

  /// Initiate transfer based on type
  Future<TransferResult> initiateTransfer({
    required TransferPageConfig config,
    required TransferRequest request,
  }) async {
    try {
      final url = _getTransferUrl(config);
      final requestData = request.toJson();
      
      _logger.i('Initiating ${config.transferType.name} transfer');
      _logger.i('URL: $url');
      _logger.i('Request data: $requestData');

      final response = await _apiProvider.request(
        url: url,
        method: Method.POST,
        params: requestData,
      );

      _logger.i('Response: ${response.data}');

      if (response.data["status"] ?? false) {
        return TransferResult(
          success: true,
          message: response.data['message'] ?? 'Transfer initiated successfully',
          data: response.data,
        );
      } else {
        return TransferResult(
          success: false,
          message: response.data['message'] ?? 'Transfer failed',
        );
      }
    } catch (e) {
      _logger.e('Transfer initiation error: $e');
      return TransferResult(
        success: false,
        message: 'Network error occurred',
      );
    }
  }

  /// Confirm transfer based on type
  Future<TransferResult> confirmTransfer({
    required TransferPageConfig config,
    required Map<String, dynamic> transferData,
  }) async {
    try {
      final url = _getConfirmUrl(config);
      _logger.i('Confirming ${config.transferType.name} transfer to: $url');

      final response = await _apiProvider.request(
        url: url,
        method: Method.POST,
        params: _buildConfirmationData(config, transferData),
      );

      if (response.data["status"] ?? false) {
        return TransferResult(
          success: true,
          message: response.data['message'] ?? 'Transfer completed successfully',
          data: response.data,
        );
      } else {
        return TransferResult(
          success: false,
          message: response.data['message'] ?? 'Transfer confirmation failed',
        );
      }
    } catch (e) {
      _logger.e('Transfer confirmation error: $e');
      return TransferResult(
        success: false,
        message: 'Network error occurred',
      );
    }
  }

  /// Get transfer URL based on type
  String _getTransferUrl(TransferPageConfig config) {
    switch (config.transferType) {
      case TransferType.event:
        return ApiUrls.TRANSFERREQUEST;
      case TransferType.chama:
      case TransferType.penalty:
        return ApiUrls.transferReq;
    }
  }

  /// Get confirmation URL based on type
  String _getConfirmUrl(TransferPageConfig config) {
    switch (config.transferType) {
      case TransferType.event:
        return ApiUrls.TRANSFERCONFIRM;
      case TransferType.chama:
      case TransferType.penalty:
        return ApiUrls.transferConfirm;
    }
  }

  /// Build confirmation data based on transfer type
  Map<String, dynamic> _buildConfirmationData(
    TransferPageConfig config,
    Map<String, dynamic> transferData,
  ) {
    switch (config.transferType) {
      case TransferType.event:
        return _buildEventConfirmationData(config, transferData);
      case TransferType.chama:
      case TransferType.penalty:
        return _buildChamaConfirmationData(config, transferData);
    }
  }

  /// Build event confirmation data
  Map<String, dynamic> _buildEventConfirmationData(
    TransferPageConfig config,
    Map<String, dynamic> transferData,
  ) {
    final data = transferData['data'] ?? {};
    final originalRequest = transferData['original_request'] ?? {};
    
    return {
      "amount": data['amount_received'] ?? originalRequest['amount'],
      "user_id": originalRequest['user_id'],
      "kitty_id": config.entityId,
      "channel_code": originalRequest['channel_code'],
      "recipient_account_number": data['beneficiary_account'] ?? originalRequest['recipient_account_number'],
      "recipient_account_ref": data['beneficiary_account_ref'] ?? originalRequest['recipient_account_ref'],
      "reason": originalRequest['reason'],
      "transfer_mode": originalRequest['transfer_mode'],
      "latitude": originalRequest['latitude'],
      "longitude": originalRequest['longitude'],
    };
  }

  /// Build chama confirmation data
  Map<String, dynamic> _buildChamaConfirmationData(
    TransferPageConfig config,
    Map<String, dynamic> transferData,
  ) {
    final originalRequest = transferData['original_request'] ?? {};
    
    // For chama transfers, use the original request format
    return {
      "amount": originalRequest['amount'],
      "user_id": originalRequest['user_id'],
      "chama_id": config.entityId,
      "member_id": originalRequest['member_id'],
      "channel_code": originalRequest['channel_code'],
      "recipient_account_number": originalRequest['recipient_account_number'],
      "recipient_account_ref": originalRequest['recipient_account_ref'],
      "reason": originalRequest['reason'],
      "transfer_mode": originalRequest['transfer_mode'],
      "latitude": originalRequest['latitude'],
      "longitude": originalRequest['longitude'],
      "device_id": originalRequest['device_id'],
      "device_model": originalRequest['device_model'],
      "is_penalty_kitty": config.isPenaltyTransfer,
    };
  }
}