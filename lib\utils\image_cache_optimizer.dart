import 'package:flutter/painting.dart';
import 'package:flutter/foundation.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:path_provider/path_provider.dart';

/// A utility class for optimizing image cache settings
class ImageCacheOptimizer {
  /// Initialize optimal image cache settings
  static Future<void> optimizeImageCache() async {
    // Configure the Flutter engine's image cache with better limits
    PaintingBinding.instance.imageCache.maximumSize = 200;  // More images in memory
    PaintingBinding.instance.imageCache.maximumSizeBytes = 100 << 20;  // 100 MB

    if (kDebugMode) {
      print('ImageCacheOptimizer: Configured Flutter image cache - 200 images, 100MB');
    }

    // Get application documents directory for storing cached images
    String storageLocation = (await getApplicationDocumentsDirectory()).path;

    // Configure Fast Cached Network Image with optimized settings
    await FastCachedImageConfig.init(
      clearCacheAfter: const Duration(days: 30), // Keep cache longer for better performance
      subDir: '$storageLocation/fastCachedImages',
    );

    if (kDebugMode) {
      print('ImageCacheOptimizer: Configured FastCachedImage with optimized settings');
    }
  }

  /// Clear the image cache
  static Future<void> clearImageCache() async {
    // Clear the Flutter engine image cache
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();

    // Also clear the Fast Cached Network Image cache
    await FastCachedImageConfig.clearAllCachedImages();

    if (kDebugMode) {
      print('ImageCacheOptimizer: Cleared all image caches');
    }
  }
}
