const Map<String, String> chamaManagementSwahili = {
  'my_chama': '<PERSON><PERSON> Yang<PERSON>',
  'create_a_chama': '<PERSON><PERSON><PERSON>',
  'chama_name': '<PERSON><PERSON> la <PERSON>',
  'chama_not_found': '<PERSON><PERSON> hakikupatikana',
  'ready_to_grow_together': '<PERSON><PERSON>?',
  'start_your_chama_build_wealth': '<PERSON><PERSON><PERSON> chama chako, jenga u<PERSON>, na msaidiane.',
  'create_a_chama_title': '<PERSON><PERSON><PERSON>ma',
  'chama_details': '<PERSON><PERSON><PERSON> ya\n Chama',
  'members': 'Wanachama',
  'chama_name_label': '<PERSON><PERSON> la Chama',
  'chama_name_hint': 'k.m. <PERSON><PERSON>i <PERSON>',
  'chama_name_required': '<PERSON>a la Chama haliwezi kuwa tupu',
  'chama_name_length': '<PERSON>a la Chama lazima liwe kati ya herufi 5 na 300',
  'chama_description': '<PERSON><PERSON><PERSON> ya Chama',
  'chama_description_hint': 'k.m. mad<PERSON>uni ya chama',
  'whatsapp_group_link': '<PERSON><PERSON> ya grupu la WhatsApp (hiari)',
  'group_link': '<PERSON><PERSON> ya Grupu',
  'enter_referer_code': 'Weka nambari ya aliyekuelekeza (Hiari)',
  'referral_code': 'Nambari ya Rufaa',
  'referral_code_hint': 'k.m. 12',
  'enter_chama_email': 'Weka Baruapepe ya Chama',
  'email_hint': 'k.m. <EMAIL>',
  'how_much_contribute': 'Kila Mwanachama Anachanga Kiasi Gani',
  'amount_hint': '1000',
  'invite_chama_members': 'Alika Wanachama',
  'update_member_details': 'Sasisha Maelezo ya Mwanachama',
  'enter_first_name': 'Weka Jina la Kwanza',
  'enter_last_name': 'Weka Jina la Mwisho',
  'payer_phone_number': 'Nambari ya simu ya mlipaji',
  'chama_member_phone_number': 'Nambari ya simu ya mwanachama',
  'pay_penalty': 'Lipa Adhabu',
  'select_penalty_to_pay': 'Chagua Adhabu ya Kulipa',
  'it_looks_like_has_penalties': 'Inaonekana kama',
  'has_penalties': 'ana',
  'penalties': 'adhabu.',
  'meetings': 'Mikutano',
  'meetings_description': 'Hii ni mikutano iliyopangwa na msimamizi wa chama',
  'add_meeting': 'Ongeza Mkutano',
  'calender': 'Kalenda',
  'view_all_penalties': 'Tazama zote',
  'view_all_transactions': 'Tazama zote',
  'transactions': 'miamala',
  'issue_penalty_to': 'Mpe adhabu',
  'update_member_info': 'Sasisha maelezo ya mwanachama',
  'remove_as_signatory': 'Ondoa kama \n mtia saini',
  'view_all_member_transactions': 'Tazama zote',
  'member_transactions': 'Miamala',
  'issue_penalty_to_member': 'Mpe Adhabu',
  'view_all_member_penalties': 'Tazama zote',
  'member_penalties': 'adhabu',
  'remove_member': 'Ondoa',
  'member_phone_required': 'Tafadhali weka nambari ya simu ya mwanachama',
  'penalties_uppercase': 'ADHABU',
  'chama_title': 'JINA LA CHAMA:',
  'member_label': 'MWANACHAMA:',
  'penalty_label': 'ADHABU:',
  'meeting': 'mkutano',
  'penalty': 'adhabu',
  'failed_to_load_chamas': 'Imeshindwa kupakia chama. Tafadhali jaribu tena.',
  'members_count': 'Wanachama',
  'add_chama_members': 'Ongeza Wanachama',
  'add_members': 'Ongeza Wanachama',
  'an_error_occurred_adding_member': 'Kosa limetokea wakati wa kuongeza mwanachama.',
  'add_chama_member': 'Ongeza Mwanachama',
  'set_receiving_order': 'Panga Mpangilio wa Kupokea',
  'issue_penalties': 'Toa adhabu',
  'update_penalty': 'Sasisha Adhabu',
  'set_penalties': 'Weka Adhabu',
  'create_penalties_description': 'Tengeneza adhabu ambazo zinaweza kutolewa kwa wanachama baadaye',
  'select_common_penalties': 'Chagua kutoka kwa adhabu za kawaida',
  'click_to_select_penalty': 'Bofya kuchagua adhabu',
  'enter_penalty_name': 'Weka jina la adhabu',
  'penalty_name_hint': 'k.m. Kukosa mkutano',
  'enter_penalty_description': 'Weka maelezo ya adhabu (Hiari)',
  'penalty_description_hint': 'k.m. maelezo',
  'enter_penalty_amount': 'Weka kiasi cha adhabu',
  'penalty_amount_hint': 'k.m. 100',
  'common_penalties': 'ADHABU ZA KAWAIDA',
  'chama_settings': 'Mipangilio ya Chama',
  'settings_description': 'Hii ni mipangilio iliyowekwa na Msimamizi wa chama',
  'beneficiary_per_cycle': 'Mnufaika kwa kila mzunguko',
  'beneficiary_percentage': 'Asilimia ya Mnufaika',
  'signatories': 'Watiasaini',
  'signatory_threshold': 'Idadi ya chini ya Watiasaini',
  'get_to_know_beneficiaries': 'Jua zaidi kuhusu wanufaika.',
  'beneficiaries_per_cycle': 'Wanufaika kwa kila mzunguko:',
  'get_to_know_signatories': 'Jua zaidi kuhusu watiasaini',
  'update_button': 'Sasisha',
  'save_button': 'Hifadhi',
  'documents': 'Nyaraka',
  'share_documents_description': 'Shiriki nyaraka zozote na wanachama wako wa chama',
  'error_getting_documents': 'Kosa wakati wa kupata nyaraka',
  'add_document': 'Ongeza Waraka',
  'signatories_title': 'Watiasaini',
  'signatories_description': 'Hawa ni watiasaini waliowekwa na msimamizi wa chama',
  'add_signatory': 'Ongeza Mtia Saini',
  'first_name': 'Jina la Kwanza',
  'last_name': 'Jina la Mwisho',
  'whatsapp_number': 'Nambari ya WhatsApp',
  'chama': 'CHAMA',
  'created_by': 'Imeundwa na: ',
  'chama_email': 'Baruapepe ya Chama',
  'enter_your_chama_email': 'Weka Baruapepe ya Chama Chako',
  'transfer_from_chama_account': 'Hamisha fedha kutoka akaunti ya chama',
  'transfer_from_penalty_kitty': 'Hamisha kutoka kitty ya adhabu',
  'transfer_from_penalty': 'Hamisha kutoka kwa Adhabu',
  'transfer_from_chama': 'Hamisha kutoka kwa Chama',
  'enter_beneficiary_phone_number': 'Weka nambari ya simu ya mnufaika',
  'enter_reason_for_transaction': 'Weka sababu ya muamala',
  'make_a_transfer': 'Fanya Uhamisho',
  'make_transactions_with_ease': 'Fanya miamala kwa urahisi',
  'group_name': 'Jina la Grupu',
  'please_enter_group_name': 'Tafadhali weka jina la grupu',
  'group_members': 'Wanagrupu',
  'add_members_to_group': 'Ongeza wanachama kwenye grupu',
  'delete_selected_members': 'Futa Wanachama Waliochaguliwa',
  'edit_member': 'Hariri Mwanachama',
  'phone_number_format_hint': 'Mfumo: 0722XXX au 722XXX',
  'delete_member': 'Futa Mwanachama',
  'please_add_one_member_group': 'Tafadhali ongeza angalau mwanachama mmoja kwenye grupu',
  'transfer_funds_from_chama_account': 'Hamisha fedha kutoka akaunti ya chama',
};
