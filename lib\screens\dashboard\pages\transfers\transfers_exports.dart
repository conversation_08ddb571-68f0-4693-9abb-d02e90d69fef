// Unified Transfer System Exports
// Single import file for all transfer-related components

// Core Components
export 'views/screens/transfer_page.dart';
export 'models/transfer_type.dart';
export 'controllers/transfer_controller.dart';
export 'services/transfer_service.dart';

// Widgets  
export 'views/widgets/transfer_form_widget.dart';
export 'views/widgets/transfer_confirmation_widget.dart';
export 'views/widgets/payment_method_selector.dart';

// Utilities
export 'utils/transfer_navigation.dart';

// Services
export 'services/transfer_service_binding.dart';

/// Usage:
/// 
/// ```dart
/// import 'package:onekitty/screens/dashboard/pages/transfers/transfers_exports.dart';
/// 
/// // Navigate to transfers:
/// Get.toEventTransfer(eventId: 123);
/// Get.toChamaTransfer(chamaId: 456);
/// Get.toPenaltyTransfer(chamaId: 456, isPenalty: true);
/// ```