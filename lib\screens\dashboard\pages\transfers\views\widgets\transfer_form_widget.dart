// Transfer Form Widget
// Reusable form components for transfers

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:onekitty/utils/my_text_field.dart';

class TransferFormWidget extends StatelessWidget {
  final TextEditingController amountController;
  final TextEditingController reasonController;
  final GlobalKey<FormState> formKey;

  const TransferFormWidget({
    super.key,
    required this.amountController,
    required this.reasonController,
    required this.formKey,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        children: [
          MyTextFieldwValidator(
            keyboardType: TextInputType.number,
            controller: amountController,
            title: 'enter_amount'.tr,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'amount_required'.tr;
              }
              final amount = int.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'enter_valid_amount'.tr;
              }
              return null;
            },
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
          MyTextFieldwValidator(
            controller: reasonController,
            title: 'enter_reason_for_transaction'.tr,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'reason_required'.tr;
              }
              return null;
            },
          ),
        ],
      ),
    );
  }
}