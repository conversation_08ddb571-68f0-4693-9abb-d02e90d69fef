// import 'package:intl_phone_number_input/intl_phone_number_input.dart';
// class CountryConfig {

//   static const isoCode = "BI";
//   static const dialCode = '+257';
//   static const dialCodeMinus = '257';
//   static const name = "Burundi";
//   static const getCurrencyCode = 'BIF';

//   static const defaultPaymentChannel = 63902;

//   static final phoneNumber = PhoneNumber(
//     isoCode: "BI", 
//     dialCode: '+257'
//   );

//   static PhoneNumber initPhone({String? phoneNumber}){
//     return PhoneNumber(
//       isoCode: "BI", 
//       dialCode: '+257',
//       phoneNumber: phoneNumber??""
//     );
//   }
// }

//KENYA  users/kitties

import 'package:intl_phone_number_input/intl_phone_number_input.dart';
class CountryConfig {

  static const isoCode = "KE";
  static const dialCode = '+254';
  static const dialCodeMinus = '254';
  static const name = "Kenya";
  static const getCurrencyCode = 'KES';

  static const defaultPaymentChannel = 63902;

  static final phoneNumber = PhoneNumber(
    isoCode: "KE", 
    dialCode: '+254'
  );

  static PhoneNumber initPhone({String? phoneNumber}){
    return PhoneNumber(
      isoCode: "KE", 
      dialCode: '+254',
      phoneNumber: phoneNumber??""
    );
  }
}
