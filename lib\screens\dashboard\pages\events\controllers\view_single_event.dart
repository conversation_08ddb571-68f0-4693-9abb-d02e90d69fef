import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/edit_event_controller.dart';

class ViewSingleEventController extends GetxController implements GetxService {
  Rx<Event> event = Event().obs;
  final transactions = <TransactionModel>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();

  // Track the current event ID to detect changes
  int? _currentEventId;

  @override
  void onInit() {
    super.onInit();
    // Clear any previous state when controller is initialized
    _clearState();
  }

  @override
  void onClose() {
    _clearState();
    super.onClose();
  }

  void _clearState() {
    event.value = Event();
    transactions.clear();
    _currentEventId = null;
  }

  // Method to set event and clear previous state if it's a different event
  void setEvent(Event newEvent) {
    if (_currentEventId != newEvent.id) {
      _clearState();
      _currentEventId = newEvent.id;
    }
    event.value = newEvent;
  }

  /// Refresh event data and update all related lists
  Future<void> refreshEventData(int eventId, {bool isOrganizer = false}) async {
    try {
      logger.i('Refreshing event data for ID: $eventId');

      // Get the edit event controller to fetch fresh data
      final editEventController = Get.find<EditEventController>();
      await editEventController.fetchEventDetail(eventId, isOrganizer: isOrganizer);

      // Update this controller's event data
      event.value = editEventController.event.value;
      _currentEventId = eventId;

      // Update event lists to ensure UI consistency
      await _updateEventListsAfterRefresh(eventId);

      logger.i('Event data refreshed successfully');
    } catch (e) {
      logger.e('Error refreshing event data: $e');
      rethrow;
    }
  }

  /// Update event lists in EventController to ensure UI consistency
  Future<void> _updateEventListsAfterRefresh(int eventId) async {
    try {
      final eventsController = Get.find<Eventcontroller>();

      // Update userEvents list if the event exists there
      final userEventIndex = eventsController.userEvents.indexWhere(
        (myEvent) => myEvent.event.id == eventId
      );

      if (userEventIndex != -1) {
        final currentMyEvent = eventsController.userEvents[userEventIndex];

        // Create updated MyEventsModel with fresh event data
        final updatedMyEvent = MyEventsModel(
          event: event.value,
          count: currentMyEvent.count,
          hasSignatoryTransactions: currentMyEvent.hasSignatoryTransactions,
          heroTagSuffix: currentMyEvent.heroTagSuffix,
        );

        eventsController.userEvents[userEventIndex] = updatedMyEvent;
        logger.i('Updated userEvents list for event ID: $eventId');
      }

      // Update general events list if the event exists there
      final eventIndex = eventsController.events.indexWhere(
        (eventItem) => eventItem.id == eventId
      );

      if (eventIndex != -1) {
        eventsController.events[eventIndex] = event.value;
        logger.i('Updated events list for event ID: $eventId');
      }

      // Trigger UI update
      eventsController.update();
      update();

    } catch (e) {
      logger.e('Error updating event lists after refresh: $e');
    }
  }

  Future fetchTransactions(int eventId, {int? size = 20, int? page = 0}) async {
    try {
      // Clear transactions if this is a different event
      if (_currentEventId != eventId) {
        transactions.clear();
        _currentEventId = eventId;
      }

      final response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId&size=$size&page=$page",
      );
      if (response.data != null && response.data['data'] != null) {
        final returnedData = response.data['data']['items'] as List;
        transactions.value = returnedData
            .map((item) => TransactionModel.fromJson(item))
            .toList();
      } else {
        Get.snackbar('Error', response.data['message'] ?? 'Failed to load transactions',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e('Error fetching transactions: $e');
    }
  }
}
