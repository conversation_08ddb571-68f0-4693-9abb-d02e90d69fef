import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

import '../../../../../services/api_urls.dart';
import '../../../../../services/http_service.dart';

class ViewEventController extends GetxController implements GetxService {
  final isReserving = false.obs;
  final isInviting = false.obs;
  final logger = Get.find<Logger>();
  final RxInt selectedTicket = 0.obs;

  final HttpService apiProvider = Get.find();
  Future reserve(
      {required String eventId,
      required String phoneNumber,
      required String firstname,
      required String email,
      required List tickets}) async {
    try {
      isReserving(true);

      var response = await apiProvider
          .request(url: ApiUrls.RESERVETICKET, method: Method.POST, params: {
        "event_id": eventId,
        "phone_number": phoneNumber,
        "longitude": 1.33,
        "latitude": 3.5,
        "first_name": firstname,
        "second_name": '',
        "email": email,
        "tickets": tickets
      });
      if (response.data['status'] ?? false) {
        Get.snackbar('Success', 'Successfully reserved ticket');
      } else {
        
        Get.snackbar('error', response.data['message'] ?? 'error', backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e('$e');
    } finally {
      isReserving(false);
    }
  }

  Future invite(
      {required int ticketId,
      required String number,
      required String fname,
      required String lname,
      required String email}) async {
    try {
      isInviting(true);
      var response = await apiProvider
          .request(url: ApiUrls.INVITEUSERS, method: Method.POST, params: {
        "ticket_id": ticketId,
        "users": [
          {
            "phone_number": number,
            "first_name": fname,
            "second_name": lname,
            "email": email
          }
        ]
      });

      if (response.data['status'] ?? false) {
        Get.snackbar('Success', 'invite sent', backgroundColor: Colors.green);
      } else {

        
        Get.snackbar('Error', response.data['message'] ?? 'error', backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e('$e');      
    } finally {
      isInviting(false);
    }
  }
}
