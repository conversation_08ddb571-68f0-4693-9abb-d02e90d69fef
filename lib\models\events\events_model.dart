// To parse this JSON data, do
//
//     final eventsModel = eventsModelFromJson(jsonString);

// ignore_for_file: constant_identifier_names

import 'dart:convert';

import 'categories_model.dart';
import 'media_models.dart';
import 'tickets_model.dart';

List<MyEventsModel> myEventsModelFromJson(String str) =>
    List<MyEventsModel>.from(
        json.decode(str).map((x) => MyEventsModel.fromJson(x)));

String myEventsModeToJson(List<MyEventsModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class MyEventsModel {
  final Event event;
  final int count;
  final bool hasSignatoryTransactions;
  final String? heroTagSuffix;

  MyEventsModel({
    this.hasSignatoryTransactions = false,
    required this.event,
    this.count = 0,
    this.heroTagSuffix,
  });
  factory MyEventsModel.fromJson(Map<String, dynamic> json) => MyEventsModel(
        event: Event.fromJson(json["event"]),
        count: json["count"],
        hasSignatoryTransactions: json["has_signatory_transactions"] ?? false,
        heroTagSuffix: json["hero_tag_suffix"],
      );

  Map<String, dynamic> toJson() => {
        "event": event.toJson(),
        "count": count,
        "has_signatory_transactions": hasSignatoryTransactions,
        "hero_tag_suffix": heroTagSuffix,
      };
}

List<Event> eventsModelFromJson(String str) =>
    List<Event>.from(json.decode(str).map((x) => Event.fromJson(x)));

String eventsModeToJson(List<Event> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Event {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String title;
  final String username;
  final String description;
  final String email;
  final String phoneNumber;
  final dynamic balance;
  final String locationTip;
  final String venue;
  final double latitude;
  final double longitude;
  final Status? status;
  final int? categoryId;
  final CategoriesModel? category;
  final int? kittyId;
  List<EventMedia>? eventMedia;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<Ticket>? tickets;
  final List<EventsModelSocialAccount>? socialAccounts;
  final int? count;
  final int? referralCode;
  final UpdatedBy? updatedBy;

  Event({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title = '',
    this.username = '',
    this.description = '',
    this.balance = 0,
    this.email = '',
    this.phoneNumber = '',
    this.locationTip = '',
    this.venue = '',
    this.latitude = 0.0,
    this.longitude = 0.0,
    this.status = Status.ACTIVE,
    this.categoryId = 0,
    this.category,
    this.kittyId,
    this.eventMedia = const [],
    this.startDate,
    this.endDate,
    this.tickets = const [],
    this.socialAccounts = const [],
    this.count = 0,
    this.referralCode = 0,
    this.updatedBy,
  });

  factory Event.fromJson(Map<String, dynamic> json) => Event(
        id: json["ID"] ?? 0,
        createdAt: _parseDateTime(json["CreatedAt"]),
        updatedAt: _parseDateTime(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"] ?? '',
        username: json["username"] ?? '',
        count: json["count"] ?? 0,
        description: json["description"] ?? '',
        phoneNumber: json["phone_number"] ?? "" ?? '',
        balance: json["balance"] ?? 0,
        email: json["email"] ?? '',
        locationTip: json["location_tip"] ?? '',
        venue: json["venue"] ?? '',
        referralCode: json["referral_code"] ?? 0,
        latitude: (json["latitude"] ?? 0.0).toDouble(),
        longitude: (json["longitude"] ?? 0.0).toDouble(),
        status: statusValues.map[json["status"]],
        categoryId: json["category_id"] ?? 0,
        updatedBy: json["updated_by"] != null
            ? UpdatedBy.fromJson(json["updated_by"])
            : null,
        category: json["category"] != null
            ? CategoriesModel.fromJson(json["category"])
            : null,
        kittyId: json["kitty_id"] ?? 0,
        eventMedia: json["event_media"] != null
            ? List<EventMedia>.from(
                json["event_media"].map((x) => EventMedia.fromJson(x)))
            : null,
        startDate: _parseDateTime(json["start_date"]),
        endDate: _parseDateTime(json["end_date"]),
        tickets: json["tickets"] == null
            ? []
            : List<Ticket>.from(
                json["tickets"]!.map((x) => Ticket.fromJson(x))),
        socialAccounts: json["social_accounts"] == null
            ? []
            : List<EventsModelSocialAccount>.from(json["social_accounts"]!
                .map((x) => EventsModelSocialAccount.fromJson(x))),
      );

  static DateTime? _parseDateTime(dynamic dateValue) {
    if (dateValue == null) return null;
    if (dateValue is String) {
      if (dateValue.isEmpty || dateValue.toLowerCase() == 'null') return null;
      try {
        return DateTime.parse(dateValue);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": updatedAt?.toUtc().toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "username": username,
        "description": description,
        "phone_number": phoneNumber,
        "balance": balance,
        "email": email,
        "location_tip": locationTip,
        "venue": venue,
        "latitude": latitude,
        "longitude": longitude,
        "status": status != null ? statusValues.reverse[status] : null,
        "category_id": categoryId,
        "category": category?.toJson(),
        "updated_by": updatedBy?.toJson(),
        "kitty_id": kittyId,
        "referral_code": referralCode,
        "event_media": eventMedia != null
            ? List<dynamic>.from(eventMedia!.map((x) => x.toJson()))
            : [],
        "start_date": startDate?.toUtc().toIso8601String(),
        "end_date": endDate?.toUtc().toIso8601String(),
        "tickets": tickets == null
            ? []
            : List<dynamic>.from(tickets!.map((x) => x.toJson())),
        "social_accounts": socialAccounts == null
            ? []
            : List<dynamic>.from(socialAccounts!.map((x) => x.toJson())),
      };
}

class EventsModelSocialAccount {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int? eventId;
  final String? facebook;
  final String? youtube;
  final String? tiktok;
  final String? instagram;
  final String? twitter;
  final String? hearthis;
  final String? website;

  EventsModelSocialAccount({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.eventId = 0,
    this.facebook = '',
    this.youtube = '',
    this.tiktok = '',
    this.instagram = '',
    this.twitter = '',
    this.hearthis = '',
    this.website = '',
  });

  factory EventsModelSocialAccount.fromJson(Map<String, dynamic> json) =>
      EventsModelSocialAccount(
        id: json["ID"] ?? 0,
        createdAt: Event._parseDateTime(json["CreatedAt"]),
        updatedAt: Event._parseDateTime(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        eventId: json["event_id"] ?? 0,
        facebook: json["facebook"] ?? '',
        youtube: json["youtube"] ?? '',
        tiktok: json["tiktok"] ?? '',
        instagram: json["instagram"] ?? '',
        twitter: json["twitter"] ?? '',
        hearthis: json["hearthis"] ?? '',
        website: json["website"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": updatedAt?.toUtc().toIso8601String(),
        "DeletedAt": deletedAt,
        "event_id": eventId ?? 0,
        "facebook": facebook,
        "youtube": youtube,
        "tiktok": tiktok,
        "instagram": instagram,
        "twitter": twitter,
        "hearthis": hearthis,
        "website": website,
      };
}

class UpdatedBy {
    final int? id;
    final DateTime? createdAt;
    final DateTime? updatedAt;
    final dynamic deletedAt;
    final String? phoneNumber;
    final String? firstName;
    final String? secondName;
    final String? email;
    final int? idNumber;
    final double? balance;
    final String? birthDate;
    final String? countryCode;
    final String? county;
    final String? subCounty;
    final String? latitude;
    final String? longitude;
    final String? secondaryNumber;
    final String? profileUrl;
    final int? status;

    UpdatedBy({
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.phoneNumber,
        this.firstName,
        this.secondName,
        this.email,
        this.idNumber,
        this.balance,
        this.birthDate,
        this.countryCode,
        this.county,
        this.subCounty,
        this.latitude,
        this.longitude,
        this.secondaryNumber,
        this.profileUrl,
        this.status,
    });

    UpdatedBy copyWith({
        int? id,
        DateTime? createdAt,
        DateTime? updatedAt,
        dynamic deletedAt,
        String? phoneNumber,
        String? firstName,
        String? secondName,
        String? email,
        int? idNumber,
        double? balance,
        String? birthDate,
        String? countryCode,
        String? county,
        String? subCounty,
        String? latitude,
        String? longitude,
        String? secondaryNumber,
        String? profileUrl,
        int? status,
    }) => 
        UpdatedBy(
            id: id ?? this.id,
            createdAt: createdAt ?? this.createdAt,
            updatedAt: updatedAt ?? this.updatedAt,
            deletedAt: deletedAt ?? this.deletedAt,
            phoneNumber: phoneNumber ?? this.phoneNumber,
            firstName: firstName ?? this.firstName,
            secondName: secondName ?? this.secondName,
            email: email ?? this.email,
            idNumber: idNumber ?? this.idNumber,
            balance: balance ?? this.balance,
            birthDate: birthDate ?? this.birthDate,
            countryCode: countryCode ?? this.countryCode,
            county: county ?? this.county,
            subCounty: subCounty ?? this.subCounty,
            latitude: latitude ?? this.latitude,
            longitude: longitude ?? this.longitude,
            secondaryNumber: secondaryNumber ?? this.secondaryNumber,
            profileUrl: profileUrl ?? this.profileUrl,
            status: status ?? this.status,
        );

    factory UpdatedBy.fromRawJson(String str) => UpdatedBy.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory UpdatedBy.fromJson(Map<String, dynamic> json) => UpdatedBy(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        phoneNumber: json["phone_number"],
        firstName: json["first_name"],
        secondName: json["second_name"],
        email: json["email"],
        idNumber: json["id_number"],
        balance: json["balance"]?.toDouble(),
        birthDate: json["birth_date"],
        countryCode: json["country_code"],
        county: json["county"],
        subCounty: json["sub_county"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        secondaryNumber: json["secondary_number"],
        profileUrl: json["profile_url"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "phone_number": phoneNumber,
        "first_name": firstName,
        "second_name": secondName,
        "email": email,
        "id_number": idNumber,
        "balance": balance,
        "birth_date": birthDate,
        "country_code": countryCode,
        "county": county,
        "sub_county": subCounty,
        "latitude": latitude,
        "longitude": longitude,
        "secondary_number": secondaryNumber,
        "profile_url": profileUrl,
        "status": status,
    };
}
