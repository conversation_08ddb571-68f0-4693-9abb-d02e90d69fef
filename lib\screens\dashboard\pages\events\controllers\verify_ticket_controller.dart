import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/verify_ticket.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/verify_ticket.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

class VerifyTicketController extends GetxController implements GetxService {
  final pageController = PageController().obs;
  RxBool isVerifying = false.obs;
  final HttpService apiProvider = Get.find();
  RxString prefix = "".obs;
  final logger = Get.find<Logger>();
  RxBool isConfirming = false.obs;
  RxBool isScanning = false.obs;
  RxString lastScannedCode = "".obs;
  DateTime? lastScanTime;
  Future verifyTicket(int eventId, String code) async {
    // Prevent duplicate scans within 2 seconds
    if (lastScannedCode.value == code && lastScanTime != null) {
      final timeDiff = DateTime.now().difference(lastScanTime!);
      if (timeDiff.inSeconds < 2) return;
    }
    
    lastScannedCode.value = code;
    lastScanTime = DateTime.now();
    isVerifying(true);
    
    try {
      var response = await apiProvider.request(
        url: ApiUrls.VERIFYTICKET,
        method: Method.POST,
        params: {"code": code, "event_id": "$eventId"},
      ).timeout(const Duration(seconds: 30));

      if (response.data['status'] ?? false) {
        Get.to(() => VerifyConfirm(
              verify: VerifyTicketConfirm.fromJson(
                  response.data['data'] as Map<String, dynamic>),
              eventId: eventId,
              code: code,
            ));
      } else {
        _showErrorMessage(response.data['message'] ?? "Failed to verify ticket");
      }
    } catch (e) {
      logger.e('$e');
      if (e.toString().contains('TimeoutException')) {
        _showErrorMessage('Request timeout. Please try again.');
      } else {
        _showErrorMessage('Network error. Please check your connection.');
      }
    } finally {
      isVerifying(false);
    }
  }
  
  void _showErrorMessage(String message) {
    Get.snackbar(
      'Verification Failed',
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  Future verifyTicketConfirm(int eventId, String code, List<int> ids) async {
    if (ids.isEmpty) {
      Get.snackbar('Error', 'Please select at least one ticket to verify',
          backgroundColor: Colors.orange);
      return;
    }
    
    isConfirming(true);
    try {
      var response = await apiProvider.request(
        url: ApiUrls.VERIFYTICKETCONFIRM,
        method: Method.POST,
        params: {
          "code": code,
          "event_id": "$eventId",
          "transaction_ticket_ids": ids
        },
      ).timeout(const Duration(seconds: 30));

      if (response.data['status'] ?? false) {
        _clearPrefix();
        Get.back();
        Get.snackbar(
          'Verification Successful',
          '${response.data['message'] ?? "Tickets verified successfully"}',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      } else {
        _showErrorMessage(response.data['message'] ?? "Failed to verify ticket");
      }
    } catch (e) {
      logger.e(e);
      if (e.toString().contains('TimeoutException')) {
        _showErrorMessage('Request timeout. Please try again.');
      } else {
        _showErrorMessage('Network error. Please try again.');
      }
    } finally {
      isConfirming(false);
    }
  }
  
  void _clearPrefix() {
    if (prefix.value.contains('-')) {
      final parts = prefix.value.split("-");
      prefix.value = "${parts[0]}-";
    } else {
      prefix.value = "";
    }
  }
}
