const Map<String, String> chamaManagementGerman = {
  'my_chama': 'Mei<PERSON><PERSON>',
  'create_a_chama': 'Ein Chama erstellen',
  'chama_name': 'Chama-Name',
  
  'ready_to_grow_together': 'Bere<PERSON>, gemeinsam zu wachsen?',
  'start_your_chama_build_wealth': 'Starten Sie Ihr Chama, bauen Sie Wohlstand auf und unterstützen Sie sich gegenseitig.',
  'create_a_chama_title': 'Ein Chama erstellen',
  'chama_details': 'Chama\\n Details',
  'members': 'Mitglieder',
  'chama_name_label': 'Chama-Name',
  'chama_name_hint': 'z.B. Wasafi Chama',
  'chama_name_required': 'Chama-Name darf nicht leer sein',
  'chama_name_length': 'Chama-Name muss zwischen 5 und 300 Zeichen lang sein',
  'chama_description': 'Chama-Beschreibung',
  'chama_description_hint': 'z.B. Zweck des Chamas',
  'whatsapp_group_link': 'WhatsApp-Gruppenlink (optional)',
  'group_link': 'Gruppenlink',
  'enter_referer_code': 'Empfehlungscode eingeben (Optional)',
  'referral_code': 'Empfehlungscode',
  'referral_code_hint': 'z.B. 12',
  'enter_chama_email': 'Chama-E-Mail eingeben',
  'email_hint': 'z.B. <EMAIL>',
  'how_much_contribute': 'Wie viel trägt jedes Mitglied bei',
  'amount_hint': '1000',
  'invite_chama_members': 'Chama-Mitglieder einladen',
  'update_member_details': 'Mitgliederdetails aktualisieren',
  'enter_first_name': 'Vornamen eingeben',
  'enter_last_name': 'Nachnamen eingeben',
  'payer_phone_number': 'Telefonnummer des Zahlers',
  'chama_member_phone_number': 'Telefonnummer des Chama-Mitglieds',
  'pay_penalty': 'Strafe bezahlen',
  'select_penalty_to_pay': 'Zu zahlende Strafe auswählen',
  'it_looks_like_has_penalties': 'Es sieht so aus, als ob',
  'has_penalties': 'hat',
  'penalties': 'Strafen.',
  'meetings': 'Sitzungen',
  'meetings_description': 'Hier sind die vom Chama-Administrator festgelegten Sitzungen',
  'add_meeting': 'Sitzung hinzufügen',
  'calender': 'Kalender',
  'view_all_penalties': 'Alle anzeigen',
  'view_all_transactions': 'Alle anzeigen',
  'transactions': 'Transaktionen',
  'issue_penalty_to': 'Strafe verhängen gegen',
  'update_member_info': 'Mitgliederinfo aktualisieren',
  'remove_as_signatory': 'als \\n Unterzeichner entfernen',
  'view_all_member_transactions': 'Alle anzeigen',
  'member_transactions': 'Transaktionen',
  'issue_penalty_to_member': 'Strafe verhängen gegen',
  'view_all_member_penalties': 'Alle anzeigen',
  'member_penalties': 'Strafen',
  'remove_member': 'Entfernen',
  'member_phone_required': 'Bitte Telefonnummer des Chama-Mitglieds eingeben',
  'penalties_uppercase': 'STRAFEN',
  'chama_title': 'CHAMA-TITEL:',
  'member_label': 'MITGLIED:',
  'penalty_label': 'STRAFE:',
  'meeting': 'Sitzung',
  'penalty': 'Strafe',
  'failed_to_load_chamas': 'Chamas konnten nicht geladen werden. Bitte erneut versuchen.',
  'members_count': 'Mitglieder',
  'add_chama_members': 'Chama-Mitglieder hinzufügen',
  'add_members': 'Mitglieder hinzufügen',
  'an_error_occurred_adding_member': 'Beim Hinzufügen des Mitglieds ist ein Fehler aufgetreten.',
  'add_chama_member': 'Chama-Mitglied hinzufügen',
  'set_receiving_order': 'Empfangsreihenfolge festlegen',
  'issue_penalties': 'Strafe(n) verhängen',
  'update_penalty': 'Strafe aktualisieren',
  'set_penalties': 'Strafen festlegen',
  'create_penalties_description': 'Erstellen Sie Strafen, die später an Mitglieder verhängt werden können',
  'select_common_penalties': 'Aus häufigen Strafen auswählen',
  'click_to_select_penalty': 'Klicken, um Strafe auszuwählen',
  'enter_penalty_name': 'Strafenname eingeben',
  'penalty_name_hint': 'z.B. Sitzung verpasst',
  'enter_penalty_description': 'Strafenbeschreibung eingeben (Optional)',
  'penalty_description_hint': 'z.B. Beschreibung',
  'enter_penalty_amount': 'Strafenbetrag eingeben',
  'penalty_amount_hint': 'z.B. 100',
  'common_penalties': 'HÄUFIGE STRAFEN',
  'chama_settings': 'Chama-Einstellungen',
  'settings_description': 'Dies sind die vom Chama-Administrator festgelegten Einstellungen',
  'beneficiary_per_cycle': 'Begünstigter pro Zyklus',
  'beneficiary_percentage': 'Begünstigtenprozentsatz',
  'signatories': 'Unterzeichner',
  'signatory_threshold': 'Unterzeichner-Schwellenwert',
  'get_to_know_beneficiaries': 'Erfahren Sie mehr über Begünstigte.',
  'beneficiaries_per_cycle': 'Begünstigte pro Zyklus:',
  'get_to_know_signatories': 'Erfahren Sie mehr über Unterzeichner',
  'update_button': 'Aktualisieren',
  'save_button': 'Speichern',
  'documents': 'Dokumente',
  'share_documents_description': 'Teilen Sie beliebige Dokumente mit Ihren Chama-Mitgliedern',
  'error_getting_documents': 'Fehler beim Abrufen der Dokumente',
  'add_document': 'Dokument hinzufügen',
  'signatories_title': 'Unterzeichner',
  'signatories_description': 'Hier sind die vom Chama-Administrator festgelegten Unterzeichner',
  'add_signatory': 'Unterzeichner hinzufügen',
  'first_name': 'Vorname',
  'last_name': 'Nachname',
  'whatsapp_number': 'WhatsApp-Nummer',
  'chama': 'CHAMA',
  'created_by': 'Erstellt von: ',
  'chama_email': 'Chama-E-Mail',
  'enter_your_chama_email': 'Ihre Chama-E-Mail eingeben',
  'transfer_from_chama_account': 'Gelder vom Chama-Konto übertragen',
  'transfer_from_penalty_kitty': 'Vom Strafen-Kitty übertragen',
  'transfer_from_penalty': 'Von Strafe übertragen',
  'transfer_from_chama': 'Vom Chama übertragen',
  'enter_beneficiary_phone_number': 'Telefonnummer des Begünstigten eingeben',
  'enter_reason_for_transaction': 'Grund für Transaktion eingeben',
  'make_a_transfer': 'Eine Übertragung vornehmen',
  'make_transactions_with_ease': 'Transaktionen einfach durchführen',
  'group_name': 'Gruppenname',
  'please_enter_group_name': 'Bitte Gruppennamen eingeben',
  'group_members': 'Gruppenmitglieder',
  'add_members_to_group': 'Mitglieder zur Gruppe hinzufügen',
  'delete_selected_members': 'Ausgewählte Mitglieder löschen',
  'edit_member': 'Mitglied bearbeiten',
  'phone_number_format_hint': 'Format: 0722XXX oder 722XXX',
  'delete_member': 'Mitglied löschen',
  'please_add_one_member_group': 'Bitte mindestens ein Mitglied zur Gruppe hinzufügen',
  'transfer_funds_from_chama_account': 'Gelder vom Chama-Konto übertragen',
};