const Map<String, String> commonEnglish = {
  'kitty_media': "Kitty Media",
  'filter': "Filter",
  'search': "Search",
  'attendees': "attendees",
  'loading': 'loading..',
  'back': 'Back',
  'next': 'Next',
  'save': 'Save',
  'cancel': 'Cancel',
  'finish': 'Finish',
  'confirm': 'CONFIRM',
  'yes': 'Yes',
  'no': 'No',
  'error': 'Error',
  'success': 'Success',
  'info': 'Info',
  'add': 'Add',
  'edit': 'Edit',
  'delete': 'Delete',
  'remove': 'Remove',
  'update': 'Update',
  'view': 'View',
  'close': 'Close',
  'processing': 'Processing...',
  'apply': 'Apply',
  'pick': 'Pick',
  'all': 'All',
  'upcoming': 'Upcoming',
  'past': 'Past',
  'this_week': 'This Week',
  'this_month': 'This Month',
  'this_year': 'This Year',
  'last_month': 'Last Month',
  'last_year': 'Last Year',
  'custom_range': 'Custom Range',
  'select_date_range': 'Select Date Range',
  'select_date': 'Select Date',
  'select_time': 'Select Time',
  'select_category': 'Select Category',
  'select_status': 'Select Status',
  'select_location': 'Select Location',
  'select_event_type': 'Select Event Type',
  'select_event_mode': 'Select Event Mode',
  'select_event_status': 'Select Event Status',
  'category': 'Category:',
  'category_is_required': 'Category is required',
  'date_range': 'Date Range:',
  'pick_a_date_range': 'pick a date range',
  'event_status': 'Event Status:',
  'field_cannot_be_empty': 'This field can\'t be empty',
  'this_field_required': 'This field can\'t be empty',
  'this_field_cannot_be_empty': 'This field can\'t be empty',
  'filed_cannot_be_empty': 'Filed cannot be empty',
  'required': 'Required',
  'invalid_number': 'Invalid number',
  'invalid_amount': 'Invalid amount',
  'invalid_phone_number': 'Invalid phone number',
  'please_enter_valid_phone_number': 'Please enter a valid phone number',
  'enter_valid_phone_number': 'Enter valid phone number',
  'phone_number_required': 'Please enter payer phone number',
  'phone_number_required_validation': 'Phone Number is required',
  'phone_number_is_required': 'Phone Number is required',
  'please_enter_phone_number': 'Please enter phone number',
  'email_required': 'Email cannot be empty',
  'email_invalid': 'Enter a valid email address',
  'email_required_validation': 'Email is required',
  'enter_valid_email': 'Please enter a valid email address',
  'enter_email': 'Enter email',
  'email_cannot_be_empty': 'Email cannot be empty',
  'enter_valid_email_address': 'Enter a valid email address',
  'amount_required': 'Amount is required',
  'please_enter_amount': 'Please enter amount',
  'enter_valid_amount': 'Enter valid amount',
  'title_required': 'Title is required',
  'description_required': 'Description is required',
  'reason_required': 'Reason is required',
  'first_name_required': 'First name is required',
  'first_name_min_length': 'First name must be at least 2 characters',
  'first_name_letters_only': 'First name can only contain letters and spaces',
  'second_name_required': 'Second name is required',
  'second_name_min_length': 'Second name must be at least 2 characters',
  'second_name_letters_only': 'Second name can only contain letters and spaces',
  'account_name_required': 'Account Name is required',
  'transaction_code': 'Transaction Code',
  'enter_transaction_code': 'Enter transaction code',
  'search_your_transactions': 'Search your transactions',
  'search_kitty_transactions': 'Search kitty transactions',
  'search_chama_transactions': 'Search chama transactions',
  'search_event_transactions': 'Search event transactions',
  'paybill_required': 'Paybill is required',
  'paybill_validation': 'Paybill can only contain letters and numbers',
  'paybill_is_required': 'Paybill is required',
  'please_enter_paybill_number': 'Please enter paybill number',
  'account_number_required': 'Account Number is required',
  'account_number_validation': 'Account Number can only contain letters and numbers',
  'account_number_is_required': 'Account Number is required',
  'please_enter_account_number': 'Please enter account number',
  'till_number_required': 'Till Number is required',
  'till_number_is_required': 'Till number is required',
  'please_enter_till_number': 'Please enter till number',
  'bank_account_required': 'Please enter your bank account number',
  'please_enter_bank_account_number': 'Please enter your bank account number',
  'please_select_a_bank': 'Please select a bank',
  'end_date_required': 'End Date is required',
  'please_fix_following_errors': 'Please fix the following errors:',
  'check_your_values': 'Check Your Values',
  'fill_all_values': 'Fill in all values',
  'please_fill_required_fields': 'Please fill all required fields',
  'error_try_again': 'Error, Please try again',
  'try_again': 'Try Again',
  'retry': 'Retry',
  'go_back': 'Go Back',
  'go_back_home': 'Go Back Home',
  'done': 'Done',
  'okay': 'Okay',
  'submit': 'Submit',
  'submitting': 'Submitting...',
  'submitting_ellipsis': 'Submitting...',
  'uploading': 'Uploading...',
  'uploading_ellipsis': 'Uploading...',
  'sending': 'Sending...',
  'processing_payment': 'Processing Payment',
  'processing_payment_please_wait': 'Processing Payment.... Please wait',
  'please_wait_confirm_payment': 'Please wait while we confirm your payment...',
  'loading_more': 'Loading more',
  'loading_contacts': 'Loading contacts...',
  'loading_categories': 'Loading categories...',
  'loading_kitties': 'loading kitties...',
  'loading_more_kitties': 'loading more kitties...',
  'loading_more_members': 'Loading more members',
  'loading_kitty_data': 'Loading Kitty Data',
  'loading_kitty_data_message': 'Loading kitty data...',
  'searching_kitties': 'Searching kitties...',
  'reached_end_of_list': 'You have reached the end of the list.',
  'reached_end_list': 'You have reached the end of the list.',
  'no_more_kitties_to_load': 'No more kitties to load',
  'refresh': 'Refresh',
  'connection_error': 'Connection Error',
  'check_internet_connection': 'Please check your internet connection and try again.',
  'failed_to_load_data': 'Failed to load data',
  'an_error_occurred': 'An error occurred while adding member.',
  'unknown_error_occurred': 'An unknown error occurred',
  'error_toast': 'Error',
  'success_toast': 'success',
  'thank_you': 'Thank You!',
  'unknown': 'Unknown',
  'n_a': 'N/A',
  'now': 'Now',
  'ago': 'ago',
  'complete': 'complete',
  'percent_complete': '{percent}% complete',
  'attempt_of_5': 'Attempt',
  'of_5': 'of 5',
  'maximum_files_allowed': 'Maximum {count} files allowed',
  'uploading_filename': 'Uploading {filename}...',
  'could_not_launch_url': 'Could not launch the url',
  'could_not_launch_url_with_param': 'Could not launch {url}',
  'could_not_launch_phone': 'Could not launch {phone}',
  'could_not_launch_email': 'Could not launch {email}',
  'link_copied_to_clipboard': 'Link copied to clipboard',
  'copied': 'Copied: ',
  'share': 'Share',
  'export': 'Export',
  'create': 'Create',
  'start': 'Start',
  'see_all': 'See all',
  'clear_all': 'Clear All',
  'clear_all_filters': 'Clear All Filters',
  'clear_filters': 'Clear Filters',
  'apply_filters': 'Apply Filters',
  'toggle_filters': 'Toggle filters',
  'select_option': 'Select Option',
  'select_contact': 'Select Contact',
  'select_contacts': 'Select Contacts',
  'select_contacts_title': 'Select Contacts',
  'cancel_selection': 'Cancel Selection',
  'no_number': 'No Number',
  'select': 'Select',
  'click_link': 'Click link',
  'to_pay': 'to pay',
  'pay': 'Pay',
  'contribute': 'Contribute',
  'transfer': 'Transfer',
  'withdraw': 'Withdraw',
  'top_up': 'Top Up',
  'confirm_transfer': 'Confirm Transfer',
  'confirm_removal': 'Confirm Removal',
  'confirm_deletion': 'Confirm Deletion',
  'confirm_affiliate_registration': 'Confirm Your Affiliate Registration Details',
  'transfer_confirmation': 'Transfer Confirmation',
  'delete_document_confirmation': 'Are you sure you want to delete this document?',
  'are_you_sure_delete_signatory': 'Are you sure you want to delete this signatory?',
  'are_you_sure_remove': 'Are you sure you want to remove',
  'are_you_sure_delete_beneficiary': 'Are you sure you want to delete',
  'are_you_sure_deactivate': 'Are you sure you want to deactivate this kitty?',
  'are_you_sure_delete_member': 'Are you sure you want to delete this member?',
  'are_you_sure_delete_selected_members': 'Are you sure you want to delete all selected members?',
  'from_group': 'from this group? This action cannot be undone.',
  'duplicate_found': 'Duplicate found',
  'link_handling': 'Link Handling',
  'allow_onekitty_open_links': 'Allow OneKitty to automatically open supported links?',
  'dont_ask_again': 'Don\'t ask again',
  'later': 'LATER',
  'allow': 'ALLOW',
  'media_not_available': 'Media not available',
  'no_media_files_selected': 'No media files selected',
  'upload_queue': 'Upload Queue',
  'upload_media': 'Upload Media',
  'upload_files': 'Upload Files',
  'add_media_url': 'Add Media URL',
  'media_url': 'Media URL',
  'media_type': 'Media Type',
  'media_upload': 'Media Upload',
  'add_url': 'Add URL',
  'supporting_evidence': 'Supporting Evidence',
  'pdf': 'PDF',
  'type': 'Type',
  'title': 'Title',
  'description': 'Description',
  'details': 'Details',
  'name': 'Name',
  'email': 'Email',
  'phone_number': 'Phone Number',
  'amount': 'Amount',
  'date': 'Date',
  'time': 'Time',
  'status': 'STATUS:',
  'status_label': 'Status',
  'reason': 'REASON:',
  'amount_paid': 'AMOUNT PAID:',
  'issued_on': 'ISSUED ON:',
  'created_on': 'Created on:',
  'joined_on': 'Joined On:',
  'created': 'created',
  'created_success': 'created',
  'end_date': 'End Date:',
  'end_date_label': 'end Date',
  'expired': 'expired',
  'ends': 'Ends:',
  'ends_in': 'Ends In',
  'account_no': 'AccNo',
  'account': 'Account',
  'reference': 'Reference',
  'mode': 'Mode',
  'to': 'To',
  'from': 'From',
  'balance': 'Balance: ',
  'current_balance': 'Current Balance',
  'new_balance': 'New Balance',
  'remaining_balance': 'Remaining Balance',
  'total_deduction': 'Total Deduction',
  'amount_breakdown': 'Amount Breakdown',
  'transfer_amount': 'Transfer Amount',
  'charges': 'Charges',
  'platform_fee': 'Platform Fee',
  'platform_fees': 'Platform Fees',
  'transaction_charge': 'Transaction Charge',
  'third_party_fee': 'Third Party Fee',
  'third_party_charges': 'Third Party Charges',
  'total_charges': 'Total Charges',
  'amount_to_recipient': 'Amount to Recipient',
  'view_charges_breakdown': 'View Charges Breakdown',
  'transfer_summary': 'Transfer Summary',
  'balance_summary': 'Balance Summary',
  'recipient_details': 'Recipient Details',
  'transfer_failed': 'Transfer failed',
  'confirmation_failed': 'Confirmation failed',
  'was_successfully': ' was successfully ',
  'transaction_updated_successfully': 'Transaction updated successfully',
  'report_submitted_successfully': 'Report submitted successfully',
  'failed_submit_report': 'Failed to submit report',
  'error_submitting_report': 'Error submitting report:',
  'whatsapp_group_connected_successfully': 'WhatsApp group connected successfully',
  'failed_to_connect_whatsapp_group': 'Failed to connect WhatsApp group',
  'an_error_occurred_error': 'An error occurred: {error}',
  'sms_successfully_sent': 'SMS was successfully sent',
  'contribution_successfully_received': ' Your contribution was successfully received',
  'amount_received': 'Amount received:',
  'view_payment_details': 'View Payment Details',
  'settlement_initiated': 'SETTLEMENT INITIATED',
  'checking': 'checking',
  'read': 'Read',
  'unread': 'Unread',
  'active': "Active",
  'ended': "Ended",
  'pending_review': "Pending Review",
  'blocked': 'Blocked',
  'pending': 'PENDING',
  'completed': 'COMPLETED',
  'failed': 'FAILED',
  'removed': 'REMOVED',
  'draft': 'Draft',
  'inactive': 'Inactive',
  'primary': 'Primary',
  'virtual': 'VIRTUAL',
  'physical': 'PHYSICAL',
  'membership_reference': 'Membership Reference', 
};