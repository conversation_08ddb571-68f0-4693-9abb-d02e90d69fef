import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/local_auth.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty/utils/cache_keys.dart';

void main() {
  group('Biometric Authentication Tests', () {
    late LocalAuthentication auth;
    late GetStorage storage;

    setUp(() {
      auth = LocalAuthentication();
      GetStorage.init();
      storage = GetStorage();
    });

    tearDown(() {
      storage.erase();
    });

    test('should check if device supports biometrics', () async {
      // Mock the platform channel
      const MethodChannel channel = MethodChannel('plugins.flutter.io/local_auth');
      
      // Set up mock responses
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'canCheckBiometrics':
            return true;
          case 'getAvailableBiometrics':
            return ['fingerprint'];
          default:
            return null;
        }
      });

      final canCheck = await auth.canCheckBiometrics;
      expect(canCheck, isTrue);

      final availableBiometrics = await auth.getAvailableBiometrics();
      expect(availableBiometrics, isNotEmpty);
    });

    test('should handle biometric authentication failure gracefully', () async {
      const MethodChannel channel = MethodChannel('plugins.flutter.io/local_auth');
      
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'canCheckBiometrics':
            return true;
          case 'getAvailableBiometrics':
            return ['fingerprint'];
          case 'authenticate':
            throw PlatformException(
              code: 'NotAvailable',
              message: 'Biometric authentication not available',
            );
          default:
            return null;
        }
      });

      expect(
        () async => await auth.authenticate(
          localizedReason: 'Test authentication',
          options: const AuthenticationOptions(biometricOnly: true),
        ),
        throwsA(isA<PlatformException>()),
      );
    });

    test('should store and retrieve biometric preferences correctly', () {
      // Test storing biometric preference
      storage.write(CacheKeys.allowsBiometric, true);
      expect(storage.read(CacheKeys.allowsBiometric), isTrue);

      // Test storing encryption keys
      storage.write(CacheKeys.encryption, 'test_encryption_key');
      storage.write(CacheKeys.iv, 'test_iv');
      storage.write(CacheKeys.encrypted, 'test_encrypted_data');

      expect(storage.read(CacheKeys.encryption), equals('test_encryption_key'));
      expect(storage.read(CacheKeys.iv), equals('test_iv'));
      expect(storage.read(CacheKeys.encrypted), equals('test_encrypted_data'));
    });

    test('should handle missing biometric enrollment', () async {
      const MethodChannel channel = MethodChannel('plugins.flutter.io/local_auth');
      
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'canCheckBiometrics':
            return true;
          case 'getAvailableBiometrics':
            return [];
          default:
            return null;
        }
      });

      final availableBiometrics = await auth.getAvailableBiometrics();
      expect(availableBiometrics, isEmpty);
    });

    test('should validate encryption/decryption flow', () {
      // This would test the actual encryption logic used in the app
      // For now, just verify the keys are stored properly
      const testPassword = 'test_password_123';
      const testEncryptionKey = 'test_encryption_key_base64';
      const testIV = 'test_iv_base64';
      const testEncrypted = 'test_encrypted_password_base64';

      storage.write(CacheKeys.encryption, testEncryptionKey);
      storage.write(CacheKeys.iv, testIV);
      storage.write(CacheKeys.encrypted, testEncrypted);

      expect(storage.read(CacheKeys.encryption), equals(testEncryptionKey));
      expect(storage.read(CacheKeys.iv), equals(testIV));
      expect(storage.read(CacheKeys.encrypted), equals(testEncrypted));
    });
  });
}
