// Unified Transfer Controller
// Manages state and business logic for all transfer types

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/configs/payment_channels.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/models/chama/transfer_req.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/utils/cache_keys.dart';
import '../models/transfer_type.dart';
import '../services/transfer_service.dart';

class TransferController extends GetxController {
  final TransferService _transferService = Get.isRegistered<TransferService>() ? Get.find<TransferService>() : Get.put(TransferService()) ;
  final Logger _logger = Get.find<Logger>();
  final GetStorage _box = Get.find<GetStorage>();

  // Configuration
  late TransferPageConfig _config;
  TransferPageConfig get config => _config;

  // Form state
  final RxInt currentPage = 0.obs;
  final RxInt selectedProvider = 63902.obs;
  final Rx<PaymentChannels?> selectedBank = Rx<PaymentChannels?>(null);

  // Loading states
  final RxBool isTransferring = false.obs;
  final RxBool isConfirming = false.obs;

  // Form controllers
  final amountController = TextEditingController();
  final phoneController = TextEditingController();
  final reasonController = TextEditingController();
  final paybillController = TextEditingController();
  final accountNumberController = TextEditingController();
  final tillNumberController = TextEditingController();
  final bankAccountController = TextEditingController();
  final  paymentChannel = Get.isRegistered<PaymentChannel>() ? Get.find<PaymentChannel>() :  Get.put(PaymentChannel());
  
  // Tab controller for transfer modes
  late TabController tabController;

  // Transfer data
  final transferData = <String, dynamic>{}.obs;

  void initialize(TransferPageConfig config) {
    _config = config;
    _resetForm();
    _logger.i('TransferController initialized for ${config.transferType.name} transfer');
  }
  
  void initializeTabController(TickerProvider vsync) {
    tabController = TabController(initialIndex: 0, length: paymentChannel.paymentGateways().length, vsync: vsync);
  }

  /// Initiate transfer request
  Future<bool> initiateTransfer({
    required int amount,
    required String transferMode,
    required String reason,
    String? phoneNumber,
    String? paybill,
    String? accNo,
    String? till,
    String? bankAccount,
  }) async {
    isTransferring(true);
    
    try {
      final request = _buildTransferRequest(
        amount: amount,
        transferMode: transferMode,
        reason: reason,
        phoneNumber: phoneNumber,
        paybill: paybill,
        accNo: accNo,
        till: till,
        bankAccount: bankAccount,
      );

      final result = await _transferService.initiateTransfer(
        config: _config,
        request: request,
      );

      if (result.success) {
        // Store both the API response and the original request data
        transferData.value = {
          ...result.data,
          'original_request': request.toJson(),
        };
        return true;
      } else {
        Get.snackbar('error'.tr, result.message, backgroundColor: Colors.red);
        return false;
      }
    } catch (e) {
      _logger.e('Transfer initiation error: $e');
      Get.snackbar('error'.tr, 'transfer_failed'.tr, backgroundColor: Colors.red);
      return false;
    } finally {
      isTransferring(false);
    }
  }

  /// Confirm transfer
  Future<bool> confirmTransfer() async {
    isConfirming(true);
    
    try {
      final result = await _transferService.confirmTransfer(
        config: _config,
        transferData: transferData.value,
      );

      if (result.success) {
        Get.snackbar('success'.tr, result.message, backgroundColor: Colors.green);
        Get.back(); // Return to previous screen
        return true;
      } else {
        Get.snackbar('error'.tr, result.message, backgroundColor: Colors.red);
        return false;
      }
    } catch (e) {
      _logger.e('Transfer confirmation error: $e');
      Get.snackbar('error'.tr, 'confirmation_failed'.tr, backgroundColor: Colors.red);
      return false;
    } finally {
      isConfirming(false);
    }
  }

  /// Build transfer request based on type
  TransferRequest _buildTransferRequest({
    required int amount,
    required String transferMode,
    required String reason,
    String? phoneNumber,
    String? paybill,
    String? accNo,
    String? till,
    String? bankAccount,
  }) {
    final user = _getUserForTransferType();
    
    _logger.i('Building ${_config.transferType.name} transfer request');
    _logger.i('Entity ID: ${_config.entityId}');
    _logger.i('User ID: ${user?.id}');
    
    if (_config.transferType == TransferType.event) {
      // Event transfer payload format
      final request = TransferRequest(
        amount: amount,
        userId: user?.id,
        kittyId: _config.entityId, // Use kittyId for events
        channelCode: selectedProvider.value,
        recipientAccountNumber: _getRecipientAccount(transferMode, phoneNumber, till, paybill, bankAccount),
        recipientAccountRef: transferMode == "PAYBILL" ? accNo : null,
        reason: reason,
        transferMode: transferMode,
        latitude: _box.read(CacheKeys.lat) ?? "",
        longitude: _box.read(CacheKeys.long) ?? "",
        deviceId: _box.read(CacheKeys.deviceId) ?? "",
        deviceModel: _box.read(CacheKeys.deviceModel) ?? "",
      );
      _logger.i('Event request built with kittyId: ${request.kittyId}');
      return request;
    } else {
      // Chama transfer payload format
      final memberId = _getMemberId();
      final request = TransferRequest(
        amount: amount,
        userId: user?.id,
        chamaId: _config.entityId,
        memberId: memberId,
        channelCode: selectedProvider.value,
        recipientAccountNumber: _getRecipientAccount(transferMode, phoneNumber, till, paybill, bankAccount),
        recipientAccountRef: transferMode == "PAYBILL" ? accNo : null,
        reason: reason,
        transferMode: transferMode,
        latitude: _box.read(CacheKeys.lat) ?? "",
        longitude: _box.read(CacheKeys.long) ?? "",
        deviceId: _box.read(CacheKeys.deviceId) ?? "",
        deviceModel: _box.read(CacheKeys.deviceModel) ?? "",
        isPenaltyKitty: _config.isPenaltyTransfer,
      );
      _logger.i('Chama request built with chamaId: ${request.chamaId}, memberId: $memberId');
      return request;
    }
  }

  // int? _getChannelCode() {
  //   switch (selectedProvider.value) {
  //     case "M-PESA": return 63902;
  //     case "SasaPay": return 0;
  //     case "AirtelMoney": return 63903;
  //     case "Card": return 55;
  //     case "Tkash": return 63907;
  //     case "BANK": return selectedBank.value?.channelCode;
  //     default: return null;
  //   }
  // }

  /// Get recipient account based on transfer mode
  String _getRecipientAccount(String transferMode, String? phoneNumber, String? till, String? paybill, String? bankAccount) {
    switch (transferMode) {
      case "WALLET": return phoneNumber ?? "";
      case "TILL": return till ?? "";
      case "PAYBILL": return paybill ?? "";
      case "BANK": return bankAccount ?? "";
      default: return "";
    }
  }

  /// Get user based on transfer type
  dynamic _getUserForTransferType() {
    switch (_config.transferType) {
      case TransferType.event:
        return Get.find<Eventcontroller>().getLocalUser();
      case TransferType.chama:
      case TransferType.penalty:
        return Get.find<ChamaController>().getLocalUser();
    }
  }

  /// Get member ID for chama transfers
  int? _getMemberId() {
    if (_config.transferType == TransferType.chama || _config.transferType == TransferType.penalty) {
      try {
        final chamaDataController = Get.find<ChamaDataController>();
        return chamaDataController.chama.value.member?.id;
      } catch (e) {
        _logger.w('Could not get member ID: $e');
        return null;
      }
    }
    return null;
  }

  /// Reset form
  void _resetForm() {
    currentPage.value = 0;
    selectedProvider.value = 63902;
    selectedBank.value = null;
    
    amountController.clear();
    phoneController.clear();
    reasonController.clear();
    paybillController.clear();
    accountNumberController.clear();
    tillNumberController.clear();
    bankAccountController.clear();
    
    transferData.clear();
  }

  /// Clean up all state when leaving the page
  void cleanupState() {
    _logger.i('Cleaning up TransferController state for ${_config.transferType.name}');
    
    // Reset all reactive variables
    currentPage.value = 0;
    selectedProvider.value = 63902;
    selectedBank.value = null;
    isTransferring.value = false;
    isConfirming.value = false;
    
    // Clear all text controllers
    amountController.clear();
    phoneController.clear();
    reasonController.clear();
    paybillController.clear();
    accountNumberController.clear();
    tillNumberController.clear();
    bankAccountController.clear();
    
    // Clear transfer data
    transferData.clear();
    
    // Reset tab controller if it exists
    if (tabController.hasListeners) {
      tabController.animateTo(0);
    }
    
    _logger.i('TransferController state cleaned up successfully');
  }

  @override
  void onClose() {
    _logger.i('Disposing TransferController for ${_config.transferType.name}');
    
    // Clean up state first
    cleanupState();
    
    // Dispose controllers
    amountController.dispose();
    phoneController.dispose();
    reasonController.dispose();
    paybillController.dispose();
    accountNumberController.dispose();
    tillNumberController.dispose();
    bankAccountController.dispose();
    
    super.onClose();
  }
}