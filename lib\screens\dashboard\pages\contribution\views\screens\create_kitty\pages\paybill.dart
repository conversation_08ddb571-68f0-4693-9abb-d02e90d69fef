import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';

class PayBill extends StatelessWidget {
  final TextEditingController paybillController;
  final TextEditingController accountController;
  final TextEditingController dateController;
  final TextEditingController timeController;
  const PayBill(
      {super.key,
      required this.paybillController,
      required this.accountController,
      required this.dateController,
      required this.timeController});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "mpesa_paybill_label".tr,
              style: context.titleText?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
          ),
          CustomTextField(
            labelText: "paybill_number_label".tr,
            controller: paybillController,
            hintText: "paybill_hint".tr,
            showNoKeyboard: true,
            allowAlphanumeric: true,
            isRequired: true,
            validator: (value) {
              if (value!.isEmpty) {
                return "enter_paybill_number_validation".tr;
              } else if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
                return "paybill_letters_numbers_only".tr;
              } else {
                return null;
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "account_number_label".tr,
              style: context.titleText?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
          ),
          CustomTextField(
            labelText: "account_number_label".tr,
            controller: accountController,
            isRequired: true,
            hintText: "account_number_hint".tr,
            validator: (value) {
              if (value!.isEmpty) {
                return "enter_account_number_validation".tr;
              } else {
                return null;
              }
            },
          ),
          SingleLineRow(text: "expected_contribution_end_date_label".tr, popup: KtStrings.endDateInfo,),
          DatePicker(date: dateController, time: timeController, isAllow: true,)
        ],
      ),
    );
  }
}
