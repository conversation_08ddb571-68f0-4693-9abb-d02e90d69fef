import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/view_single_event.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;

// Data class for the isolate to work with
class ExcelData {
  final bool isKitty;
  final List<dynamic> transactions;
  final String title;
  final int? eventId;
  final String? eventTitle;
  final String outputPath;

  ExcelData({
    required this.isKitty,
    required this.transactions,
    required this.title,
    this.eventId,
    this.eventTitle,
    required this.outputPath,
  });
}

// Process Excel in isolate
Future<String> _processExcelInIsolate(ExcelData data) async {
  return await compute(_createExcelInIsolate, data);
}

String _createExcelInIsolate(ExcelData data) {
  final xlsio.Workbook workbook = xlsio.Workbook();
  final xlsio.Worksheet sheet = workbook.worksheets[0];
  
  final xlsio.Range titleRange = sheet.getRangeByName('A1:E1');
  titleRange.merge();
  
  data.isKitty
      ? titleRange.setText(
          '${data.eventTitle ?? data.title} Transaction Report')
      : titleRange.setText(
          '${data.title} Transaction Report');
          
  titleRange.cellStyle.bold = true;
  titleRange.cellStyle.fontSize = 14;
  titleRange.cellStyle.hAlign = xlsio.HAlignType.center;

  sheet.getRangeByName('A2').setText('Date');
  sheet.getRangeByName('B2').setText('Details');
  sheet.getRangeByName('C2').setText('Amount');
  sheet.getRangeByName('D2').setText('Reference');
  sheet.getRangeByName('E2').setText('Type');

  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  DateTime recent = data.transactions.first.createdAt!;
  DateTime nrecent = data.transactions.first.createdAt!;

  for (var transaction in data.transactions) {
    if (transaction.createdAt!.isAfter(recent)) {
      recent = transaction.createdAt!;
    }
    if (transaction.createdAt!.isBefore(nrecent)) {
      nrecent = transaction.createdAt!;
    }
  }
  
  for (int i = 0; i < data.transactions.length; i++) {
    sheet.getRangeByName('A${i + 3}').setText(
          dateformat.format(data.transactions[i].createdAt!.toLocal()),
        );
    data.isKitty
        ? sheet.getRangeByName('B${i + 3}').setText(
            '${data.transactions[i].firstName ?? ""} ${data.transactions[i].secondName ?? ''}__${data.transactions[i].phoneNumber ?? ''}')
        : sheet.getRangeByName('B${i + 3}').setText(
            '${data.transactions[i].firstName ?? ""} ${data.transactions[i].secondName ?? ''}__${data.transactions[i].phoneNumber ?? ''}');
    sheet
        .getRangeByName('C${i + 3}')
        .setText(data.transactions[i].amount.toString());
    sheet
        .getRangeByName('D${i + 3}')
        .setText(data.transactions[i].transactionCode?.toString() ?? '');
    sheet.getRangeByName('E${i + 3}').setText(data.transactions[i].typeInOut);
  }
  
  for (int col = 1; col <= 5; col++) {
    sheet.autoFitColumn(col);
  }
  
  sheet.protect('@OneKitty');

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  try {
    final String filename = data.isKitty
        ? '${data.outputPath}/${data.eventTitle ?? data.title}_${DateFormat('MMM dd').format(nrecent.toLocal())}_${DateFormat('MMM dd').format(recent.toLocal())}_${data.eventId != null ? 'Event' : 'Kitty'}Transactions.xlsx'
        : '${data.outputPath}/${data.title}_${DateFormat('MMM dd').format(nrecent.toLocal())}_${DateFormat('MMM dd').format(recent.toLocal())}_ChamaTransactions.xlsx';
    
    // Clean the filename to remove invalid characters
    final cleanFilename = filename.replaceAll(RegExp(r'[*?:"<>|]'), '_');
    final File file = File(cleanFilename);
    
    // Write the file
    file.writeAsBytesSync(bytes, flush: true);
    
    return cleanFilename;
  } catch (e) {
    print('Error creating Excel file: $e');
    rethrow;
  }
}

// Wrapper function for creating Excel files
Future<String> createExcel(
    {required bool isKitty, List<dynamic>? transactions, int? eventId}) async {
  final KittyController kittyController = Get.put(KittyController());
  final ChamaController chamaController = Get.put(ChamaController());
  final DataController _dataController = Get.put(DataController());
  final ChamaDataController dataController = Get.put(ChamaDataController());
  final event = eventId != null
      ? Get.find<ViewSingleEventController>().event.value
      : null;
  
  if (isKitty) {
    transactions = kittyController.transactionsKitty.toList();
  } else {
    transactions = chamaController.chamaTransactions.toList();
  }

  final String path = (await getApplicationSupportDirectory()).path;
  
  // Prepare data for isolate
  final excelData = ExcelData(
    isKitty: isKitty,
    transactions: transactions,
    title: isKitty 
      ? _dataController.kitty.value.kitty?.title ?? ""
      : dataController.singleChamaDts.value.title ?? "",
    eventId: eventId,
    eventTitle: event?.title,
    outputPath: path,
  );
  
  // Process in isolate
  return await _processExcelInIsolate(excelData);
}

// Data class for user Excel
class UserExcelData {
  final List<dynamic> transactions;
  final String outputPath;

  UserExcelData({
    required this.transactions,
    required this.outputPath,
  });
}

// Process user Excel in isolate
Future<String> _processUserExcelInIsolate(UserExcelData data) async {
  return await compute(_createUserExcelInIsolate, data);
}

String _createUserExcelInIsolate(UserExcelData data) {
  final xlsio.Workbook workbook = xlsio.Workbook();
  final xlsio.Worksheet sheet = workbook.worksheets[0];

  final xlsio.Range titleRange = sheet.getRangeByName('A1:E1');
  titleRange.merge();
  titleRange.setText('Transaction Report');

  titleRange.cellStyle.bold = true;
  titleRange.cellStyle.fontSize = 14;
  titleRange.cellStyle.hAlign = xlsio.HAlignType.center;

  sheet.getRangeByName('A2').setText('Date');
  sheet.getRangeByName('B2').setText('Product');
  sheet.getRangeByName('C2').setText('Amount');
  sheet.getRangeByName('D2').setText('Reference');
  sheet.getRangeByName('E2').setText('Type');

  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  DateTime recent = data.transactions.first.createdAt!;
  DateTime nrecent = data.transactions.first.createdAt!;

  for (var transaction in data.transactions) {
    if (transaction.createdAt!.isAfter(recent)) {
      recent = transaction.createdAt!;
    }
    if (transaction.createdAt!.isBefore(nrecent)) {
      nrecent = transaction.createdAt!;
    }
  }
  
  for (int i = 0; i < data.transactions.length; i++) {
    sheet
        .getRangeByName('A${i + 3}')
        .setText(dateformat.format(data.transactions[i].createdAt!.toLocal()));

    sheet.getRangeByName('B${i + 3}').setText(data.transactions[i].product);

    sheet
        .getRangeByName('C${i + 3}')
        .setText(data.transactions[i].amount.toString());
    sheet
        .getRangeByName('D${i + 3}')
        .setText(data.transactions[i].transactionCode.toString());
    sheet.getRangeByName('E${i + 3}').setText(data.transactions[i].typeInOut);
  }
  
  for (int col = 1; col <= 5; col++) {
    sheet.autoFitColumn(col);
  }
  
  sheet.protect('@OneKitty');

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final String filename =
      '${data.outputPath}/Transactions_${DateFormat('MMM dd').format(nrecent.toLocal())}_${DateFormat('MMM dd').format(recent.toLocal())}.xlsx';
  
  // Clean the filename to remove invalid characters
  final cleanFilename = filename.replaceAll(RegExp(r'[*?:"<>|]'), '_');
  final File file = File(cleanFilename);
  file.writeAsBytesSync(bytes, flush: true);

  return cleanFilename;
}

Future<String> userExcel({List<dynamic>? transactions}) async {
  final UserKittyController userController = Get.put(UserKittyController());
  transactions = userController.alltransactions.toList();
  
  final String path = (await getApplicationSupportDirectory()).path;
  
  // Prepare data for isolate
  final excelData = UserExcelData(
    transactions: transactions,
    outputPath: path,
  );
  
  // Process in isolate
  return await _processUserExcelInIsolate(excelData);
}
