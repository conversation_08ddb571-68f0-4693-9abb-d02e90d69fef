import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;

import '../../controllers/transaction_controller.dart';
import '../../models/transaction_type.dart';

class TransactionExcelData {
  final List<Map<String, dynamic>> transactions;
  final String title;
  final String outputPath;

  TransactionExcelData({
    required this.transactions,
    required this.title,
    required this.outputPath,
  });
}

Future<String> _processTransactionExcelInIsolate(TransactionExcelData data) async {
  return await compute(_createTransactionExcelInIsolate, data);
}

String _createTransactionExcelInIsolate(TransactionExcelData data) {
  final xlsio.Workbook workbook = xlsio.Workbook();
  final xlsio.Worksheet sheet = workbook.worksheets[0];
  
  final xlsio.Range titleRange = sheet.getRangeByName('A1:F1');
  titleRange.merge();
  titleRange.setText('${data.title} ${'transaction_report'.tr}');
  titleRange.cellStyle.bold = true;
  titleRange.cellStyle.fontSize = 14;
  titleRange.cellStyle.hAlign = xlsio.HAlignType.center;

  // Headers
  sheet.getRangeByName('A2').setText('date'.tr);
  sheet.getRangeByName('B2').setText('reference'.tr);
  sheet.getRangeByName('C2').setText('name'.tr);
  sheet.getRangeByName('D2').setText('phone_number'.tr);
  sheet.getRangeByName('E2').setText('amount_label'.tr);
  sheet.getRangeByName('F2').setText('type'.tr);

  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  DateTime? recent;
  DateTime? earliest;

  for (var transaction in data.transactions) {
    final dateStr = transaction['createdAt'] as String?;
    if (dateStr != null) {
      try {
        final date = DateTime.parse(dateStr);
        if (recent == null || date.isAfter(recent)) recent = date;
        if (earliest == null || date.isBefore(earliest)) earliest = date;
      } catch (e) {
        // Skip invalid dates
      }
    }
  }
  
  for (int i = 0; i < data.transactions.length; i++) {
    final transaction = data.transactions[i];
    final row = i + 3;
    
    // Date
    final dateStr = transaction['createdAt'] as String?;
    if (dateStr != null) {
      try {
        final date = DateTime.parse(dateStr);
        sheet.getRangeByName('A$row').setText(dateformat.format(date.toLocal()));
      } catch (e) {
        sheet.getRangeByName('A$row').setText('n_a'.tr);
      }
    } else {
      sheet.getRangeByName('A$row').setText('n_a'.tr);
    }
    
    // Reference
    final ref = transaction['transactionCode'] ?? transaction['transactionRef'] ?? 'n_a'.tr;
    sheet.getRangeByName('B$row').setText(ref.toString());
    
    // Name
    final firstName = transaction['firstName'] ?? '';
    final secondName = transaction['secondName'] ?? '';
    final fullName = '$firstName $secondName'.trim();
    sheet.getRangeByName('C$row').setText(fullName.isNotEmpty ? fullName : 'n_a'.tr);
    
    // Phone
    final phone = transaction['phoneNumber'] ?? '';
    sheet.getRangeByName('D$row').setText(phone.toString());
    
    // Amount
    final amount = transaction['amount'] ?? 0;
    sheet.getRangeByName('E$row').setNumber(double.tryParse(amount.toString()) ?? 0);
    
    // Type
    final type = transaction['typeInOut'] ?? transaction['transactionType'] ?? 'n_a'.tr;
    sheet.getRangeByName('F$row').setText(type.toString());
  }
  
  // Auto-fit columns
  for (int col = 1; col <= 6; col++) {
    sheet.autoFitColumn(col);
  }
  
  sheet.protect('@OneKitty');

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  try {
    final dateRange = earliest != null && recent != null
        ? '${DateFormat('MMM_dd').format(earliest.toLocal())}_${DateFormat('MMM_dd').format(recent.toLocal())}'
        : DateFormat('MMM_dd').format(DateTime.now());
        
    final filename = '${data.outputPath}/${data.title.replaceAll(' ', '_')}_${dateRange}_Transactions.xlsx';
    
    final cleanFilename = filename.replaceAll(RegExp(r'[*?:\"<>|]'), '_');
    final File file = File(cleanFilename);
    
    file.writeAsBytesSync(bytes, flush: true);
    
    return cleanFilename;
  } catch (e) {
    print('Error creating Excel file: $e');
    rethrow;
  }
}

Future<String> createTransactionExcel({
  required TransactionType transactionType,
  required String controllerTag,
  String? entityTitle, 
}) async {
  final controller = Get.find<TransactionController>(tag: controllerTag);
   var transactions = transactionType == TransactionType.kitty ? Get.find<KittyController>().transactionsKitty.value : controller.transactions;
  
  if (transactions.isEmpty) {
    throw Exception('no_transactions_to_export'.tr);
  }

  final String path = (await getApplicationSupportDirectory()).path;
  final title = entityTitle ?? transactionType.displayName;
  
  // Convert TransactionModel to Map for isolate
  final transactionMaps = transactions.map((t) => {
    'createdAt': t.createdAt?.toIso8601String(),
    'transactionCode': t.transactionCode,
    'transactionRef': t.transactionRef,
    'firstName': t.firstName,
    'secondName': t.secondName,
    'phoneNumber': t.phoneNumber,
    'amount': t.amount,
    'typeInOut': t.typeInOut,
    'transactionType': t.transactionType,
  }).toList();
  
  final excelData = TransactionExcelData(
    transactions:   transactionMaps,
    title: title,
    outputPath: path,
  );
  
  return await _processTransactionExcelInIsolate(excelData);
}