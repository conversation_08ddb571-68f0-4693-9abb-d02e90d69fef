import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/helpers/show_toast.dart';

import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/models/bulk_sms/sms_groups.dart';

/// Helper class for standardizing navigation and cleanup after CRUD operations
class CrudNavigationHelper {
  /// Clears a list of text controllers
  static void clearTextControllers(List<TextEditingController> controllers) {
    for (final controller in controllers) {
      controller.clear();
    }
  }

  /// Clears a single text controller
  static void clearTextController(TextEditingController controller) {
    controller.clear();
  }

  /// Clears multiple text controllers from a map
  static void clearTextControllersMap(Map<String, TextEditingController> controllers) {
    for (final controller in controllers.values) {
      controller.clear();
    }
  }

  /// Standard navigation after successful CRUD operation
  /// [context] - BuildContext for navigation
  /// [operation] - Type of operation (create, edit, delete)
  /// [entityType] - Type of entity (event, kitty, group, ticket)
  /// [successMessage] - Optional success message to show
  /// [navigateToHome] - Whether to navigate to home instead of going back
  /// [specificRoute] - Specific route to navigate to instead of default behavior
  static void handleSuccessfulCrudOperation({
    required BuildContext context,
    required String operation,
    required String entityType,
    String? successMessage,
    bool navigateToHome = false,
    String? specificRoute,
    List<TextEditingController>? controllersToClean,
  }) {
    // Clear text controllers if provided
    if (controllersToClean != null) {
      clearTextControllers(controllersToClean);
    }

    // Show success message
    if (successMessage != null) {
      ToastUtils.showSuccessToast(context, successMessage, 'Success');
    }

    // Handle navigation
    if (specificRoute != null) {
      Get.offNamed(specificRoute);
    } else if (navigateToHome) {
      Get.offAllNamed(NavRoutes.bottomNavSection);
    } else {
      // Default behavior: go back if possible, otherwise go to home
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      } else {
        Get.offAllNamed(NavRoutes.bottomNavSection);
      }
    }
  }

  /// Handle failed CRUD operation
  static void handleFailedCrudOperation({
    required BuildContext context,
    required String operation,
    required String entityType,
    String? errorMessage,
  }) {
    final defaultMessage = 'Failed to $operation $entityType. Please try again.';

    ToastUtils.showErrorToast(
      context,
      errorMessage ?? defaultMessage,
      'Error'
    );
  }

  /// Show confirmation dialog before discarding changes
  static Future<bool> showDiscardChangesDialog({
    required BuildContext context,
    String? customTitle,
    String? customMessage,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(customTitle ?? 'Discard Changes?'),
          content: Text(
            customMessage ??
            'You have unsaved changes. Are you sure you want to discard them?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Discard'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Reset form state
  static void resetFormState(GlobalKey<FormState>? formKey) {
    if (formKey != null) {
      formKey.currentState?.reset();
    }
  }

  /// Navigation patterns for specific entity types
  static void handleEventCrudSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    handleSuccessfulCrudOperation(
      context: context,
      operation: operation,
      entityType: 'event',
      successMessage: 'Event ${operation}d successfully',
      controllersToClean: controllers,
      specificRoute: operation == 'create' ? NavRoutes.bottomNavSection : null,
    );
  }

  static void handleKittyCrudSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    handleSuccessfulCrudOperation(
      context: context,
      operation: operation,
      entityType: 'kitty',
      successMessage: 'Kitty ${operation}d successfully',
      controllersToClean: controllers,
      specificRoute: operation == 'create' ? NavRoutes.myKittiescontribtionScreen : NavRoutes.viewingsinglekittyScreen,
    );
  }

  static void handleGroupCrudSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    handleSuccessfulCrudOperation(
      context: context,
      operation: operation,
      entityType: 'group',
      successMessage: 'Group ${operation}d successfully',
      controllersToClean: controllers,
      navigateToHome: operation == 'create',
    );
  }

  static void handleTicketCrudSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    handleSuccessfulCrudOperation(
      context: context,
      operation: operation,
      entityType: 'ticket',
      successMessage: 'Ticket ${operation}d successfully',
      controllersToClean: controllers,
    );
  }

  static void handleOperatorCrudSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    handleSuccessfulCrudOperation(
      context: context,
      operation: operation,
      entityType: 'operator',
      successMessage: 'Operator ${operation}d successfully',
      controllersToClean: controllers,
    );
  }

  static void handleSignatoryCrudSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    handleSuccessfulCrudOperation(
      context: context,
      operation: operation,
      entityType: 'signatory',
      successMessage: 'Signatory ${operation}d successfully',
      controllersToClean: controllers,
    );
  }

  static void handleChamaCrudSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    handleSuccessfulCrudOperation(
      context: context,
      operation: operation,
      entityType: 'chama',
      successMessage: 'Chama ${operation}d successfully',
      controllersToClean: controllers,
      specificRoute: operation == 'create' ? NavRoutes.bottomNavSection : null,
    );
  }

  /// Unified contact selection method that combines best practices from all implementations
  /// [selectedContact] - The contact to select
  /// [context] - BuildContext for showing toasts
  /// [selectedContacts] - Set of currently selected contacts
  /// [isGroup] - Whether this is for group creation (optional)
  /// [groupMembers] - List of group members for group creation (optional)
  /// [onStateChanged] - Callback to trigger UI updates
  /// [isMounted] - Function to check if widget is still mounted (optional)
  static void selectContact({
    required Contact selectedContact,
    required BuildContext context,
    required Set<Contact> selectedContacts,
    bool isGroup = false,
    List<SmsGroupMember>? groupMembers,
    required VoidCallback onStateChanged,
    bool Function()? isMounted,
  }) {
    // Safety check - ensure widget is still mounted
    if (isMounted != null && !isMounted()) return;

    if (!selectedContacts.contains(selectedContact)) {
      selectedContact.name.prefix = "MEMBER";

      if (isGroup && groupMembers != null) {
        // For group creation - add to group members
        groupMembers.add(SmsGroupMember(
          phoneNumber: selectedContact.phones.first.number,
          firstName: selectedContact.name.first,
          secondName: selectedContact.name.last,
        ));
      } else {
        // For individual contact selection
        selectedContacts.add(selectedContact);
        Get.put(BulkSMSController()).selectedContacts.add(SelectedContact(
          contact: selectedContact,
          type: 'contact',
        ));
      }

      onStateChanged();
    } else {
      ToastUtils.showInfoToast(context, "Contact Already Selected", "Check");
    }
  }
}
